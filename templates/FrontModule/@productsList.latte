{php
  $proIds = array();
}
{* <!-- výpis produktů start --> *}
<section role="region">

  {*
  {if !empty($title)}
  <h2 class="category__header">{$title}</h2>
  {/if}
  *}

  {foreach $products as $row}
    {php
    $proIds[] = $row->proid;
    $GLOBALS["ecommTotalValue"] += $row->proprice;
    }
    {if $iterator->isFirst()}
      <div class="row row--autoclear row--flex">
    {/if}

    {* jedna položka *}
    {include @productItem.latte, product => $row}

    {if $iterator->isLast()}
      </div>
    {/if}
  {/foreach}

  {if count($products) == 0}<p>Nejsou zde žádné výrobky</p>{/if}

</section>
{* <!-- výpis produktů end --> *}

{php
  $GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
  }
