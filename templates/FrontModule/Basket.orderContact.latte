{default $pageTitle='Dodac<PERSON> údaje'}
{default $pageRobots = "nofollow,noindex"}

{block #content}

{/block}

{block #orderProgress}
<div class="order order--3">

  <div class="order-progress">

    <div class="container-fluid">

      <div class="order-progress__item"><a href="{plink default}">{_'Nákupní k<PERSON>'}</a></div>
      <div class="order-progress__item"><a href="{plink orderDelMode}">{_'Doprava a platba'}</a></div>
      <div class="order-progress__item is-active">{_'Dodací údaje'}</div>
      <div class="order-progress__item">{_'Souhrn objednávky'}</div>

    </div>

  </div>

  {form orderContactForm}
  {* vykresleni chyb pokud ma vypnuty JS *}

  <div class="container-fluid">
  <div class="row">

    <div class="col-xs-12">

      <ul class="errors" n:if="$form->hasErrors()">
        <li n:foreach="$form->errors as $error">{$error}</li>
      </ul>

      <h2>{_'Fakturační a současně doručovací adresa'}</h2>

    </div>

  </div>

  <div class="row">

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordiname']->getLabel()->class('required') } <span class="order__star">*</span><br>
        {php echo $form['ordiname']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordilname']->getLabel()->class('required') } <span class="order__star">*</span><br>
        {php echo $form['ordilname']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordmail']->getLabel()->class('required') } <span class="order__star">*</span>
        {*
        <span class="order__help">
          Uvádějte platný emailový kontakt, na tuto adresu Vás budeme informovat o stavu vyřízení Vaší objednávky.
        </span>
        *}
        <br>
        {php echo $form['ordmail']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordtel']->getLabel()->class('required') } <span class="order__star">*</span>
        {*
        <span class="order__help">
            Telefonní kontakt je nutný pro ověření objednávky před odesláním. Urychlí komunikaci i v případě nečekaných nejasností při odbavování Vaší objednávky a může pomoci urychlit doručení zásilky. Prosím uveďte číslo, na kterém budete spolehlivě k zastižení.
        </span>
        *}
        <br>
        {php echo $form['ordtel']->control }
      </p>
    </div>

  </div>

  <div class="row">

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordistreet']->getLabel()->class('required') } <span class="order__star">*</span><br>
        {php echo $form['ordistreet']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordistreetno']->getLabel()->class('required') } <span class="order__star">*</span><br>
        {php echo $form['ordistreetno']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordicity']->getLabel()->class('required') } <span class="order__star">*</span><br>
        {php echo $form['ordicity']->control }<br><small>Povoleno maximálně 30 znaků</small>
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordipostcode']->getLabel()->class('required') } <span class="order__star">*</span><br>
        {php echo $form['ordipostcode']->control }
      </p>
    </div>

  </div>

  <div class="row">

    <div class="col-xs-12">

      <p class="order__option">{php echo $form['onfirm']->control } {php echo $form['onfirm']->getLabel() }</p>

    </div>

  </div>

  <div id="order-company">

      <div class="col-xs-12 col-sm-3">
        <p>
          {php echo $form['ordifirname']->getLabel() } <br>
          {php echo $form['ordifirname']->control }
        </p>
      </div>

      <div class="col-xs-12 col-sm-3">
        <p>
          {php echo $form['ordic']->getLabel() }<br>
          {php echo $form['ordic']->control }
        </p>
      </div>

      <div class="col-xs-12 col-sm-3">
        <p>
          {php echo $form['orddic']->getLabel() }<br>
          {php echo $form['orddic']->control }
        </p>
      </div>

  </div>

  <div class="row">

    <div class="col-xs-12">

      <p class="order__option">{php echo $form['shipto']->control } {php echo $form['shipto']->getLabel() }</p>

    </div>

  </div>

  <div id="invoiceAddress">
  <div class="row">

    <div class="col-xs-12 col-sm-3">
      <p>
        {label ordstname /}<br>
        {php echo $form['ordstname']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordstlname']->getLabel() } <br>
        {php echo $form['ordstlname']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordstfirname']->getLabel() } <br>
        {php echo $form['ordstfirname']->control }
      </p>
    </div>

  </div>

  <div class="row">

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordststreet']->getLabel() } <br>
        {php echo $form['ordststreet']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordststreetno']->getLabel() } <br>
        {php echo $form['ordststreetno']->control }
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordstcity']->getLabel() } <br>
        {php echo $form['ordstcity']->control }<br><small>Povoleno maximálně 30 znaků</small>
      </p>
    </div>

    <div class="col-xs-12 col-sm-3">
      <p>
        {php echo $form['ordstpostcode']->getLabel() } <br>
        {php echo $form['ordstpostcode']->control }
      </p>
    </div>

  </div>
  </div>

  <div class="row">

    <div class="col-xs-12 col-sm-6">

      <label class="order__note">Poznámka: <em>Máte nějaké další přání? Napište nám ho do poznámky</em>.</label>
      {php echo $form['ordnote']->control->cols(60)->rows(5) }

    </div>

    {if !$identity->isLoggedIn()}
    <div class="col-xs-12 antispam" data-help="{$presenter->config["ANTISPAM_NO"]|noescape}">
      {php echo $form['antispam']->getLabel() }<br>
      {php echo $form['antispam']->control }
    </div>
    {/if}

  </div>

  <div class="row order-controls">

    <div class="col-xs-12 col-sm-6">
      <a href="{plink 'orderDelMode'}" class="btn btn--big btn--back"><i class="icon icon--arrow-left"></i> {_'Zpět k dopravě a platbě'}</a>
    </div>

    <div class="col-xs-12 col-sm-6">
      <button type="submit" id="frm-orderContactForm-submit" name="_submit" class="btn--big btn--buy">{_'Souhrn a objednávka'} <i class="icon icon--arrow-right"></i></button>
    </div>

  </div>

  </div>
  {/form orderContactForm}

</div>

{/block}
