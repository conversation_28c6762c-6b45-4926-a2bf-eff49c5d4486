{$pageTitle  = '<PERSON><PERSON><PERSON>'}
{$pageRobots = "nofollow,noindex"}

{php
$GLOBALS["ecommProId"] = '';
$GLOBALS["ecommPageType"] = 'cart';
$GLOBALS["ecommTotalValue"] = 0;
}

{block #content}

{/block}

{block #orderProgress}
{php
  $proIds = array();
}
{if $presenter->getParam('ok') != 1}
<div class="order order--1">

  {if count($basket->items) > 0}
    <div class="order-progress">

      <div class="container-fluid">

        <div class="order-progress__item is-active">{_'Nákupní koš<PERSON>'}</div>
        <div class="order-progress__item">{_'Doprava a platba'}</div>
        <div class="order-progress__item">{_'Dodací údaje'}</div>
        <div class="order-progress__item">{_'Souhrn objednávky'}</div>

      </div>

    </div>

    {form basketForm}
    <div class="container-fluid">
    <div class="row order-content">

      <div class="col-xs-12">

        {php $form->render('errors') }

        <h2>Produkty v košíku</h2>

        <table class="order__table" cellpadding="3" cellspacing="0" border="1">
        <tbody>
        {foreach $basket->items as $id=>$value}
          {php
            $proIds[] = $productRows[$id]->proid;
            $GLOBALS["ecommTotalValue"] += ($productRows[$id]->proprice * (int)$basket->items[$id]);
          }
          <tr>
            <td class="order__product">
              <a href="{plink Product:detail, $productRows[$id]->proid, ($productRows[$id]|getProKey)}">
                <img src="{$baseUri}/{($productRows[$id]|getProductPicNameMaster:'list')|noescape}" alt="{$productRows[$id]->proname}">
              </a>
              <a href="{plink Product:detail, $productRows[$id]->proid, ($productRows[$id]|getProKey)}">
              {$productRows[$id]->proname}
              </a>
              {*nezahrnuje se do slevy*}
              {if $productRows[$id]->pronotdisc == 1}<span class="order__star">*</span>{/if}
              {if !empty($productRows[$id]->prodiscountinfo)}<br><br>{$productRows[$id]->prodiscountinfo}<br>{/if}
            </td>
            <td class="order__count">
              <span class="control--count">
                {php echo $form['count_'.$productRows[$id]->proid]->getControl() }
              </span>
              {*if (int)$productRows[$id]->proqty > 0 && (int)$productRows[$id]->proqty < (int)$basket->items[$id]}<br />Pouze {$productRows[$id]->proqty}ks{/if*}
              <a href="{plink Basket:delete, $productRows[$id]->proid}" class="control control--remove" onclick="return DeleteConfirmFront('{_'Opravdu chcete smazat položku'} {$productRows[$id]->proname|noescape}');" title="{_'Odstranit z nákupního košíku'}"><i class="icon icon--close"></i></a>
            </td>
            <td class="order__price"style="max-width: 100px">
              {($productRows[$id]->proprice*$value)|formatPrice}
            </td>
          </tr>
        {/foreach}
        </tbody>
        <tfoot>
          <tr class="order__price-sum">
            <td>{_'Cena s DPH'}</td>
            <td colspan="3" class="order__price">{$basket->priceSumVat|formatPrice}</td>
          </tr>
          {if !empty($basket->coupon)}
          <tr class="order__discount">
            <td class="order__product"><strong>{_'Slevový kupón'} {$basket->coupon->coucode}</strong></td>
            <td colspan="3">
              {if ((int)$basket->coupon->couvalue == 0)}
                Získáváte:<br>
                <ul>
                  {if $basket->coupon->coupayfree == 1}<li>Platba zdarma</li>{/if}
                  {if $basket->coupon->coudelfree == 1}<li>Doprava zdarma</li>{/if}
                  {foreach $basket->coupon->products as $product}
                    <li>Sleva {$product["prodiscount"]|formatPrice} na {$product["proname"]} (původní cena: {$product["proprice"]|formatPrice})
                      {if $product["used"] == FALSE}<a href="{plink add $product["proid"]}">vložit do košíku a využít slevu</a>{/if}
                    </li>
                  {/foreach}
                </ul>
              {/if}
            </td>
            <td><a href="{plink Basket:deleteCoupon}" class="control control--remove" onclick="return DeleteConfirmFront('Opravdu chcete smazat slevový kupón');" title="Odstranit slevový kupón z nákupního košíku">x</a></td>
          </tr>
          {/if}

          {if $basket->discountVal > 0}
          <tr class="order__discount">
            <td><strong>{_'Sleva'} {if $basket->discountVal > 0 && $basket->discountPer > 0}{$basket->discountPer}%{/if}</strong></td>
            <td colspan="3" class="order__price"><strong>{if $basket->discountVal > 0}{$basket->discountVal|formatPrice}{else}bez slevy{/if}</strong></td>
          </tr>
          {/if}
        </tfoot>
        </table>

        {ifset $form['coupon']}
          <p class="order__coupon">
            Slevový kupón: {php echo $form['coupon']->getControl()->class('input--25')  } {php echo $form['recalc']->getControl()->class('btn')  }
          </p>
        {/ifset}

      </div>

    </div>
    </div>

    <div class="order__price-final">
    <div class="container-fluid">
      {_'CENA CELKEM'} (včetně DPH)
      <strong>{($basket->priceSumVat - $basket->discountVal)|formatPrice}</strong>
    </div>
    </div>

    <div class="container-fluid">
    <div class="row order-controls">

      <div class="col-xs-12 col-sm-4">
       <a href="{$baseUri}" class="btn btn--big btn--back"><i class="icon icon--arrow-left"></i> {_'Pokračovat v nákupu'}</a>
      </div>

      <div class="col-xs-12 col-sm-8">
        <button id="frm-basketForm-recalc" name="recalc" class="btn--big btn--back">{_'Přepočítat'}</button>
        <button id="frm-basketForm-makeorder" name="makeorder" class="btn--big btn--buy">{_'Pokračovat k dopravě a platbě'} <i class="icon icon--arrow-right"></i></button>
      </div>

    </div>

    <div class="row order-info">

      <div class="col-xs-12">

        <p>Akční zboží označené červenou hvězdičkou se do slev <strong>nezapočitává</strong>.</p>

        {if $nextDisc}
          <p class="order-info__item order-info--discount">
            Získáte <strong>slevu {$nextDisc->dispercent}%</strong> pokud objednáte ještě minimálně za <strong>{$nextDisc->diff|formatPrice}</strong>.
          </p>
        {/if}

        {if isset($nextDelsFree)}
        {foreach $nextDelsFree as $key => $dels}
          <p class="order-info__item order-info--delivery">
            {foreach $dels as $key => $row}{$row->delname}{if !$iterator->isLast()}, {/if}{/foreach} zdarma, objednáte-li ještě zboží za {$row->diff|formatPrice}.
          </p>
        {/foreach}

        {/if}

        {if $basket->weightSum > $presenter->config["WEIGHT_LIMIT"]}
          <p class="order-info__item order-info--weight">
            <strong>Vaše objednávka přesahuje váhový limit {$presenter->config["WEIGHT_LIMIT"]}Kg</strong>, pro odeslání běžnou přepravní službou. Cena za dopravu bude stanovena po odeslání objednávky na základě <a href="{plink Page:detail 'doprava-platba'}">platného ceníku pro dodávku zboží</a>.
          </p>
        {/if}

        {if !empty($textBlocks["basket_add"])}
          <div class="order-info__item order-info--text">
            {$textBlocks["basket_add"]|noescape}
          </div>
        {/if}

      </div>

    </div>
    </div>

    {/form}

  {else}
    <div class="container-fluid">
    <div class="row order-content">

      <div class="col-xs-12">
    <p class="order__empty">{_'Košík je prázdný'}...</p>
      </div>
      </div>
      </div>
  {/if}

  {else}

  {/if}

</div>

  {php
  $GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
  }
  <script>
    fbq('track', 'AddToCart', {
      content_ids: {$GLOBALS["ecommProId"]},
      content_type: 'product_group',
      value: {$GLOBALS["ecommTotalValue"]},
      currency: {$curKey}
    });
  </script>
{/block}
