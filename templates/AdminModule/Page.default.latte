{var $title = 'Textové stránky'}

{block #content}
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Název</th>
    <th>URL klíč</th>
    <th>Typ</th>
    <th></th>
    <th></th>
  </tr>
  {foreach $dataRows as $row}
    <tr>
      <td>{$row->pagname}</td>
      <td>{$row->pagurlkey}</td>
      <td>{$enum_pagtypid[$row->pagtypid]}</td>
      <td><a href="{plink Page:edit, $row->pagid}">{('edit'|glyph)|noescape}</a></td>
      <td><a href="{plink :Front:Page:detail, $row->pagid, $row->pagurlkey}">{('front'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>

  <h4><PERSON><PERSON><PERSON><PERSON> systémov<PERSON>ch souborů (VOP, ...)</h4>
  {control uploadSystemFileForm}
  <h4>Aktuálně nahrané systémové soubory</h4>
  <ul>
  {foreach $uploadedSystemFiles as $url => $name}
    <li><a href="{$baseUrl}{$url}" target="_blank">{$name}</a></li>
  {/foreach}
  </ul>
{/block}
