<?php
/*
	Hosting90 s.r.o. - knihovna implementace SMS brany
	SMSlib 0.3

	* Script vyzaduje minimalni verzi php 5.1.
	* Prenos znaku narodnich abeced probiha v kodovani UTF-8.
*/
	
	class sms {
		function sms($uid, $password) {
			$this->uid = $uid;
			$this->password = $password;
			return $this;
		}
		
		private function rpc($string) {
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, 'http://sms.hosting90.cz/?uid='.$this->uid.'&password='.$this->password.'&'.$string);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_TIMEOUT, 10);
			$return = curl_exec($ch);
			if ($return === FALSE) return false;
			try {
				$replay = @(new SimpleXMLElement($return));
			} catch (Exception $e) {
				return false;
			}
			curl_close($ch);
			$this->return = $replay;
			return $replay;
		}
		
		function error() {
			return @$this->return['text'];
		}
		
		function state() {
			return @$this->return['state'];
		}
		
		function kredit() {
			$return = $this->rpc('action=info');
			if (!$return) return false;
			return (int) $return->info->sms_kredit;
		}
		
		function archiv() {
			$return = $this->rpc('action=archiv');
			if (!$return) return false;
			$ret = array();
			foreach ($return->archiv->sms AS $sms) {
				$return=array();
				foreach ($sms->attributes() AS $key=>$value) {
					$return[(string)$key]=(string)$value;
				}
				$ret[]=$return;
			}
			return $ret;
		}

		function send($number, $text, $react_email, $msg_num=1, $delivery_notification="") {
			$return = $this->rpc(
				"number=".urlencode($number).
				"&text=".urlencode($text).
				"&max_length=".urlencode($msg_num).
				"&react_email=".urlencode($react_email).
				"&delivery_notification=".urlencode($delivery_notification)
			);
			if (!$return || $return['state']!="ok") return false;
			return true;
		}
	}
	
	function sms($uid, $password) {
		return new sms($uid, $password);
	}
?>
