<?php
namespace Model;
use <PERSON><PERSON>, dibi;
                
class MailingsModel extends BaseModel {
  protected $tableName = "mailings";
  protected $fieldPrefix = "mam";
  
  public $usersTable = "users"; 
  
  /**
  * ciselnik admstatus
  * @return array
  */
  public function getEnumMamStatus() {
    return array(
      0 => 'Vytvořený',
      1 => 'Připravený k odeslání',
      2 => 'Odesílá se',
      4 => 'Pozastavený',
      3 => 'Odmailovaný',
    );
  }
}
?>