<?php

/**
 * postaveno nad knihovnou
 * https://github.com/ondrakoupil/csob
 */

use Ondra<PERSON>oupil\Csob\Config;
use Ondra<PERSON>oupil\Csob\GatewayUrl;
use Ondra<PERSON>oupil\Csob\Client;
use OndraKoupil\Csob\Payment;
use OndraKoupil\Csob\Extensions\EET\EETData;
use OndraKoupil\Csob\Extensions\EET\EETInitExtension;


class CsobPayment {
  private $csobConfig;
  private $config;
  private $useEet = FALSE;
  public $paymentUrl = "";
  public $lastError = "";


  public function __construct($config) {

    $this->config = $config;
    $this->useEet = false;
    //die ($this->config["isProduction"]);
    if ((bool)$this->config["isProduction"]) {
      $bankPublicKeyFilename = "mips_platebnibrana.csob.cz.pub";
      $gatewayUrl = GatewayUrl::PRODUCTION_LATEST;
    } else {
      $bankPublicKeyFilename = "mips_iplatebnibrana.csob.cz.pub";
      $gatewayUrl = GatewayUrl::TEST_LATEST;
    }
    $this->csobConfig = new Config(
      $this->config["merchantId"],
      __DIR__ . '/keys/rsa_'.$this->config["merchantId"].'.key',
      __DIR__ . '/keys/'.$bankPublicKeyFilename,
      $this->config["shopName"],
      '',
      $gatewayUrl
    );
  }

  Public function connectionTest() {
    try {
      $client = new Client($this->csobConfig);
      $retGet = $client->testGetConnection();
      $retPost = $client->testPostConnection();
    } catch (Exception $e) {
      echo "Something went wrong: " . $e->getMessage();
    }
  }

  private function getClient() {

    $client = new Client($this->csobConfig);
    //$client->setLog(__DIR__.'/logs/log.txt');
    //if ($this->config["isProduction"]) $client->setTraceLog(__DIR__.'/logs/traceLog.txt');
    return $client;

  }

  public function paymentInit($order, $orditems, $vatLevels, $returnUrl) {
    $this->csobConfig->returnUrl = $returnUrl;
    $client = $this->getClient();

    $payment = new Payment($order->ordcode);
    $sumOrdItems = 0;

    //init watlevels
    $vatInfo = array();
    foreach ($vatLevels as $key => $info) {
      $vatInfo[$key] = array(
        'pricenovat' => 0,
        'vat' => 0,
      );
    }

    //if (count($orditems) > 2) {
    $price = round($order->ordpricevat) * 100;
    $payment->addCartItem('za všechny položky', 1, (int)$price);
    /*
    } else {
      foreach ($orditems as $row) {
        $priceQty = $row->oriprice * $row->oriqty;
        $sumOrdItems += $priceQty;
        $price = round($priceQty) * 100;
        $payment->addCartItem($row->oriname, $row->oriqty, (int)$price);
      }
    }
    */

    try {

      if ($this->useEet) {
        foreach ($orditems as $row) {
          $priceQty = $row->oriprice * $row->oriqty;
          $sumOrdItems += $priceQty;

          //spočítám vat
          if (isset($vatInfo[$row->orivatid])) {
            $vatInfo[$row->orivatid]["pricenovat"] += round($priceQty / (1 + ($vatLevels[$row->orivatid]/100)), 2);
            $vatInfo[$row->orivatid]["vat"] += $priceQty - $vatInfo[$row->orivatid]["pricenovat"];
          }
        }

        // Vytvoříme data pro EET. Jen parametry v konstruktoru jsou povinné.
        $eetData = new EETData($this->config["eetPremiseId"], $this->config["eetCashRegisterId"], $sumOrdItems);
        //Celkový základ daně se základní sazbou DPH
        if (!empty($vatInfo[0]["pricenovat"])) {
          $eetData->priceStandardVat = $vatInfo[1]["pricenovat"];
        }
        //Celková DPH se základní sazbou
        if (!empty($vatInfo[0]["vat"])) {
          $eetData->vatStandard = $vatInfo[1]["vat"];
        }
        //Celkový základ daně s první sníženou sazbou DPH
        if (!empty($vatInfo[1]["pricenovat"])) {
          $eetData->priceFirstReducedVat = $vatInfo[1]["pricenovat"];
        }
        //Celková DPH s první sníženou sazbou
        if (!empty($vatInfo[1]["vat"])) {
          $eetData->vatFirstReduced = $vatInfo[1]["vat"];
        }

        // Vytvoříme extension pro payment/init v ověřovacím režimu
        $extensionInit = new EETInitExtension($eetData, (boolean)$this->config["eetVerificationMode"]);
        $response = $client->paymentInit($payment, $extensionInit);
      } else {
        $response = $client->paymentInit($payment);
      }

      $payId = $payment->getPayId();

      //zaloguji
      $responseJson = json_encode($response);
      $this->log($payId, $order->ordid, $responseJson);
      $this->paymentUrl = $client->getPaymentProcessUrl($payment);
      return TRUE;

    } catch (\Exception $e) {
      $this->lastError = "inicializace platby se nezdařila. Důvod: " . (string)$e->getMessage();
      return FALSE;
    }
  }

  public function receiveResponse() {
    $client = $this->getClient();
    try {
      $response = $client->receiveReturningCustomer($_POST);
    } catch (\Exception $e) {
      $response = $e->getResponse();
    }
    return $response;
  }

  public function getPaymentInfo($payId) {
    $client = $this->getClient();
    if ($this->useEet) {
      $extensionStatus = new \OndraKoupil\Csob\Extensions\EET\EETStatusExtension();
      $status = $client->paymentStatus($payId, true, $extensionStatus);
      return array(
        "status" => $status,
        "eetStatus" => $extensionStatus->getEETStatus(),
        "eetData" => $extensionStatus->getReport()
      );
    }  else {
      return array(
        "status" => $client->paymentStatus($payId, true),
        "eetStatus" => "",
        "eetData" => ""
      );
    }
  }

  public function getPaymentStatus($payId) {
    $client = $this->getClient();
    return $client->paymentStatus($payId);
  }

  public function paymentReverse($payId) {
    $client = $this->getClient();
    $response = $client->paymentReverse($payId);
    return($response["resultCode"] == 0 && $response["paymentStatus"] == 5);
  }

  public function getPaymentStatusText($statusId) {
    $text = "";
    switch ($statusId) {
      case 1:
        $text = "Platba založena";
        break;
      case 2:
        $text = "Platba probíhá";
        break;
      case 3:
        $text = "Platba zrušena";
        break;
      case 4:
        $text = "Platba potvrzena";
        break;
      case 5:
        $text = "Platba odvolána";
        break;
      case 6:
        $text = "Platba zamítnuta";
        break;
      case 7:
        $text = "Čeká na zúčtování";
        break;
      case 8:
        $text = "Platba zúčtována";
        break;
      case 9:
        $text = "Zpracování vrácení";
        break;
      case 10:
        $text = "Platba vrácena";
        break;
      default:

    }
    return $text;
  }



  private function log($payid, $ordid, $responseJson) {
    return dibi::insert('csob_log', array(
      'payid'=>$payid,
      'payordid'=>$ordid,
      'payresponse'=>$responseJson,
      'paydatec'=>new \DateTime,
    ))->execute();
  }



}
