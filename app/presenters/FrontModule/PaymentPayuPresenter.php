<?php
/**
* /payment/payu-negative?pos_id=%posId%&session_id=%sessionId%&error=%error%
* /payment/payu-positive?pos_id=%posId%&session_id=%sessionId%
* /payment/payu-response
* Testovací platební metoda se automaticky deaktivuje v případě, že přes ni nebyla v posledních
* třech dnech provedena žádná testovací transakce. Testovací platební metodu lze kdykoliv
* libovolně zapínat a vypínat v administraci Vašeho PayU účtu v nastavení POS: Můj obchod –
* daný obchod – Seznam POS – daný POS – tabulka dole: Dostupné typy plateb; klikněte u typu
* Testovací platba v kolonce Status na Zapnout.
*/


namespace FrontModule;
use dibi;
use Nette;

final class PaymentPayuPresenter extends BasePresenter {
  // adresy pro server PayU a metodu Payment/get
  // addresses for PayU server and the Payment/get method
  public $payuServer = "secure.payu.com";
  public $payuScript = "/paygw/UTF/Payment/get";
  public $payUPar = array();

  protected function startup() {
    parent::startup();
    $this->payUPar = $this->neonParameters["payU".$this->curId];
  }

  public function renderPayuPositive() {
    $ords=new \Model\OrdersModel();
    $session_id  = $_GET["session_id"];
    $arr = explode("-", $session_id);
    $ordid = (int)$arr[0];
    $str = "";
    if ($ordid > 0) {
      $ord = $ords->load($ordid);
      $this->template->order = $ord;
    }
    $this->template->params = $_GET;

  }

  public function renderPayuNegative() {
    $ords=new \Model\OrdersModel();
    $session_id  = $_GET["session_id"];
    $arr = explode("-", $session_id);
    $ordid = (int)$arr[0];
    $str = "";
    if ($ordid > 0) {
      $ord = $ords->load($ordid);
      $this->mailSend($this->config["SERVER_MAILORDERS"], "PayU platba - chyba", 'kod objednavky: '.$ord->ordcode." kod chyby:".$_GET["error"]."|".print_r($_GET));
      $this->template->order = $ord;
    }
    $this->template->params = $_GET;
  }

  public function renderPayuResponse() {
    // některé parametry chybějí
    // some parameters are missing
    if(!isset($_POST["pos_id"]) || !isset($_POST["session_id"]) || !isset($_POST["ts"]) || !isset($_POST["sig"]))
        die("ERROR: EMPTY PARAMETERS");

    // obdržené číslo POS ID je jiné, než bylo očekáváno
    // received POS ID is different than expected
    if($_POST["pos_id"] != $this->payUPar["posId"])
        die("ERROR: INCORRECT POS ID");

    // verifikace obdrženého podpisu
    // verification of received signature
    $sig = md5($_POST["pos_id"].$_POST["session_id"].$_POST["ts"].$this->payUPar["key2"]);

    // chybný podpis
    // incorrect signature
    if($_POST["sig"] != $sig)
        die("ERROR: INCORRECT SIGNATURE");

    // podpis, který bude odeslán do PayU spolu s požadavkem
    // signature that will be sent to PayU with request
    $ts = time();
    $sig = md5($this->payUPar["posId"].$_POST["session_id"].$ts.$this->payUPar["key1"]);

    // příprava řetězce (string) parametrů k odeslání do PayU
    // preparing parameters string to be sent to PayU
    $parameters = "pos_id=".$this->payUPar["posId"]."&session_id=".$_POST["session_id"]."&ts=".$ts."&sig=".$sig;

    // určení metodu spojení (socket nebo CURL)
    // determining connection method (socket or CURL)
    $fsocket = false;
    $curl = false;
    if((PHP_VERSION >= 4.3) && ($fp = @fsockopen("ssl://".$this->payuServer, 443, $errno, $errstr, 30)))
        $fsocket = true;
    elseif (function_exists("curl_exec"))
        $curl = true;

    // odesílání požadavku pomocí socket
    // sending request via socket
    if ($fsocket == true) {
        $header = "POST ".$this->payuScript." HTTP/1.0"."\r\n"."Host: ".$this->payuServer."\r\n".
            "Content-Type: application/x-www-form-urlencoded"."\r\n"."Content-Length: ".
            strlen($parameters)."\r\n"."Connection: close"."\r\n\r\n";
        @fputs($fp, $header.$parameters);
        $payu_response = "";
        while (!@feof($fp))
        {
            $res = @fgets($fp, 1024);
            $payu_response .= $res;
        }
        @fclose($fp);
    }

    // odesílání požadavku pomocí CURL
    // sending request via CURL
    elseif ($curl == true)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://".$this->payuServer.$this->payuScript);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $parameters);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $payu_response = curl_exec($ch);
        curl_close($ch);
    }

    // není k dispozici žádná použitelná metoda spojení
    // no connection method available
    else
        die("ERROR: No connect method ...\n");

    // získávání odpovědi od PayU
    // parsing PayU response
    $result = false;
    if (preg_match("/<trans>.*<pos_id>([0-9]*)<\/pos_id>.*<session_id>(.*)<\/session_id>.*<order_id>(.*)<\/order_id>.*".
        "<amount>([0-9]*)<\/amount>.*<status>([0-9]*)<\/status>.*<desc>(.*)<\/desc>.*<ts>([0-9]*)<\/ts>.*<sig>([a-z0-9]*)".
        "<\/sig>.*<\/trans>/is", $payu_response, $parts))
        $result = $this->getStatus($parts);

    // rozpoznaný status transakce
    // recognised status of transaction
    if ($result["code"]) {
        $pos_id = $parts[1];
        $session_id = $parts[2];
        $order_id = $parts[3];
        $amount = $parts[4];          // v haléřích (in hellers)
        $status = $parts[5];
        $desc = $parts[6];
        $ts = $parts[7];
        $sig = $parts[8];


        // změna statusu transakce v systému shopu
        if ($result["code"] == "99") {
          //nactu objednavku
          $ords = new \Model\OrdersModel();
          $ord = $ords->load($order_id, 'code');
          if($amount / 100 == $ord->ordpricevat && $ord->ordid > 0) {
              //nastavim zaplaceno
              if ($ord->ordpaystatus == 0) {
                $vals =  array(
                  'ordpaystatus' => 1,
                  'ordstatus' => 6,
                );
                $ords->update($ord->ordid, $vals);
                $ords->logStatus($ord->ordid, $vals["ordstatus"], $this->adminData->admid);
                //mailuju
                $mailTemplate = $this->createTemplate();
                $orderRow = $ords->load($ord->ordid);
                $mailTemplate->orderRow = $orderRow;

                $mailTemplate->enum_ordStatus = $ords->getEnumOrdStatus();
                $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderChanged.latte');

                //mailuju zakaznikovi a adminovi
                try {
                  $this->mailSend($orderRow->ordmail, $this->translator->translate("Změna stavu objednávky č.")." ".$orderRow->ordcode, $mailTemplate);
                  $this->mailSend($this->config["SERVER_MAILORDERS"], "Objednávka č. ".$orderRow->ordcode." uhrazena PayU", $mailTemplate);
                } catch (Nette\InvalidStateException $e) {
                  $this->flashMessage("Nepodařilo se odeslat informační email o změně stavu objednávky (".$e->getMessage().")".' [ID:'.$id.']', "danger");
                }
              }
              // platba je úspěšná, takže posíláme zpátky OK
              echo "OK";
              exit;
          }
          mailit('<EMAIL>', 'test plateb', 'nenastavil se status');
        }
        else if ($result["code"] == "2") {
            // transakce zrušena, můžeme rovněž transakci zrušit

        } else {
            // jiné akce

        }
            echo "OK";
            exit;

    } else {
        // správa plateb se statusem error
        // error transaction status managment
        echo "ERROR: Data error ....\n";
        echo "code=".$result["code"]." message=".$result["message"]."\n";
        echo $payu_response;
        // informace o změně statusu bude z payu.cz odeslána znovu, můžeme zapsat informaci do logů (logs)....
        // information about changing a status will be send again from PayU.cz, we can write information to logs....
    }
    $this->terminate();
  }

  /* vrací pole s indexy:
  *
  * "code" (číslo statusu transakce nebo false v případě chyby), "message" (popis statusu transakce nebo popis chyby)
  * returns array with values:
  * "code" (numerical transaction status or false in case of error), "message" (status description or error description)
  */
  private function getPayuStatus($parts) {
    // chybné číslo POS ID v odpovědi
    // incorrect POS ID number specified in response
    if($parts[1] != $this->payUPar["posId"])
        return array("code" => false, "message" => "incorrect POS number");

    // výpočet podpisu pro porovnání se sig odeslaným ze strany PayU
    // calculating signature for comparison with sig sent by PayU
    $sig = md5($parts[1].$parts[2].$parts[3].$parts[5].$parts[4].$parts[6].$parts[7].$this->payUPar["key2"]);

    // chybný podpis v odpovědi v porovnání s podpisem spočítaným lokálně
    // incorrect signature in response in comparison to locally calculated one
    if($parts[8] != $sig)
        return array("code" => false, "message" => "incorrect signature");

    // různé zprávy dle statusu transakce. Popisy jednotlivých statusů jsou uvedeny v technické dokumentaci
    // different messages depending on transaction status. For status description, see documentation
    switch($parts[5]) {
        case 1: return array("code" => $parts[5], "message" => "new");
        case 2: return array("code" => $parts[5], "message" => "cancelled");
        case 3: return array("code" => $parts[5], "message" => "rejected");
        case 4: return array("code" => $parts[5], "message" => "started");
        case 5: return array("code" => $parts[5], "message" => "awaiting receipt");
        case 6: return array("code" => $parts[5], "message" => "no authorization");
        case 7: return array("code" => $parts[5], "message" => "payment rejected");
        case 99: return array("code" => $parts[5], "message" => "payment received - ended");
        case 888: return array("code" => $parts[5], "message" => "incorrect status");
        default: return array("code" => false, "message" => "no status");
    }
  }
}
