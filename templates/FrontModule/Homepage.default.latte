{php
  $GLOBALS["ecommProId"] = '';
  $GLOBALS["ecommPageType"] = 'home';
  $GLOBALS["ecommTotalValue"] = 0;
}

{block  #homepageSlider}
{* <!-- slideshow start --> *}
<div class="slider">

  <div class="container-fluid slider__body">

    {foreach $menuIndexs as $row}
      <div class="slider__item" style="background: #000 url({$baseUri}/pic/menuindex/{$row->meiid}.jpg) center top no-repeat;">

        {php
          $arrTexts = array();
          if (!empty($row->meidesc)){
            $arrTexts = explode("\n", trim($row->meidesc));
          }
        }
        <div class="slider__content">

          {* toto je nazev banneru *}
          <h2>{$row->meiname}</h2>

          {* texty - co řádek v adminu, to jeden text *}
          <p>
          {foreach $arrTexts as $text}
            {if $iterator->getCounter() == 1}
              {$text}
            {elseif $iterator->getCounter() == 2}
              {$text}
            {elseif $iterator->getCounter() == 3}
              {$text}
            {/if}
          {/foreach}
          </p>

          {* odkaz *}
          <p>
            <a class="btn btn--small" href="
            {if $row->meipagid > 0}
              {plink Page:detail $row->pagurlkey}
            {elseif $row->meicatid > 0}
              {plink Catalog:detail $row->catid, ($row|getCatKey)}
            {elseif !empty($row->meiprocode)}
              {plink Product:detail $row->proid, ($row|getProKey)}
            {else}
              {$row->meiurl}
            {/if}
            ">více
            </a>
          </p>

        </div>

      </div>

    {/foreach}

  </div>

</div>
{* <!-- slideshow end --> *}

{/block}

{block #content}

{ifset $textBlocks["homepage_text"]}{$textBlocks["homepage_text"]|noescape}{/ifset}

<h2 class="article__bigheader">Novinky</h2>

{* výpis produktů - Novinky *}
{include @productsList.latte, products => $homepageNewProducts, title => 'Novinky'}

<h2 class="article__bigheader">Oblíbené produkty</h2>

{* výpis produktů - Oblíbené *}
{include @productsList.latte, products => $homepageProducts, title => 'Oblíbené zboží'}
{/block}
