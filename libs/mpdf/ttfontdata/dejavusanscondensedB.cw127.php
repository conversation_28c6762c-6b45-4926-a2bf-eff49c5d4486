<?php
$rangeid=114;
$prevcid=126;
$prevwidth=754;
$interval=false;
$range=array (
  32 => 
  array (
    0 => 313,
    1 => 410,
    2 => 469,
    3 => 754,
    4 => 626,
    5 => 901,
    6 => 785,
    7 => 275,
  ),
  40 => 
  array (
    0 => 411,
    1 => 411,
    'interval' => true,
  ),
  42 => 
  array (
    0 => 470,
    1 => 754,
    2 => 342,
    3 => 374,
    4 => 342,
    5 => 329,
  ),
  48 => 
  array (
    0 => 626,
    1 => 626,
    'interval' => true,
    2 => 626,
    3 => 626,
    4 => 626,
    5 => 626,
    6 => 626,
    7 => 626,
    8 => 626,
    9 => 626,
  ),
  58 => 
  array (
    0 => 360,
    1 => 360,
    'interval' => true,
  ),
  60 => 
  array (
    0 => 754,
    1 => 754,
    'interval' => true,
    2 => 754,
  ),
  63 => 
  array (
    0 => 522,
    1 => 900,
    2 => 696,
    3 => 686,
    4 => 660,
    5 => 747,
  ),
  69 => 
  array (
    0 => 615,
    1 => 615,
    'interval' => true,
  ),
  71 => 
  array (
    0 => 738,
    1 => 753,
  ),
  73 => 
  array (
    0 => 334,
    1 => 334,
    'interval' => true,
  ),
  75 => 
  array (
    0 => 697,
    1 => 573,
    2 => 896,
    3 => 753,
    4 => 765,
    5 => 659,
    6 => 765,
    7 => 693,
    8 => 648,
    9 => 614,
    10 => 730,
    11 => 696,
    12 => 993,
    13 => 694,
    14 => 651,
    15 => 652,
    16 => 411,
    17 => 329,
    18 => 411,
    19 => 754,
  ),
  95 => 
  array (
    0 => 450,
    1 => 450,
    'interval' => true,
  ),
  97 => 
  array (
    0 => 607,
    1 => 644,
    2 => 533,
    3 => 644,
    4 => 610,
    5 => 391,
    6 => 644,
    7 => 641,
  ),
  105 => 
  array (
    0 => 308,
    1 => 308,
    'interval' => true,
  ),
  107 => 
  array (
    0 => 598,
    1 => 308,
    2 => 938,
    3 => 641,
    4 => 618,
  ),
  112 => 
  array (
    0 => 644,
    1 => 644,
    'interval' => true,
  ),
  114 => 
  array (
    0 => 444,
    1 => 536,
    2 => 430,
    3 => 641,
    4 => 586,
    5 => 831,
    6 => 580,
    7 => 586,
    8 => 523,
    9 => 641,
    10 => 329,
    11 => 641,
    12 => 754,
  ),
);
?>