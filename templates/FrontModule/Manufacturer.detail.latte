{$pageTitle       = (empty($man->mantitle) ? $man->manname : $man->mantitle)}
{$pageDescription = $man->mandesc}

{block #content}

<div class="product-category">

  <h1 class="product-category__header">{$man->manname}</h1>

  <div class="product-category__description">
    {$man->mandesc|noescape}
  </div>


  {form manufacturerSearchForm}
    {* vypise chyby formulare *}
    {include ../@formErrors.latte form=>$form}
    {* typy zbozi *}
    <div class="filter__label">
    {foreach $form['t']->controls as $item}
      {if $iterator->isFirst()}
      {/if}
        {php echo $item->getControl()}
        {php echo $item->getLabel()}
      {if $iterator->isLast()}
      {php
        $o = $presenter->o;
      }
      {if $o=='pa'}nejlevnější{else}<a href="{plink 'this', 'o'=>'pa'}">nejlevnějš<PERSON></a>{/if}&nbsp;/&nbsp;{if $o=='pd'}nejdražší{else}<a href="{plink 'this', 'o'=>'pd'}">nejdražší</a>{/if}
      {*{php echo $form["o"]->getLabel()} {php echo $form["o"]->getControl()}*}
      {/if}
    {/foreach}

    </div>

    <div class="product-category__submit">{input search}</div>

    {/form}

  {if count($productsByCatId) == 0}<p>Nejsou zde žádné výrobky</p>{/if}

</div>

<!-- products start -->
<div class="product-list">

  {foreach $rootCats as $crow}
    {if isset($productsByCatId[$crow->catid])}
    {foreach $productsByCatId[$crow->catid] as $key => $row}
    {if $iterator->isFirst()}
      <h2 class="product-category__divider"><a href="{plink Catalog:detail, $crow->catid, ($crow|getCatKey)}">{$crow->catname}</a></h2>
      <ul>
    {/if}

    {* jedna položka *}
    {include @productItem.latte, product => $row}

    {if $iterator->isLast()}
     </ul>
    {/if}
    {/foreach}
    {/if}
  {/foreach}
<!-- products end -->

  {* strankovani *}
  {control paginator}

</div>
{/block}
