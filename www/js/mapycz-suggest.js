/**
 * Implementace našeptávání adres pomocí Mapy.cz API
 */

// Funkce pro inicializaci našeptávání adres
function initMapySuggest() {
    // Najdeme všechny elementy s třídou mapycz-suggest
    const suggestElements = document.querySelectorAll('.mapycz-suggest');
    
    // Pro každý element nastavíme našeptávání
    suggestElements.forEach(function(element) {
        // Přidáme autocomplete="off" pro zabránění výchozímu našeptávání prohlížeče
        element.setAttribute('autocomplete', 'off');
        
        // Vytvoříme kontejner pro našeptávání
        const suggestContainer = document.createElement('div');
        suggestContainer.className = 'mapycz-suggest-container';
        suggestContainer.style.display = 'none';
        suggestContainer.style.position = 'absolute';
        suggestContainer.style.zIndex = '1000';
        suggestContainer.style.backgroundColor = '#fff';
        suggestContainer.style.border = '1px solid #ccc';
        suggestContainer.style.borderRadius = '4px';
        suggestContainer.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
        suggestContainer.style.maxHeight = '200px';
        suggestContainer.style.overflowY = 'auto';
        suggestContainer.style.width = element.offsetWidth + 'px';
        
        // Vložíme kontejner za element
        element.parentNode.insertBefore(suggestContainer, element.nextSibling);
        
        // Funkce pro získání našeptávání z Mapy.cz API
        function getSuggestions(query) {
            if (query.length < 2) {
                suggestContainer.style.display = 'none';
                return;
            }
            
            // Použijeme správnou URL pro API dotaz
            let apiUrl = 'https://api.mapy.cz/v1/suggest';
            
            // Parametry dotazu podle poskytnuté URL
            const params = {
                query: query,
                lang: 'cs',
                limit: 5,
                type: 'regional.address',
                apikey: 'EE_sOvhTj7S2ls1zjRma6g_jEScwhSkMepZ-i7vRt30'
            };
            
            // Přidáme parametry do URL
            apiUrl += '?' + new URLSearchParams(params).toString();
            
            // Provedeme AJAX dotaz
            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    // Vyčistíme kontejner
                    suggestContainer.innerHTML = '';
                    
                    // Pokud nejsou žádné výsledky, skryjeme kontejner
                    const results = data.items || data.result || [];
                    if (results.length === 0) {
                        suggestContainer.style.display = 'none';
                        return;
                    }
                    
                    // Zobrazíme kontejner
                    suggestContainer.style.display = 'block';
                    
                    // Přidáme výsledky do kontejneru
                    results.forEach(item => {
                        const suggestionItem = document.createElement('div');
                        suggestionItem.className = 'mapycz-suggest-item';
                        suggestionItem.style.padding = '8px 12px';
                        suggestionItem.style.cursor = 'pointer';
                        suggestionItem.style.borderBottom = '1px solid #eee';
                        
                        // Zobrazíme název položky podle poskytnuté struktury dat z API
                        if (item.name) {
                            suggestionItem.textContent = item.name;
                            
                            // Přidáme lokalitu (pouze město, bez země)
                            if (item.location) {
                                const locationParts = item.location.split(',');
                                if (locationParts.length > 0) {
                                    const city = locationParts[0].trim();
                                    // Přidáme město, pokud není již součástí názvu
                                    if (!item.name.includes(city)) {
                                        suggestionItem.textContent += ', ' + city;
                                    }
                                }
                            }
                            
                            // Přidáme PSČ, pokud je k dispozici
                            if (item.zip) {
                                suggestionItem.textContent += ', ' + item.zip;
                            }
                        } else if (item.label) {
                            suggestionItem.textContent = item.label;
                        } else {
                            // Pokud nemáme name ani label, použijeme JSON reprezentaci
                            suggestionItem.textContent = JSON.stringify(item);
                        }
                        
                        // Přidáme hover efekt
                        suggestionItem.addEventListener('mouseover', function() {
                            this.style.backgroundColor = '#f5f5f5';
                        });
                        
                        suggestionItem.addEventListener('mouseout', function() {
                            this.style.backgroundColor = 'transparent';
                        });
                        
                        // Přidáme kliknutí na položku
                        suggestionItem.addEventListener('click', function() {
                            // Nastavíme hodnotu ulice
                            element.value = item.name || item.label || item.title;
                            suggestContainer.style.display = 'none';
                            
                            // Vyplníme město a PSČ, pokud jsou k dispozici
                            // Zjistíme, zda jde o fakturační nebo dodací adresu
                            const isInvoiceField = element.name.startsWith('ordi');
                            const isShippingField = element.name.startsWith('ordst');
                            
                            // Najdeme pole pro město a PSČ
                            let cityField, zipField, streetNoField;
                            
                            if (isInvoiceField) {
                                cityField = document.querySelector('input[name="ordicity"]');
                                zipField = document.querySelector('input[name="ordipostcode"]');
                                streetNoField = document.querySelector('input[name="ordistreetno"]');
                            } else if (isShippingField) {
                                cityField = document.querySelector('input[name="ordstcity"]');
                                zipField = document.querySelector('input[name="ordstpostcode"]');
                                streetNoField = document.querySelector('input[name="ordststreetno"]');
                            }
                            
                            // Rozdělíme adresu na části podle poskytnuté struktury dat z API
                            let street = '';
                            let houseNumber = '';
                            let city = '';
                            let zipCode = '';
                            
                            // Zpracujeme data podle struktury odpovědi z API
                            if (item.name) {
                                // Pokusíme se rozdělit name na ulici a číslo popisné
                                const nameParts = item.name.split(' ');
                                if (nameParts.length > 1) {
                                    // Poslední část je pravděpodobně číslo popisné
                                    houseNumber = nameParts.pop();
                                    // Zbytek je ulice
                                    street = nameParts.join(' ');
                                } else {
                                    // Pokud nemůžeme rozdělit, použijeme celý name jako ulici
                                    street = item.name;
                                }
                            }
                            
                            // Najdeme město v regionalStructure
                            if (item.regionalStructure && Array.isArray(item.regionalStructure)) {
                                // Hledáme položku typu regional.municipality
                                const municipalityItem = item.regionalStructure.find(rs => rs.type === 'regional.municipality');
                                if (municipalityItem) {
                                    city = municipalityItem.name;
                                }
                            }
                            
                            // Pokud nemáme město z regionalStructure, zkusíme použít location
                            if (!city && item.location) {
                                // Location má formát "Palkovice, Česko", vezmeme první část
                                const locationParts = item.location.split(',');
                                if (locationParts.length > 0) {
                                    city = locationParts[0].trim();
                                }
                            }
                            
                            // PSČ je přímo v kořenovém objektu
                            zipCode = item.zip || '';
                            
                            // Pokud máme ulici, nastavíme ji do pole pro ulici
                            if (street) {
                                element.value = street;
                            }
                            
                            // Vyplníme město, pokud je k dispozici
                            if (cityField && city) {
                                cityField.value = city;
                            }
                            
                            // Vyplníme PSČ, pokud je k dispozici
                            if (zipField && zipCode) {
                                zipField.value = zipCode;
                            }
                            
                            // Pokud je k dispozici číslo popisné, vyplníme ho
                            if (streetNoField && houseNumber) {
                                streetNoField.value = houseNumber;
                            }
                        });
                        
                        suggestContainer.appendChild(suggestionItem);
                    });
                })
                .catch(error => {
                    suggestContainer.style.display = 'none';
                });
        }
        
        // Přidáme posluchače událostí pro input
        element.addEventListener('input', function() {
            getSuggestions(this.value);
        });
        
        // Skryjeme našeptávání při kliknutí mimo
        document.addEventListener('click', function(event) {
            if (event.target !== element && event.target !== suggestContainer) {
                suggestContainer.style.display = 'none';
            }
        });
        
        // Zobrazíme našeptávání při focusu
        element.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                getSuggestions(this.value);
            }
        });
        
        // Přidáme ovládání klávesnicí
        element.addEventListener('keydown', function(event) {
            if (suggestContainer.style.display === 'none') return;
            
            const items = suggestContainer.querySelectorAll('.mapycz-suggest-item');
            let activeItem = suggestContainer.querySelector('.mapycz-suggest-item.active');
            let activeIndex = -1;
            
            // Najdeme index aktivní položky
            if (activeItem) {
                for (let i = 0; i < items.length; i++) {
                    if (items[i] === activeItem) {
                        activeIndex = i;
                        break;
                    }
                }
            }
            
            // Šipka dolů
            if (event.key === 'ArrowDown') {
                event.preventDefault();
                
                if (activeItem) {
                    activeItem.classList.remove('active');
                    activeItem.style.backgroundColor = 'transparent';
                }
                
                activeIndex = (activeIndex + 1) % items.length;
                items[activeIndex].classList.add('active');
                items[activeIndex].style.backgroundColor = '#f5f5f5';
                
                // Zajistíme, že aktivní položka je viditelná
                items[activeIndex].scrollIntoView({ block: 'nearest' });
            }
            
            // Šipka nahoru
            if (event.key === 'ArrowUp') {
                event.preventDefault();
                
                if (activeItem) {
                    activeItem.classList.remove('active');
                    activeItem.style.backgroundColor = 'transparent';
                }
                
                activeIndex = (activeIndex - 1 + items.length) % items.length;
                items[activeIndex].classList.add('active');
                items[activeIndex].style.backgroundColor = '#f5f5f5';
                
                // Zajistíme, že aktivní položka je viditelná
                items[activeIndex].scrollIntoView({ block: 'nearest' });
            }
            
            // Enter
            if (event.key === 'Enter' && activeItem) {
                event.preventDefault();
                activeItem.click();
            }
            
            // Escape
            if (event.key === 'Escape') {
                suggestContainer.style.display = 'none';
            }
        });
    });
}

// Inicializace našeptávání adres při načtení DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    initMapySuggest();
});

// Inicializace našeptávání adres při načtení skriptu
initMapySuggest();

// Inicializace našeptávání adres po 1 sekundě (pro případ, že by se DOM načetl později)
setTimeout(function() {
    initMapySuggest();
}, 1000);
