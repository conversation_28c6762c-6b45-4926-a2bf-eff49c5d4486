// odkaz na košík

.basket {

  position: relative; // pro pozicování ikony

  padding-top: 5px;
  padding-bottom: 5px;
  padding-right: 5px;
  padding-left: 55px; // místo pro ikonu

  font-size: 15px;
  color: @color_gray_dark;

  @media (min-width: @mqsm) {
    float: right;
  }

  // úprava vzhledu na nižších rozlišeních
  @media (max-width: @mqsm) {
    .clear;
    width: 50%;
    display: inline-block;
    vertical-align: top;
  }
  @media (max-width: 375px) {
    width: 60%;
  }

  a {
    color: @color_main_light;
    &:hover, &:active, &:focus { color: @color_main_dark; }
  }

  p {
    margin: 5px 0;
  }

  .icon {
    position: absolute;
    margin-left: -55px;
    margin-top: -13px;
    color: @color_main_light;
    font-size: 53px;
  }

  // pokud je ko<PERSON><PERSON> klikatelný (pouze pokud je v něm obsah)
  a .icon {

    text-decoration: none;

    &:hover, &:active, &:focus {
      color: @color_main_dark;
    }

  }

}

  // nadpis boxu
  .basket__header {
    font-size: 15px;
    text-transform: uppercase;
  }

  // obsah boxu
  .basket__content {

  }
