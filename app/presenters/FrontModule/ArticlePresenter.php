<?php
namespace FrontModule;
use dibi;
use Nette;

final class ArticlePresenter extends BasePresenter {

  public function renderDefault() {

    $articles = new \Model\ArticlesModel();
    $dataSource = $articles->getDataSource("SELECT * FROM articles WHERE artstatus=0 ORDER BY artdate DESC");

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = 20;
    $paginator->itemCount = $dataSource->count();
    $this->template->rows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
  }

  public function renderDetailOld($id, $key) {
    $articles = new \Model\ArticlesModel();
    $articleData = $articles->load($id);
    if ($articleData) {
      //kontrola platnosti URL
      $urlkey = (!empty($articleData->arturlkey) ? $articleData->arturlkey : Nette\Utils\Strings::webalize($articleData->artname));
      //presmeruju na novy
      $this->redirect(301, 'Article:detail', array('id'=>$id, 'key'=>$urlkey));
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }
  }

  public function renderDetail($id, $key) {
    $articles = new \Model\ArticlesModel();
    $articleData = $articles->load($id);
    if ($articleData) {
      if ($articleData->artstatus == 1) throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
      if ($articleData->artid > 0) {
        $this->template->urlkey = $articleData->arturlkey;
        $this->template->article = $articleData;
      }
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }
    $articles->runCounter($articleData->artid);

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $articleData->artid);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $articleData->artid);
  }
}
