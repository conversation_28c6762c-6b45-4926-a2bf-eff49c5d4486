Allow from all

# disable directory listing
<IfModule mod_autoindex.c>
	Options -Indexes
</IfModule>

# enable cool URL
<IfModule mod_rewrite.c>
	RewriteEngine On
	# RewriteBase /

  #RewriteCond %{HTTPS} !=on
  #RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

  # presmeruje na adresu s www na zacatku
  RewriteCond %{HTTP_HOST} ^([^\.]+\.[^\.]+)$ [NC]
  RewriteRule ^(.*)$ https://www.%1/$1 [R=301,L]

	# prevents files starting with dot to be viewed by browser
	RewriteRule /\.|^\.(?!well-known/) - [F]

	# front controller
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule !\.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz|map)$ index.php [L]
</IfModule>

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
</IfModule>

<IfModule mod_expires.c>
  ExpiresActive On
  <FilesMatch "\.(jpg|jpeg|png|gif|svg|js|css|eot|ttf|woff|ico)$">
  ExpiresDefault "access plus 1 month"
  </FilesMatch>
</IfModule>