// navigace

.nav-product {

  margin-bottom: 25px;

  ul {
    list-style-type: none;
    margin: 0;
    padding: 10px 0;
    color: @color_white;
    background-color: @color_main_light;
  }

  // podmenu
  li ul {
    padding: 0;
    margin: 5px 0;
  }
  li ul a {
    padding: 5px 10px 5px 48px;
    font-size: 15px;
    background: url('../../img/menu-dash.svg') 30px center no-repeat;
  }

  // aktivní <PERSON>t
  li.is-active {

    padding: 10px 0;
    background-color: @color_main;

    a {

      background-color: @color_main;

      &:hover, &:active, &:focus {
        background-color: @color_main_dark;
      }

    }

    // podmenu aktivní části
    li.is-active {

      padding: 0;

      a {
        padding-top: 8px;
        padding-bottom: 8px;
        background-color: @color_main_dark;
      }

    }

  }

  a {

    display: block;
    padding: 8px 10px 8px 25px;
    color: @color_white;
    font-size: 16px;
    text-decoration: none;

    &:hover, &:active, &:focus {
      background-color: @color_main;
    }

  }

  strong {
    font-weight: 400;
  }

}

  // mobilní verze menu
  .nav-product__wrapper {

    @media (max-width: @mqsm) {

      //display: none; // standardně skryto
      position: absolute; // pozicujeme nahoru k ikoně
      top: 55px;
      z-index: 1;
      width: 100%;
      padding: 0;

    }

  }

  // skryté menu pro full-page stránky
  .nav-product--hidden {
    display: none;
  }

  // nadpis menu
  .nav-product__header {
    margin: 0;
    padding: 10px 15px 7px 15px;
    color: @color_white;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    background-color: @color_main_light;
  }

  // výrobce bude napravo
  .nav-product__header--right {
    right: 0;
  }

  // spodní menu
  .nav-product__other {

    ul {
      background-color: @color_main;
    }

    a {
      background-color: @color_main;
      &:hover, &:active, &:focus { background-color: @color_main_dark; }
    }

  }

  // textový blok
  .nav-product__textblock {

  }
