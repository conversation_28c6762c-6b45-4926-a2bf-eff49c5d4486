{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "0265c470f9c2452456cbf336a6b1e6bd", "packages": [{"name": "dibi/dibi", "version": "v3.2.4", "source": {"type": "git", "url": "https://github.com/dg/dibi.git", "reference": "d571460a6f8fa1334a04f7aaa1551bb0f12c2266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/dibi/zipball/d571460a6f8fa1334a04f7aaa1551bb0f12c2266", "reference": "d571460a6f8fa1334a04f7aaa1551bb0f12c2266", "shasum": ""}, "require": {"php": ">=5.4.4"}, "replace": {"dg/dibi": "*"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "~2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Dibi is Database Abstraction Library for PHP", "homepage": "https://dibiphp.com", "keywords": ["access", "database", "dbal", "mssql", "mysql", "odbc", "oracle", "pdo", "postgresql", "sqlite", "sqlsrv"], "support": {"issues": "https://github.com/dg/dibi/issues", "source": "https://github.com/dg/dibi/tree/v3.2.4"}, "time": "2020-03-26T03:05:01+00:00"}, {"name": "grifart/enum", "version": "0.2.1", "source": {"type": "git", "url": "https://github.com/grifart/enum.git", "reference": "f4e6c3fc1d172ac959e135a39fa63323c4e377b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grifart/enum/zipball/f4e6c3fc1d172ac959e135a39fa63323c4e377b1", "reference": "f4e6c3fc1d172ac959e135a39fa63323c4e377b1", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"grifart/phpstan-oneline": "^0.2.0", "nette/tester": "^2.1.0", "phpstan/phpstan": "^0.10.7", "phpstan/phpstan-strict-rules": "^0.10.1"}, "type": "library", "autoload": {"psr-4": {"Grifart\\Enum\\": "src"}, "classmap": ["src/exceptions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides bullet proof enums with behaviours.", "support": {"issues": "https://github.com/grifart/enum/issues", "source": "https://github.com/grifart/enum/tree/0.2.1"}, "time": "2019-12-10T15:36:35+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-12-03T20:19:20+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/45b30f99ac27b5ca93cb4831afe16285f57b8221", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-12-03T20:05:35+00:00"}, {"name": "ipub/mobile-detect", "version": "v2.2.2", "source": {"type": "git", "url": "https://github.com/iPublikuj/mobile-detect.git", "reference": "cfdc3a9bb13326c5bca5aa6e12403855328c6f06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/iPublikuj/mobile-detect/zipball/cfdc3a9bb13326c5bca5aa6e12403855328c6f06", "reference": "cfdc3a9bb13326c5bca5aa6e12403855328c6f06", "shasum": ""}, "require": {"jenssegers/agent": "~2.5", "latte/latte": "~2.4", "nette/application": "~2.4", "nette/di": "~2.4", "nette/http": "~2.4", "nette/utils": "~2.4", "php": ">=7.1.0"}, "require-dev": {"nette/bootstrap": "~2.4", "nette/mail": "~2.4", "nette/robot-loader": "~2.4", "nette/safe-stream": "~2.3", "nette/tester": "~2.0", "pds/skeleton": "~1.0", "tracy/tracy": "~2.4"}, "type": "library", "extra": {"ipub": {"configuration": {"extensions": {"mobileDetect": "IPub\\MobileDetect\\DI\\MobileDetectExtension"}}, "mobileDetect": {"redirect": {"mobile": {"isEnabled": false, "host": null, "statusCode": 301, "action": "noRedirect"}, "phone": {"isEnabled": false, "host": null, "statusCode": 301, "action": "noRedirect"}, "tablet": {"isEnabled": false, "host": null, "statusCode": 301, "action": "noRedirect"}, "detectPhoneAsMobile": false, "detectTabletAsMobile": false}, "switchDeviceView": {"saveRefererPath": true}}}}, "autoload": {"psr-0": {"IPub\\MobileDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0", "GPL-3.0"], "authors": [{"name": "iPublikuj:cms", "email": "<EMAIL>", "homepage": "https://www.ipublikuj.eu/"}], "description": "Extension for detecting mobile devices, managing mobile view types, redirect to mobile version for Nette Framework", "homepage": "https://github.com/iPublikuj/mobile-detect", "keywords": ["framework", "ipub", "ipublikuj", "mobile", "mobile detect", "mobile redirect", "mobile view managing", "nette", "tools"], "support": {"email": "<EMAIL>", "issues": "https://github.com/iPublikuj/mobile-detect/issues", "source": "https://github.com/iPublikuj/mobile-detect/tree/master"}, "abandoned": true, "time": "2018-03-18T15:24:06+00:00"}, {"name": "ipub/visual-paginator", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/ipublikuj-ui/visual-paginator.git", "reference": "26ba98199a7d6d6a61072fc8323176e6cec39dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ipublikuj-ui/visual-paginator/zipball/26ba98199a7d6d6a61072fc8323176e6cec39dbe", "reference": "26ba98199a7d6d6a61072fc8323176e6cec39dbe", "shasum": ""}, "require": {"latte/latte": "~2.2", "nette/application": "~2.2", "nette/di": "~2.2", "nette/utils": "~2.2", "php": ">=5.4.0"}, "require-dev": {"janmarek/mockista": "@dev", "nette/bootstrap": "~2.2", "nette/forms": "~2.2", "nette/mail": "~2.2", "nette/robot-loader": "~2.2", "nette/safe-stream": "~2.2", "nette/tester": "@dev", "tracy/tracy": "@dev"}, "type": "library", "autoload": {"psr-0": {"IPub\\VisualPaginator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "iPublikuj:cms", "email": "<EMAIL>", "homepage": "http://www.ipublikuj.eu/"}], "description": "Visual paginator for Nette Framework", "homepage": "https://github.com/iPublikuj/visual-paginator", "keywords": ["ipub", "ipublikuj", "nette", "paginator", "paging", "tools"], "support": {"email": "<EMAIL>", "issues": "https://github.com/iPublikuj/visual-paginator/issues", "source": "https://github.com/ipublikuj-ui/visual-paginator/tree/v1.0.7"}, "abandoned": true, "time": "2018-07-02T18:04:23+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.119", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "275002e22b0333c15a7c6792fdae5d5deefc9ef0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/275002e22b0333c15a7c6792fdae5d5deefc9ef0", "reference": "275002e22b0333c15a7c6792fdae5d5deefc9ef0", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.119"}, "time": "2024-06-07T07:58:43+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": ""}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "support": {"issues": "https://github.com/jenssegers/agent/issues", "source": "https://github.com/jenssegers/agent/tree/v2.6.4"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "time": "2020-06-13T08:05:20+00:00"}, {"name": "latte/latte", "version": "v2.11.7", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "0ac0843a459790d471821f6a82f5d13db831a0d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/0ac0843a459790d471821f6a82f5d13db831a0d3", "reference": "0ac0843a459790d471821f6a82f5d13db831a0d3", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "7.1 - 8.3"}, "conflict": {"nette/application": "<2.4.1"}, "require-dev": {"nette/php-generator": "^3.3.4", "nette/tester": "^2.0", "nette/utils": "^3.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.11-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v2.11.7"}, "time": "2023-10-18T17:16:11+00:00"}, {"name": "minetro/recaptcha", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/contributte/reCAPTCHA.git", "reference": "823c29d4fdba9ed81e2ddc2f5e2bd1c34a40e248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/reCAPTCHA/zipball/823c29d4fdba9ed81e2ddc2f5e2bd1c34a40e248", "reference": "823c29d4fdba9ed81e2ddc2f5e2bd1c34a40e248", "shasum": ""}, "require": {"nette/di": "~2.4.11", "nette/forms": "~2.4.7", "nette/utils": "~2.5.1", "php": ">= 5.6"}, "conflict": {"nette/http": "<2.4.0"}, "require-dev": {"ninjify/nunjuck": "^0.2.0", "ninjify/qa": "^0.4.0"}, "suggest": {"ext-openssl": "To make requests via https"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "3.1.x-dev"}}, "autoload": {"psr-4": {"Contributte\\ReCaptcha\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Google reCAPTCHA for Nette - Forms", "homepage": "https://github.com/contributte/reCAPTCHA", "keywords": ["Forms", "<PERSON><PERSON>a", "google", "nette", "recaptcha"], "support": {"issues": "https://github.com/contributte/reCAPTCHA/issues", "source": "https://github.com/contributte/reCAPTCHA/tree/3.1.0"}, "abandoned": "contributte/reCAPTCHA", "time": "2018-08-22T13:31:56+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.45", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/96aaebcf4f50d3d2692ab81d2c5132e425bca266", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.45"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2023-11-07T21:57:25+00:00"}, {"name": "nette/application", "version": "v2.4.17", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "c2d4def8fce2602d74f03ff29204da0ee708c9dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/c2d4def8fce2602d74f03ff29204da0ee708c9dc", "reference": "c2d4def8fce2602d74f03ff29204da0ee708c9dc", "shasum": ""}, "require": {"nette/component-model": "^2.3", "nette/http": "^2.2", "nette/reflection": "^2.2", "nette/utils": "^2.4.3", "php": ">=5.6 <8.1"}, "conflict": {"nette/di": "<2.4", "nette/forms": "<2.4", "nette/latte": "<2.4 >=3.0", "nette/nette": "<2.2"}, "require-dev": {"latte/latte": "^2.4.3 <2.8", "mockery/mockery": "^1.0", "nette/di": "^2.4", "nette/forms": "^2.4", "nette/robot-loader": "^2.4.2 || ^3.0", "nette/security": "^2.4", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v2.4.17"}, "time": "2020-11-24T19:05:35+00:00"}, {"name": "nette/bootstrap", "version": "v2.4.6", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "268816e3f1bb7426c3a4ceec2bd38a036b532543"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/268816e3f1bb7426c3a4ceec2bd38a036b532543", "reference": "268816e3f1bb7426c3a4ceec2bd38a036b532543", "shasum": ""}, "require": {"nette/di": "~2.4.7", "nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.2", "nette/application": "~2.3", "nette/caching": "~2.3", "nette/database": "~2.3", "nette/forms": "~2.3", "nette/http": "~2.4.0", "nette/mail": "~2.3", "nette/robot-loader": "^2.4.2 || ^3.0", "nette/safe-stream": "~2.2", "nette/security": "~2.3", "nette/tester": "~2.0", "tracy/tracy": "^2.4.1"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v2.4"}, "time": "2018-05-17T12:52:20+00:00"}, {"name": "nette/caching", "version": "v2.5.9", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "d93ef446836a5a0ff7ef78d5ffebb7fe043f9953"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/d93ef446836a5a0ff7ef78d5ffebb7fe043f9953", "reference": "d93ef446836a5a0ff7ef78d5ffebb7fe043f9953", "shasum": ""}, "require": {"nette/finder": "^2.2 || ~3.0.0", "nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "^2.4", "nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v2.5.9"}, "time": "2019-11-19T18:38:13+00:00"}, {"name": "nette/component-model", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "6e7980f5ddec31f68a39e767799b1b0be9dd1014"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/6e7980f5ddec31f68a39e767799b1b0be9dd1014", "reference": "6e7980f5ddec31f68a39e767799b1b0be9dd1014", "shasum": ""}, "require": {"nette/utils": "^2.5 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/application": "<2.4", "nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v2.4.0"}, "time": "2018-03-20T16:32:50+00:00"}, {"name": "nette/database", "version": "v2.4.12", "source": {"type": "git", "url": "https://github.com/nette/database.git", "reference": "c7a1431c9a82b866b7bd464ec9ad8758654f655a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/database/zipball/c7a1431c9a82b866b7bd464ec9ad8758654f655a", "reference": "c7a1431c9a82b866b7bd464ec9ad8758654f655a", "shasum": ""}, "require": {"ext-pdo": "*", "nette/caching": "^2.2", "nette/utils": "^2.4", "php": ">=5.6 <8.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"mockery/mockery": "^1.0.0", "nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💾 Nette Database: layer with a familiar PDO-like API but much more powerful. Building queries, advanced joins, drivers for MySQL, PostgreSQL, SQLite, MS SQL Server and Oracle.", "homepage": "https://nette.org", "keywords": ["database", "mssql", "mysql", "nette", "notorm", "oracle", "pdo", "postgresql", "queries", "sqlite"], "support": {"issues": "https://github.com/nette/database/issues", "source": "https://github.com/nette/database/tree/v2.4.12"}, "time": "2022-11-18T03:14:03+00:00"}, {"name": "nette/di", "version": "v2.4.17", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "aa25fa3dfcb0fc40af8da1f2495af75b63b5db1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/aa25fa3dfcb0fc40af8da1f2495af75b63b5db1d", "reference": "aa25fa3dfcb0fc40af8da1f2495af75b63b5db1d", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^2.3.3 || ~3.0.0", "nette/php-generator": "^2.6.1 || ^3.0.0", "nette/utils": "^2.5.0 || ~3.0.0", "php": ">=5.6 <8.1"}, "conflict": {"nette/bootstrap": "<2.4", "nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP 7.1 features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v2.4.17"}, "time": "2020-11-06T00:28:18+00:00"}, {"name": "nette/finder", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "991aefb42860abeab8e003970c3809a9d83cb932"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/991aefb42860abeab8e003970c3809a9d83cb932", "reference": "991aefb42860abeab8e003970c3809a9d83cb932", "shasum": ""}, "require": {"nette/utils": "^2.4 || ^3.0", "php": ">=7.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v2.6.0"}, "time": "2022-10-13T01:31:15+00:00"}, {"name": "nette/forms", "version": "v2.4.11", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "f29624795d77b155d5de82f186d0f63c8ebe03fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/f29624795d77b155d5de82f186d0f63c8ebe03fe", "reference": "f29624795d77b155d5de82f186d0f63c8ebe03fe", "shasum": ""}, "require": {"nette/component-model": "~2.3", "nette/http": "^2.3.8", "nette/utils": "^2.4.6", "php": ">=5.6 <8.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.4", "nette/di": "~2.4", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "?? Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v2.4.11"}, "time": "2020-11-06T00:25:25+00:00"}, {"name": "nette/http", "version": "v2.4.12", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "a5adfb1746f1d21d75e8b77f84a3d7f84977dcb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/a5adfb1746f1d21d75e8b77f84a3d7f84977dcb6", "reference": "a5adfb1746f1d21d75e8b77f84a3d7f84977dcb6", "shasum": ""}, "require": {"nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6 <8.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4.8 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of uploaded files", "nette/security": "allows use Nette\\Http\\UserStorage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v2.4.12"}, "time": "2020-11-06T00:17:53+00:00"}, {"name": "nette/mail", "version": "v2.4.6", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "431f1774034cc14ee6a795b6514fe6343f75a68e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/431f1774034cc14ee6a795b6514fe6343f75a68e", "reference": "431f1774034cc14ee6a795b6514fe6343f75a68e", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of attached files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Mail: handy email creation and transfer library for PHP with both text and MIME-compliant support.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v2.4"}, "time": "2018-11-21T22:35:13+00:00"}, {"name": "nette/neon", "version": "v2.4.3", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "5e72b1dd3e2d34f0863c5561139a19df6a1ef398"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/5e72b1dd3e2d34f0863c5561139a19df6a1ef398", "reference": "5e72b1dd3e2d34f0863c5561139a19df6a1ef398", "shasum": ""}, "require": {"ext-iconv": "*", "ext-json": "*", "php": ">=5.6.0"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette NEON: encodes and decodes NEON file format.", "homepage": "http://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v2.4"}, "time": "2018-03-21T12:12:21+00:00"}, {"name": "nette/nette", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/nette/nette.git", "reference": "c74833a3581a7066a18c66b4f177b6a0bcbbf818"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/nette/zipball/c74833a3581a7066a18c66b4f177b6a0bcbbf818", "reference": "c74833a3581a7066a18c66b4f177b6a0bcbbf818", "shasum": ""}, "require": {"latte/latte": "^2.4", "nette/application": "^2.4", "nette/bootstrap": "^2.4", "nette/caching": "^2.5", "nette/component-model": "^2.4", "nette/database": "^2.4", "nette/di": "^2.4", "nette/finder": "^2.4", "nette/forms": "^2.4", "nette/http": "^2.4", "nette/mail": "^2.4", "nette/neon": "^2.4", "nette/php-generator": "^2.4 || ^3.0", "nette/robot-loader": "^2.4 || ^3.0", "nette/safe-stream": "^2.4", "nette/security": "^2.4", "nette/tokenizer": "^2.2", "nette/utils": "^2.5", "tracy/tracy": "^2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["Nette/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Framework - innovative framework for fast and easy development of secured web applications in PHP (metapackage)", "homepage": "https://nette.org", "keywords": ["framework", "metapackage", "mvc"], "support": {"issues": "https://github.com/nette/nette/issues", "source": "https://github.com/nette/nette/tree/v2.5.0"}, "time": "2019-02-26T14:38:05+00:00"}, {"name": "nette/php-generator", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "7051954c534cebafd650efe8b145ac75b223cb66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/7051954c534cebafd650efe8b145ac75b223cb66", "reference": "7051954c534cebafd650efe8b145ac75b223cb66", "shasum": ""}, "require": {"nette/utils": "^2.4.2 || ^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "nikic/php-parser": "^4.4", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "suggest": {"nikic/php-parser": "to use ClassType::withBodiesFrom() & GlobalFunction::withBodyFrom()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 7.4 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/master"}, "time": "2020-06-19T14:31:47+00:00"}, {"name": "nette/reflection", "version": "v2.4.2", "source": {"type": "git", "url": "https://github.com/nette/reflection.git", "reference": "b12327e98ead74e87a1315e0d48182a702adf901"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/reflection/zipball/b12327e98ead74e87a1315e0d48182a702adf901", "reference": "b12327e98ead74e87a1315e0d48182a702adf901", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/caching": "^2.2 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4 || ^3.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Reflection: docblock annotations parser and common reflection classes", "homepage": "https://nette.org", "keywords": ["annotation", "nette", "reflection"], "support": {"issues": "https://github.com/nette/reflection/issues", "source": "https://github.com/nette/reflection/tree/master"}, "abandoned": true, "time": "2017-07-11T19:28:57+00:00"}, {"name": "nette/robot-loader", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "bde1d0ad576ff2b95e99a0b0f7d4fbe00c1ec0d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/bde1d0ad576ff2b95e99a0b0f7d4fbe00c1ec0d4", "reference": "bde1d0ad576ff2b95e99a0b0f7d4fbe00c1ec0d4", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/finder": "^2.3 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=5.6 <8.2"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v3.1.4"}, "time": "2022-02-01T13:39:08+00:00"}, {"name": "nette/safe-stream", "version": "v2.5.1", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "96c57055927d0f2b4d0fe545896a7a0335adbeb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/96c57055927d0f2b4d0fe545896a7a0335adbeb5", "reference": "96c57055927d0f2b4d0fe545896a7a0335adbeb5", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: provides isolation for thread safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "isolation", "nette", "safe", "thread safe"], "support": {"issues": "https://github.com/nette/safe-stream/issues", "source": "https://github.com/nette/safe-stream/tree/v2.5.1"}, "time": "2022-12-12T17:17:33+00:00"}, {"name": "nette/security", "version": "v2.4.4", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "7b8ac90c9ec405bb3b4dab9214bf122d3620fc65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/7b8ac90c9ec405bb3b4dab9214bf122d3620fc65", "reference": "7b8ac90c9ec405bb3b4dab9214bf122d3620fc65", "shasum": ""}, "require": {"nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "~2.4", "nette/http": "~2.4", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v2.4"}, "time": "2018-10-17T15:50:54+00:00"}, {"name": "nette/tokenizer", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "88373e9f79007245af0ccd8132fde117421723b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/88373e9f79007245af0ccd8132fde117421723b2", "reference": "88373e9f79007245af0ccd8132fde117421723b2", "shasum": ""}, "require": {"php": ">=5.4"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "support": {"issues": "https://github.com/nette/tokenizer/issues", "source": "https://github.com/nette/tokenizer/tree/v2.3.0"}, "abandoned": true, "time": "2017-09-08T14:16:06+00:00"}, {"name": "nette/utils", "version": "v2.5.7", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "d272f87cd6491377231702b1ccd920b6e981b713"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d272f87cd6491377231702b1ccd920b6e981b713", "reference": "d272f87cd6491377231702b1ccd920b6e981b713", "shasum": ""}, "require": {"php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize() and toAscii()", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠 Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v2.5.7"}, "time": "2020-12-13T14:12:17+00:00"}, {"name": "nextras/mail-panel", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/nextras/mail-panel.git", "reference": "790008bcbb9df1de8254cdda920ac023909b6b59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/mail-panel/zipball/790008bcbb9df1de8254cdda920ac023909b6b59", "reference": "790008bcbb9df1de8254cdda920ac023909b6b59", "shasum": ""}, "require": {"latte/latte": "~2.4", "nette/http": "~2.4", "nette/mail": "~2.4", "nette/utils": "~2.4", "php": ">=5.6", "tracy/tracy": "~2.4"}, "require-dev": {"phpstan/phpstan-nette": "~0.9", "phpstan/phpstan-shim": "~0.9.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"Nextras\\MailPanel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://www.jandrabek.cz"}, {"name": "<PERSON>", "homepage": "http://www.janmarek.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "MailPanel is extension for Nette Framework which captures sent e-mails in development mode and shows them in debugger bar.", "keywords": ["debugging", "framework", "mail", "mail panel", "mailing", "mailpanel"], "support": {"issues": "https://github.com/nextras/mail-panel/issues", "source": "https://github.com/nextras/mail-panel/tree/v2.4.0-beta1"}, "time": "2017-12-12T09:07:26+00:00"}, {"name": "ondrakoupil/csob-eapi-paygate", "version": "v1.9.5", "source": {"type": "git", "url": "https://github.com/ondrakoupil/csob.git", "reference": "3dfc7155223e0e83fc9b8fc7caa4dc19f9acc7eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ondrakoupil/csob/zipball/3dfc7155223e0e83fc9b8fc7caa4dc19f9acc7eb", "reference": "3dfc7155223e0e83fc9b8fc7caa4dc19f9acc7eb", "shasum": ""}, "require": {"ext-curl": "*", "ext-openssl": "*", "ondrakoupil/tools": "^1.2.4", "php": ">=5.4.8"}, "require-dev": {"nette/tester": "1.7.2", "ondrakoupil/testing-utils": "~0.0.7"}, "type": "library", "autoload": {"psr-4": {"OndraKoupil\\Csob\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Client library for easy integration of ČSOB payment gateway", "homepage": "https://github.com/ondrakoupil/csob", "keywords": ["Bank", "cards", "commerce", "csob", "merchant", "pay", "payment"], "support": {"issues": "https://github.com/ondrakoupil/csob/issues", "source": "https://github.com/ondrakoupil/csob/tree/v1.9.5"}, "time": "2023-01-13T12:59:25+00:00"}, {"name": "ondrakoupil/tools", "version": "v1.4.2", "source": {"type": "git", "url": "https://github.com/ondrakoupil/tools.git", "reference": "c40b4c815be7c1256890926f9947f6616925d612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ondrakoupil/tools/zipball/c40b4c815be7c1256890926f9947f6616925d612", "reference": "c40b4c815be7c1256890926f9947f6616925d612", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*"}, "require-dev": {"nette/tester": "^2.3"}, "type": "library", "autoload": {"psr-4": {"OndraKoupil\\Tools\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Just a bunch of tools for myself", "support": {"issues": "https://github.com/ondrakoupil/tools/issues", "source": "https://github.com/ondrakoupil/tools/tree/v1.4.2"}, "time": "2024-04-09T09:13:31+00:00"}, {"name": "pixidos/gpwebpay-core", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/Pixidos/gpwebpay-core.git", "reference": "1390666d294758c4d81d930842ae804179405ca9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Pixidos/gpwebpay-core/zipball/1390666d294758c4d81d930842ae804179405ca9", "reference": "1390666d294758c4d81d930842ae804179405ca9", "shasum": ""}, "require": {"ext-openssl": "*", "grifart/enum": "^0.2.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan-phpunit": "^1.3.3", "phpunit/phpunit": "^9.6.3", "pixidos/coding-standards": "1.2.*"}, "type": "library", "autoload": {"files": ["src/validators.php", "src/stubs.php"], "psr-4": {"Pixidos\\GPWebPay\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "GPWepPay core php library for http api GPWebPay service", "homepage": "https://github.com/pixidos", "keywords": ["checkout", "gp", "gp webpay", "pay", "payment", "webpay"], "support": {"email": "<EMAIL>", "issues": "https://github.com/Pixidos/gpwebpay-core/issues", "source": "https://github.com/Pixidos/gpwebpay-core/tree/2.2.1"}, "time": "2023-10-18T11:20:20+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-24T14:02:46+00:00"}, {"name": "tracy/tracy", "version": "v2.6.8", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "08710e2708a67e360b82e243c09489b2af2e3f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/08710e2708a67e360b82e243c09489b2af2e3f54", "reference": "08710e2708a67e360b82e243c09489b2af2e3f54", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": ">=7.1"}, "require-dev": {"nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.2", "nette/utils": "^2.4 || ^3.0", "psr/log": "^1.0"}, "suggest": {"https://nette.org/donate": "Please support <PERSON> via a donation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"files": ["src/Tracy/shortcuts.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎 Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.6.8"}, "time": "2022-02-15T16:14:57+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.0", "ext-curl": "*", "ext-json": "*", "ext-pdo": "*"}, "platform-dev": [], "platform-overrides": {"php": "8.0"}, "plugin-api-version": "2.6.0"}