{var $title = '<PERSON><PERSON><PERSON><PERSON><PERSON>'}

{block #content}
{form editForm}
<p>{input save class=>"btn btn-primary"}</p>
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>ID</th>
    <th>Originál<br />Překlad</th>
    <th>Poznámka</th>
    <th></th>
  </tr>
  <tr>
    <th colspan="10">Nová položka</th>
    </tr>
    <tr>
      <td></td>
      <td>{$form[data][0]["dicfrom"]->getControl()}<br />{$form[data][0]["dicto_en"]->getControl()}</td>
      <td>{$form[data][0]["dicnote"]->getControl()}</td>
      <td></td>
    </tr>
  {foreach $dataRows as $row}
    <tr>
      <td {if $iterator->isOdd()}class="odd"{/if}>{$row->dicid}</td>
      <td {if $iterator->isOdd()}class="odd"{/if}>{$row->dicfrom}<br />{$form[data][$row->dicid]["dicto_en"]->getControl()}</td>
      <td {if $iterator->isOdd()}class="odd"{/if}><br />{$form[data][$row->dicid]["dicnote"]->getControl()}</td>
      <td {if $iterator->isOdd()}class="odd"{/if}><a href="{plink delete, $row->dicid}">{('delete'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
  <p>{input save class=>"btn btn-primary"}</p>
 {/form}
{/block}
