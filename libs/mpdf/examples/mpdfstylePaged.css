	@page {
		size: 15cm 17cm;	/* width height  <length>{1,2} | auto | portrait | landscape NB 'em' and 'ex' % are not allowed */
		margin: 10%;	/* % of page-box width for LR, height for TB */
		margin-header: 5mm;
		margin-footer: 5mm;
		margin-left: 4cm;
		margin-right: 2cm;
		odd-header-name: myHeader2Odd;
		even-header-name: html_myHTMLHeaderEven;
		odd-footer-name: html_myHTMLFooterOdd;
		even-footer-name: myFooter2Even;
		marks: crop;	 /* crop | cross | none */
	}

	@page :first {
		margin-top: 5cm;    /* Top margin on first page 10cm */
	}

	@page standard {
		size: auto;	/* auto is the default mPDF value */
		margin: 10%;	/* % of page-box width for LR, height for TB */
		marks: none;	 /* crop | cross | none */
	}

	@page standard :first {
		margin-top: 7cm;    /* Top margin on first page 10cm */
	}

	@page bigsquare {
		size: 15cm 20cm;
		margin-left: 4cm;
		marks: crop cross;	 /* crop | cross | none */
		background: transparent url('bg.jpg') repeat scroll 5mm 5mm;  /* position inset by bleedMargin */
	}

	@page bigsquare :right {
		header: html_myHTMLHeaderOdd;
		footer: html_myHTMLFooterOdd;
	}

	@page bigsquare :left {		/* left is what mPDF calls EVEN page - right=ODD */
		header: html_myHTMLHeaderEven;
		footer: html_myHTMLFooterEven;
	}

	@page smallsquare {
		size: 25cm 15cm;
		margin-left: 4cm;
		marks: crop;	 /* crop | cross | none */
		background-gradient: linear #c7cdde #f0f2ff 0 1 0 0.5;
	}

	@page rotated {
		size: landscape;
		marks: none;	 /* crop | cross | none */
		background-color: #fff0f2;
		margin-left: 3cm;
		margin-right: 3cm;
	}
	@page rotated :first {
		margin-top: 7cm;    /* Top margin on first page 10cm */
	}

	h1.heading1 {	color: #1188FF;
	}

	h1.heading2 {	color: #88FF11;
		page-break-before: always;
		page: standard;
	}
	h1.heading3 {	color: #FF1188;
		page-break-before: right;
	}
	h1.heading4 {	color: #FF8811;
		page-break-before: left;
		page: bigsquare;
	}
	h1.heading5 {	color: #11FF88;
		page: smallsquare;
	}
	h1.heading6 {	color: #8811FF;
		page: rotated;
	}

	br.paging { page-break-after: always; }

		body { font-family: DejaVuSansCondensed; font-size: 11pt;  }
		p { 	text-align: justify; margin-bottom: 4pt;  margin-top:0pt; }

		hr {	width: 70%; height: 1px; 
			text-align: center; color: #999999; 
			margin-top: 8pt; margin-bottom: 8pt; }

		a {	color: #000066; font-style: normal; text-decoration: underline; 
			font-weight: normal; }

		ul {	text-indent: 5mm; margin-bottom: 9pt; }
		ol {	text-indent: 5mm; margin-bottom: 9pt; }

		pre { font-family: DejaVuSansMono; font-size: 9pt; margin-top: 5pt; margin-bottom: 5pt; }

		h1 {	font-weight: normal; font-size: 26pt; color: #000066; 
			font-family: DejaVuSansCondensed; margin-top: 18pt; margin-bottom: 6pt; 
			border-top: 0.075cm solid #000000; border-bottom: 0.075cm solid #000000; 
			text-align: ; page-break-after:avoid; }
		h2 {	font-weight: bold; font-size: 12pt; color: #000066; 
			font-family: DejaVuSansCondensed; margin-top: 6pt; margin-bottom: 6pt; 
			border-top: 0.07cm solid #000000; border-bottom: 0.07cm solid #000000; 
			text-align: ;  text-transform: uppercase; page-break-after:avoid; }
		h3 {	font-weight: normal; font-size: 26pt; color: #000000; 
			font-family: DejaVuSansCondensed; margin-top: 0pt; margin-bottom: 6pt; 
			border-top: 0; border-bottom: 0; 
			text-align: ; page-break-after:avoid; }
		h4 {	font-weight: ; font-size: 13pt; color: #9f2b1e; 
			font-family: DejaVuSansCondensed; margin-top: 10pt; margin-bottom: 7pt; 
			text-align: ;  margin-collapse:collapse; page-break-after:avoid; }
		h5 {	font-weight: bold; font-style:italic; ; font-size: 11pt; color: #000044; 
			font-family: DejaVuSansCondensed; margin-top: 8pt; margin-bottom: 4pt; 
			text-align: ;  page-break-after:avoid; }
		h6 {	font-weight: bold; font-size: 9.5pt; color: #333333; 
			font-family: DejaVuSansCondensed; margin-top: 6pt; margin-bottom: ; 
			text-align: ;  page-break-after:avoid; }

