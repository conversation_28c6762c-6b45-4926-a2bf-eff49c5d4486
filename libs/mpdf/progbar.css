body {
	margin : 0 auto;
	width:100%;
	font-family: "Verdana";
	color: #40454b;
	font-size: 12px;
	text-align:center;
}
.main {
	width:540px;
	margin: 0 auto;
	text-align:left;
}
.heading {
	font-size:14px;
	font-weight:bold;
	color:#CC0000;
	padding:5px;
	margin-left:10px;
	border-bottom:solid;
	border-bottom-width:1px;
	border-bottom-color:#333333;
	/* Use this to suppress the horizontal line under the heading */
	/* border-bottom: 0px solid #000000; */
}
table {
	font-family: "Verdana";
	color: #40454b;
	font-size: 12px;
}
.demo {
	margin : 0 auto;
	width:100%;
	margin:20px;
	/* Use this to suppress all the bars and text */
	/* display: none; */
}
td {
	vertical-align: top;
	padding: 0 0.5em 0 0;
}
.code {
	font-family: "Courier New", Courier, monospace;
	font-size: 10px;
}
.code2 {
	font-family: "Courier New", Courier, monospace;
	font-size: 11px; font-weight: bold; color: red;
}
.barheading {
	color:#006600;
	font-weight:bold;
}
.progressBar { 
	border: 1px solid #000000;
	background-color: #EEEEEE;
	width: 200px;
	font-size: 6px; 
}
.innerBar { 
	background-color:#00CC00; 
	width: 0%; 
}
#box3 { 
	font-weight: bold; 
}
