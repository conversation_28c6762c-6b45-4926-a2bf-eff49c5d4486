$(document).ready(function(){
  $(".getBranchesBalikovna").autocomplete({
    source : function(request, response) {
       if (request.term.length >= 2) {
           $.ajax({
               url: basePath + "/kosik/balikovna-ac?q=" + request["term"],
               dataType: "json",
               success: function (data) {
                   response($.map(data.rows, function (item) {
                       return {
                           balid2: item.balid2,
                           balname: item.balname,
                           balstreet: item.balstreet,
                           balcity: item.balcity
                       }
                   }));
               }
           });
       } else {
         //vymazu vybranou pobocku
         $('#orddelspec').val('');
         $('#detail_bal').hide();
         $('#detail_balstreet').text('');
         $('#detail_balcity').text('');
         $('#detail_balurl').attr("href", '');
       }
    },

    focus : function(event, ui) {                   
        $(this).val(ui.item.balname);
        return false;
    },

    select : function(event, ui) {
        $(this).val(ui.item.balname);
        $('#orddelspec').val(ui.item.balid2);
        $('#detail_bal').show();
        $('#detail_balstreet').text(ui.item.balstreet);
        $('#detail_balcity').text(ui.item.balcity);
        $('#detail_balid2').text(ui.item.balid2);
        $('#detail_balurl').attr("href", 'https://www.postaonline.cz/detail-pobocky/-/pobocky/detail/' + ui.item.balid2);
        return false;
    }
  }).each(function() {
    $(this).data('ui-autocomplete')._renderItem = function(ul, item) {
    return $("<li></li>").data("ui-item-autocomplete", item).append(
                item.balcity + ", " + item.balstreet + ",  " + item.balid2)
                .appendTo(ul);  
    };
  });
});