<?php
namespace AdminModule;

use Model\CatalogsModel;
use <PERSON><PERSON>,
  dibi,
  <PERSON>;
use <PERSON>\Debugger;

final class WidexImportPresenter extends BasePresenter
{

  /** @var \PDO */
  private $msCn = null;

  protected function startup() {
    parent::startup();

    $options = $this->neonParameters["mssql"];

    try {
      $this->msCn = new \PDO('dblib:charset=WINDOWS-1250;host=***********:1433;dbname=Widex2006', $options["username"], $options["password"]);
    } catch (\Exception $e) {
      die($e->getMessage());
    }

  }

  public function renderDefault()
  {
    $this->template->rows = $this->getRows();
  }

  private function getRows()
  {
    $sql = "select * from HPCeShopData";
    return $this->msSqlFetchAll($sql);
  }

  public function actionImport() {
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') {
      $this->terminate();
    }

    //nactu si datum a cas začátku aktualizace
    $start = dibi::fetchSingle("SELECT NOW()");

    $cats = new CatalogsModel();

    $cntNew = 0;
    $cntUpd = 0;

    //offset pro ID podskupiny
    $subcatoff = 10000;
    //offset pro ID 3. podskupiny
    $subcat1off = 20000;

    //naliju hlavni uroven katalogu
    $sql = '
    SELECT IdSkupiny, NazevSkupiny
    FROM HPCeShopData
    GROUP BY IdSkupiny, NazevSkupiny';
    $rows = $this->msSqlFetchAll($sql);

    foreach ($rows as $row) {
      $cat = $cats->load($row["IdSkupiny"]);
      $vals = array(
        'catmasid' => 0,
        'catname' => $this->toUtf8($row["NazevSkupiny"]),
        'catstatus' => 0,
      );
      if ($cat) {
        $cats->update($cat->catid, $vals, true, false);
        //echo $cat->catid . "-u1<br>";
        $cntUpd++;
      } else {
        $vals["catid"] = $row["IdSkupiny"];
        $catid = $cats->insert($vals, false);
        $cntNew++;
        //echo $catid . "-i1<br>";
      }
    }

    //naliju druhou úroveň katalogu
    $sql = '
      SELECT IdPodskupiny, NazevPodskupiny, IdSkupiny
      FROM HPCeShopData
      GROUP BY IdPodskupiny, NazevPodskupiny, IdSkupiny';

    $rows = $this->msSqlFetchAll($sql);
    foreach ($rows as $row) {
      $catid = $row["IdPodskupiny"] + $subcatoff;
      $cat = $cats->load($catid);
      $vals = array(
        'catmasid' => $row["IdSkupiny"],
        'catname' => $this->toUtf8($row["NazevPodskupiny"]),
        'catstatus' => 0,
      );
      if ($cat) {
        $cats->update($cat->catid, $vals, true, false);
        //echo $cat->catid . "-u2<br>";
        $cntUpd++;
      } else {
        $vals["catid"] = $catid;
        $catid = $cats->insert($vals, false);
        $cntNew++;
        //echo $catid . "-i2<br>";
      }
    }

    //naliju třetí úroveň katalogu
    $sql = 'SELECT IdPodskupiny1, NazevPodskupiny1, IdPodskupiny
FROM HPCeShopData
WHERE IdPodskupiny1 > 0
GROUP BY IdPodskupiny1, NazevPodskupiny1, IdPodskupiny';

    $rows = $this->msSqlFetchAll($sql);
    foreach ($rows as $row) {
      $catid = $row["IdPodskupiny1"] + $subcat1off;
      $cat = $cats->load($catid);
      $vals = array(
        'catmasid' => $row["IdPodskupiny"] + $subcatoff,
        'catname' => $this->toUtf8($row["NazevPodskupiny1"]),
        'catstatus' => 0,
      );
      if ($cat) {
        $cats->update($cat->catid, $vals, true, false);
        //echo $cat->catid . "-u3<br>";
        $cntUpd++;
      } else {
        $vals["catid"] = $catid;
        $catid = $cats->insert($vals, false);
        $cntNew++;
        //echo $catid . "-i3<br>";
      }
    }
    $cats->rebuildPaths();

    echo "Nových kategorií: $cntNew<br>";
    echo "Upravených kategorií: $cntUpd<br>";

    //vymazu tabulku se zarazenim do katalogu
    dibi::query("TRUNCATE catplaces");

    $cntNew = 0;
    $cntUpd = 0;
    $cnt = 0;

    $sql = 'SELECT * FROM HPCeShopData';

    $pros = new \Model\ProductsModel();
    $caps = new \Model\CatPlacesModel();
    $rows = $this->msSqlFetchAll($sql);

    foreach ($rows as $row) {
      $cnt++;
      $pro = $pros->load($row["IdProduktu"], 'code');

      $vatId = 0;
      if ($row["DPH"] == 0.12) {
        $vatId = 1;
      }
      if ($row["DPH"] == 0.10) {
        $vatId = 2;
      }

      $vals = [
        'procode' => $row["IdProduktu"],
        'proname' => $this->toUtf8($row["NazevProduktu"]),
        'proprice1a' => round($row["Cena"] * (1 + $row["DPH"])),
        'proprice1b' => round($row["CenaB"] * (1 + $row["DPH"])),
        'provatid' => $vatId,
        'prostatus' => 0,
        'proaccess' => (int)$row["Mnozstvi"] > 0 ? 0 : 100, //skladem
        'proqty' => (int)$row["Mnozstvi"] //skladem počet kusů
      ];
      $updateCatRootId = FALSE;
      if ($pro) {
        $proid = $pro->proid;
        $pros->update($proid, $vals, true);
        //echo $proid . "-up[".$row["IdProduktu"]."]<br>";
        $cntUpd++;
      } else {
        $vals["protypid2"] = 1; //novinka
        $vals["promanid"] = 1; //výrobce
        $proid = $pros->insert($vals);
        $cntNew++;
        //echo $proid . "-ip[".$row["IdProduktu"]."]<br>";
      }
      $idskup = 0;
      if ($row["IdPodskupiny1"] > 0) {
        $idskup = $row["IdPodskupiny1"] + $subcat1off;
      } else {
        $idskup = $row["IdPodskupiny"] + $subcatoff;
      }
      if ($idskup > 0) {
        $cvals = array(
          'capproid' => $proid,
          'capcatid' => $idskup,
        );
        $caps->insert($cvals);
        $updateCatRootId = TRUE;
      }
      //aktualizuji catrootid
      if ($updateCatRootId) {
        $pro = $pros->load($proid);
        $pros->setProCatRootId($pro);
      }
    }
    //skryji polozky, ktere nejsou aktualizovany behem importu ale jiz existuji v databazi
    $sql = "UPDATE products SET prostatus=1 WHERE coalesce(prodateu, prodatec) < '$start'";
    dibi::query($sql);
    $sql = "UPDATE catalogs SET catstatus=1 WHERE coalesce(catdateu, catdatec) < '$start'";
    dibi::query($sql);
    echo "Nových produktů: $cntNew<br>";
    echo "Upravených produktů: $cntUpd<br>";
    $this->terminate();
  }

  private function msSqlFetchAll($sql) {
    $statement = $this->msCn->prepare($sql);
    $statement->execute();
    return $statement->fetchAll();
  }
}
