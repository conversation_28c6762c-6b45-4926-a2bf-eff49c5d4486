<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ArticlesModel extends BaseModel {

  protected $tableName = "articles";
  protected $fieldPrefix = "art";

  public function runCounter($id) {
    return dibi::query("UPDATE articles SET artcnt=artcnt+1 WHERE artid=%i", $id);
  }

  /********************* ciselniky *********************/
  /**
  * ciselnik typy <PERSON>án<PERSON>ů
  * @return array
  */
  public function getEnumArtTypId() {
    return array(
      1 => 'Novinky',
      2 => 'Odborn<PERSON>',

    );
  }

  public function getEnumArtTop() {
    return array(
      0 => 'žádné',
      1 => 'Odborné č<PERSON>án<PERSON>',
      2 => 'Proč nakupovat u nás',
    );
  }

  public function getEnumArtStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'B<PERSON>kovaný',
    );
  }
}
