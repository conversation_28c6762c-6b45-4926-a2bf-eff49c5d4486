<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ProductPricesModel extends BaseModel {

  protected $tableName = "product_prices";
  protected $fieldPrefix = "pra";


  public function delete($id) {
    //zjistim jestli da<PERSON> akce je platná
    $pra = $this->load($id);
    if ($pra->prastatus == 1) $this->deactivate($pra);
    return parent::delete($id);
  }

  /**
   * @param $product_price \dibi\Row
   */
  public function activate($product_price) {
    //zjistím soucasnou cenu
    $pros = new ProductsModel();
    $priceField = "proprice".$product_price->pracurid.$product_price->praprccat;
    $proPriceBefore = (double)dibi::fetchSingle("SELECT $priceField FROM products WHERE proid=%i", $product_price->praproid);
    if ($proPriceBefore > 0) {
      $this->update($product_price->praid, array("prapricebefore"=>$proPriceBefore, "prastatus"=>1));
      $vals = array(
        $priceField => $product_price->praprice,
      );
      for ($i = 1; $i <= 5; $i++) {
        $sufix = $i;
        if ($i == 1) $sufix = "";
        if ($product_price["pratypid".$sufix] == 1) $vals["protypid".$sufix] = 1;
      }
      $pros->update($product_price->praproid, $vals);
    } else {
      return false;
    }
  }

  /**
   * @param $product_price \dibi\Row
   */
  public function deactivate($product_price) {
    //zjistím soucasnou cenu
    $pros = new ProductsModel();
    $priceField = "proprice".$product_price->pracurid.$product_price->praprccat;
    if ((double)$product_price->prapricebefore > 0) {
      $this->update($product_price->praid, array("prastatus"=>2));

      $vals = array(
        $priceField => $product_price->prapricebefore,
      );
      for ($i = 1; $i <= 5; $i++) {
        $sufix = $i;
        if ($i == 1) $sufix = "";
        if ($product_price["pratypid".$sufix] == 1) $vals["protypid".$sufix] = 0;
      }

      $pros->update($product_price->praproid, $vals);
    } else {
      return false;
    }
  }

  /**
   * číselní prastatus
   * @return array
   */
  public function getEnumPraStatus() {
    return array(
      0 => 'Čekající',
      1 => 'Aktivní',
      2 => 'Ukončeno',
    );
  }
}
