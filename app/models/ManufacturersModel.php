<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ManufacturersModel extends BaseModel {

  protected $tableName = "manufacturers";
  protected $fieldPrefix = "man";

  /********************* ciselniky *********************/

  /**
  * ciselnik manstatus
  * @return array
  */
  public function getEnumManStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  /**
  * ciselnik usrstatus
  * @return array
  */
  public function getEnumManId() {
    return dibi::query("SELECT manid, manname FROM manufacturers WHERE manstatus=0")->fetchPairs('manid', 'manname');
  }
}
