<?php
namespace AdminModule;

use <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>;
use <PERSON>\Debugger;

final class ProductPresenter extends BasePresenter {

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sCode2 = '';

  /** @persistent */
  public $sCodeP = '';

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sCatId = '';

  /** @persistent */
  public $sManId = '';

  /** @persistent */
  public $sTypId = Null;
  /** @persistent */
  public $sTypId2 = Null;
  /** @persistent */
  public $sTypId3 = Null;
  /** @persistent */
  public $sTypId4 = Null;
  /** @persistent */
  public $sTypId5 = Null;

  /** @persistent */
  public $sStatus = 0;

  /** @persistent */
  public $sOrderBy = 'pro.procode';

  /** @persistent */
  public $sOrderByType = 'ASC';

  public function productEditFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $product = new \Model\ProductsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();

      //pokud nova polozka a neni vyplneny kod zbozi tak predplnim
      if (empty($vals["procode"]) AND ($id == 0 || $form['saveAsNew']->isSubmittedBy())) {
        $procode = (int)dibi::fetchSingle("SELECT procode FROM products ORDER BY CAST(procode AS UNSIGNED) DESC");
        $vals["procode"] = $procode + 1;
      }
      $digits1 = $this->neonParameters["currency"][1]["decimals"];
      $vals["proprice1com"] = round((double)$vals["proprice1com"], $digits1);
      $vals["proprice1a"] = round((double)$vals["proprice1a"], $digits1);
      $vals["proprice1b"] = round((double)$vals["proprice1b"], $digits1);
      $vals["proprice1c"] = round((double)$vals["proprice1c"], $digits1);
      $vals["proprice1d"] = round((double)$vals["proprice1d"], $digits1);
      if ($this->secondCurrency) {
        $digits2 = $this->neonParameters["currency"][2]["decimals"];
        $vals["proprice2com"] = round((double)($vals["proprice1com"] / $this->config["PRICE2RATE"]), $digits2);
        $vals["proprice2a"] = round((double)($vals["proprice1a"] / $this->config["PRICE2RATE"]), $digits2);
        $vals["proprice2b"] = round((double)($vals["proprice1b"] / $this->config["PRICE2RATE"]), $digits2);
        $vals["proprice2c"] = round((double)($vals["proprice1c"] / $this->config["PRICE2RATE"]), $digits2);
        $vals["proprice2d"] = round((double)($vals["proprice1d"] / $this->config["PRICE2RATE"]), $digits2);
      }
      $copyPrice = (bool)$vals["copyprice"];

      //vezmu si obrazek pokud byl odeslan
      $image0 = null;
      $images = null;
      $image_usrsize = null;
      $image1 = null;
      $file1 = null;
      if ($vals["pic0"]->isOk()) {
        $image0 = $vals["pic0"]->toImage();
      }

      if ($vals["pic_usrsize"]->isOk()) {
        $image_usrsize = $vals["pic_usrsize"]->toImage();
      }
      if ($vals["pic1"]->isOk()) {
        $image1 = $vals["pic1"]->toImage();
        $image1_position = $vals["pic1position"];
      }
      if ($vals["attAdd"]->isOk()) {
        $file1 = $vals["attAdd"];
        $filedesc = $vals["ataname"];
      }

      if (!empty($vals->mastercode)) {
        $pi = (int)dibi::fetchSingle("SELECT proid FROM products WHERE procode=%s", $vals->mastercode);
        $vals->promasid = $pi;
      } else {
        $vals->promasid = 0;
      }
      unset($vals->mastercode);
      unset($vals["copyprice"]);

      //hlidaci pes
      $sendWatchDogPrice = false;
      $sendWatchDogStore = false;
      if (isset($vals->watchdog_price)) $sendWatchDogPrice = ($vals->watchdog_price == 1);
      if (isset($vals->watchdog_store)) $sendWatchDogStore = ($vals->watchdog_store == 1);

      unset($vals->watchdog_price);
      unset($vals->watchdog_store);

      //pokud vyplnil slevu
      $discountData = [];
      if (isset($vals->disqty) && $vals->disqty > 1 && isset($vals->disdiscount) && $vals->disdiscount > 0) {
        $discountData["disqty"] = $vals->disqty;
        $discountData["disdiscount"] = $vals->disdiscount;
      }
      unset($vals->disqty);
      unset($vals->disdiscount);

      //pokud neni vyplneny nazev obrazku, vyplnim kod zbozi
      if (empty($vals["propicname"])) $vals["propicname"] = $vals["procode"];

      //zapamatuji si příslušenství
      $accessories = $vals->accessories;
      unset($vals->vals->accessories);
      $accessoriesNew = $vals->accessories_new;
      unset($vals->vals->accessories_new);

      //parametry zbozi
      $proParam = array();
      if (!empty($vals["params_edit"])) {
        $pars = $vals["params_edit"];
        foreach ($pars as $key => $item) {
          $prdid = (!empty($item->param_prdid) ? $item->param_prdid : NULL);
          $paramname = (!empty($item->param_prdid) ? NULL : $item->param_name);
          if (!empty($item->param_value)) $proParam[$key] = array('prpprdid' => $prdid, 'prpname' => $paramname, 'prpvalue' => $item->param_value);
        }
        //novy parametr - nova hodnota
        if (!empty($vals["param_name_0"]) || !empty($vals["param_prdid_0"])) {
          $prdid = (!empty($vals["param_prdid_0"]) ? $vals["param_prdid_0"] : NULL);
          if (!empty($vals["param_value_0"])) $proParam[0] = array('prpprdid' => $prdid, 'prpname' => $vals["param_name_0"], 'prpvalue' => $vals["param_value_0"]);
        }
      }
      unset($vals["params_edit"]);
      unset($vals["param_name_0"]);

      //cenove akce
      $proPrices = array();
      if (!empty($vals["product_prices"])) {
        $proPrices = $vals["product_prices"];
        unset($vals["product_prices"]);
      }

      //projdu formularova pole a ktere nejsou treba odstranim
      $catIds = array();
      if (!empty($vals["catPlaces"])) {
        foreach ($vals["catPlaces"] as $key => $value) {
          if (substr($key, 0, 4) === 'cat_' && $value) {
            $catIds[] = substr($key, 4);
          }
        }
      }
      foreach ($vals as $key => $value) {
        //zarazeni do katalogu
        if (substr($key, 0, 4) === 'cat_') {
          if (($key == 'cat_0' || $key == 'cat_') && $value > 0) {
            //nove zarazeni
            $catIds[] = $value;
          } else if ($value) {
            //stavajici zarazeni
            $catIds[] = substr($key, 4);
          }
          unset($vals[$key]);
        }
        if (substr($key, 0, 3) != "pro") unset($vals[$key]);
      }

      //pokud se ma ulozit jako nova polozka
      $saveAsNewSrcProId = 0;
      if ($id > 0) {
        if ($form['saveAsNew']->isSubmittedBy()) {
          $saveAsNewSrcProId = $id;
          $id = 0;
        }
      }
      $isnew = ($id == 0);
      try {
        if ($product->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');

          //upravim a ulozim hlavni a doplnkovy obrazek
          $sizes = (array(
            $this->config["PROPICSIZE_BIG"].'xbig',
            $this->config["PROPICSIZE_DETAIL"].'xdetail',
            $this->config["PROPICSIZE_LIST"].'xlist',
            $this->config["PROPICSIZE_SMALL"].'xsmall',
            '50x50xsize2'
          ));
          if (isset($image0)) $this->saveImage($image0, WWW_DIR."/pic/product", $vals["propicname"].".jpg", $sizes);
          if (isset($images)) $this->saveImage($images, WWW_DIR."/pic/product", $vals["propicname"].".jpg", array('200x284xslider'));
          if (isset($image_usrsize)) $this->saveImage($image_usrsize, WWW_DIR."/pic/product", $vals["propicname"].".jpg", array('40x40xusrsize'));
          if (isset($image1)) $this->saveImage($image1, WWW_DIR."/pic/product", $vals["propicname"]."_".$image1_position.".jpg", $sizes);
          //ulozim prilohu
          if (isset($file1)) {
            //ulozim do db
            $atts = new \Model\AttachmentsModel();
            $ataVals = array(
              'ataproid'=>$id,
              'ataname'=>$filedesc,
              'atafilename'=>Nette\Utils\Strings::webalize($filedesc).'_'.$id.'.'.substr($file1->getName(), -3),
              'atasize'=>(string)$file1->getSize(),
              'atatype'=>substr($file1->getName(), -3),
            );
            $atts->insert($ataVals);
            $file1->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);
          }
          //zpracovani zarazeni do katalogu
          if ($id > 0) $product->updateCatPlace($id, $catIds);

          //zpracování příslušenství
          $acss = new \Model\ProAccessModel();
          foreach ($accessoriesNew as $code) {
            if (empty($code)) continue;

            $acs = $product->load($code, "code");
            if ($acs) {
              //zjistim, jestli uz neni ulozeny
              $cnt = (int)dibi::fetchSingle("SELECT COUNT(acsid) FROM proaccess WHERE acsproid=%i", $id, " AND acsacsproid=%i", $acs->proid);
              if ($cnt == 0) {
                $acss->insert(array(
                  "acsproid" => $id,
                  "acsacsproid" => $acs->proid
                ));
              }
            }
          }
          foreach ($accessories as $key => $value) {
            if ($value === false) {
              //vymazu prislusko
              $arr = explode("_", $key);
              if(!empty($arr[1])) {
                dibi::query("DELETE FROM proaccess WHERE acsproid=%i", $id, " AND acsacsproid=%i", $arr[1]);
              }
            }
          }

          //zpracovani parametru
          $proparams = new \Model\ProParamsModel();
          if ($saveAsNewSrcProId > 0) {
            $params = dibi::fetchAll("SELECT * FROM proparams WHERE prpproid=%i", $saveAsNewSrcProId, " ORDER BY prpid");
            foreach ($params as $row) {
              unset($row->prpid);
              unset($row->prpdatec);
              unset($row->prpdateu);
              $row->prpproid = $id;
              $proparams->insert($row);
            }
          } else {
            foreach ($proParam as $key => $value) {
              if (substr($key, 0, 1) == 'N') $key = 0;
              if (!empty($value["prpvalue"])) $proparams->save($key, array('prpproid' => $id,'prpprdid' => $value["prpprdid"], 'prpname' => $value["prpname"], 'prpvalue' => $value["prpvalue"]));
            }
          }

          //zpracuji cenove akce
          $pras = new Model\ProductPricesModel();
          foreach ($proPrices as $praid => $data) {
            $praid = (int)$praid;
            if ($praid == 0 && empty($data->pradatefrom)) continue;
            $data->pradatefrom = $this->formatDateMySQL($data->pradatefrom);
            $data->pradateto = $this->formatDateMySQL($data->pradateto);
            if ($praid > 0) {
              $pras->update($praid, $data);
            } else {
              $pras->insert($data);
            }
          }

          //splacování slevy
          if (count($discountData) > 0) {
            $discountData["disproid"] = $id;
            $diss = New Model\ProductDiscountsModel();
            $diss->insert($discountData);
          }

          //prilohy
          if ($saveAsNewSrcProId > 0) {
            $files = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $saveAsNewSrcProId);
            $ata = new \Model\AttachmentsModel();
            foreach ($files as $row) {
              unset($row->ataid);
              unset($row->atadatec);
              unset($row->atadateu);
              $row->ataproid = $id;
              $ata->insert($row);
            }
          }

          //aktualizace podrizenych polozek
          dibi::query("
            UPDATE products SET
            prodelfree=%i", $vals["prodelfree"], ",
            pronotdisc=%f", $vals["pronotdisc"], "
            WHERE promasid=%i", $id
          );
          if ($copyPrice) {
            dibi::query("
              UPDATE products SET
              proprice1com=%f", $vals["proprice1com"], ",
              proprice1a=%f", $vals["proprice1a"], ",
              proprice1b=%f", $vals["proprice1b"], ",
              proprice1c=%f", $vals["proprice1c"], ",
              proprice1d=%f", $vals["proprice1d"], ",
              proprice2com=%f", (isset($vals["proprice2com"]) ? $vals["proprice2com"] : 0) , ",
              proprice2a=%f", (isset($vals["proprice2a"]) ? $vals["proprice2a"] : 0), ",
              proprice2b=%f", (isset($vals["proprice2b"]) ? $vals["proprice2b"] : 0), ",
              proprice2c=%f", (isset($vals["proprice2c"]) ? $vals["proprice2c"] : 0), ",
              proprice2d=%f", (isset($vals["proprice2d"]) ? $vals["proprice2d"] : 0), "
              WHERE promasid=%i", $id
            );
          }


          //odmailuju hlidaciho psa
          if ($sendWatchDogPrice) {
            //mailovani, ze zbozi je levnejsi
            $rows = dibi::query("
              SELECT proid, concat(proname, ' ', proname2) AS proname, procode, prokey,
              proprice".$this->curId."a AS propricea,
              proprice".$this->curId."b AS propriceb,
              proprice".$this->curId."c AS propricec,
              proprice".$this->curId."d AS propriced,
              dogmail FROM watchdogs
              INNER JOIN products ON (proid=dogproid AND proaccess=0 AND prostatus=0)
              WHERE dogprice=1 AND dogproid=%i", $id
            )->fetchAssoc("dogmail,proid");

            $mailTemplate = $this->createTemplate();
            $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailWatchDogPrice.latte');
            $cnt = 0;
            foreach ($rows as $products) {
              //odmailuju
              if (count($products) > 0) {
                $product = current($products);
                if (isset($product)) {
                  $mailTemplate->products = $products;
                  $this->mailSend($product->dogmail, "Zboží co Vás zajímá jsme zlevnili", $mailTemplate);
                  //vymazu hlidaciho psa
                  $arr = array_keys($products);
                  $ids = implode(',', $arr);
                  $ids = trim($ids, ',');
                  dibi::query("DELETE FROM watchdogs WHERE dogprice=1 AND dogmail=%s", $product->dogmail, " AND dogproid IN ($ids)");
                  $cnt++;
                }
              }
            }
            $this->flashMessage("Mailování o změně ceny bylo provedeno. Odesláno $cnt emailů.");
          }
          if ($sendWatchDogStore) {
            //mailovani, ze zbozi je skladem
            $rows = dibi::query("
              SELECT proid, concat(proname, ' ', proname2) AS proname, procode, prokey, proaccess,
              proprice".$this->curId."a AS propricea,
              proprice".$this->curId."b AS propriceb,
              proprice".$this->curId."c AS propricec,
              proprice".$this->curId."d AS propriced,
              dogmail FROM watchdogs
              INNER JOIN products ON (proid=dogproid AND proaccess=0 AND prostatus=0)
              WHERE dogstore=1 AND dogproid=%i", $id
            )->fetchAssoc("dogmail,proid");

            $mailTemplate = $this->createTemplate();
            $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailWatchDogStore.latte');
            $cnt = 0;
            foreach ($rows as $products) {
              //odmailuju
              if (count($products) > 0) {
                $product = current($products);
                if (isset($product)) {
                  $mailTemplate->products = $products;
                  $this->mailSend($product->dogmail, "Zboží co Vás zajímá jsme naskladnili", $mailTemplate);
                  //vymazu hlidaciho psa
                  $arr = array_keys($products);
                  $ids = implode(',', $arr);
                  $ids = trim($ids, ',');
                  dibi::query("DELETE FROM watchdogs WHERE dogstore=1 AND dogmail=%s", $product->dogmail, " AND dogproid IN ($ids)");
                  $cnt ++;
                }
              }
            }
            $this->flashMessage("Mailování o změně dostupnosti bylo provedeno. Odesláno $cnt emailů.");
          }

          //redirect podle tlacitka
          $btn = $form->isSubmitted();
          $btn_name = $btn->name;

          if ($isnew) $btn_name = "newitem";
          switch ($btn_name) {
             case 'save':
               $this->redirect("default");
               break;
             case 'newitem':
               $this->redirect("edit", $id);
               break;
             case 'saveAsNew':
              $this->redirect("this");
               break;
             default:
               $this->redirect("edit#".$btn_name, array('id'=>$id));
               break;
          }
        }
      } catch (\Model\ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  public function productListEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {

      $products = new \Model\ProductsModel();
      $vals = $form->getValues();
      foreach ($vals as $id => $item) {

        if ($item['proaccess']!="" || $item['procode2']!="" || $item['procodep']!=""  || $item['prostatuschange'] || $item['proorder']!="") {
          if ($item['proaccess']=="") unset($item['proaccess']);
          if ($item['procode2']=="") unset($item['procode2']);
          if ($item['procodep']=="") unset($item['procodep']);
          if ($item['proorder']=="") unset($item['proorder']);
          if ($item['prostatuschange']) {
            $item['prostatus'] = ($item['prostatus'] == 1 ? 0 : 1);
          } else {
            unset($item['prostatus']);
          }
          unset($item['prostatuschange']);
          if (count($item) > 0) $products->update($id, $item);
        }
      }
      $this->redirect('default');
    }
  }

  public function productBatchUpdateFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $proPrice = (int)$vals["proprice"];
      $prcCat = $vals["prcid"];
      $sql = "UPDATE products SET proprice$prcCat=".$proPrice." WHERE proid IN (SELECT capproid FROM catplaces INNER JOIN catalogs ON (capcatid=catid) WHERE capcatid=".$vals["catid"]." AND catpathids LIKE '|".$vals["catid"]."|%')";
      if (dibi::query($sql)) {
          $this->flashMessage("Ceny byly aktualizovány");
         $this->redirect('this');
      } else {
        $form->addError("Nastala chyba při aktualizaci ceny");
      }

    }
  }


  /********************* view default *********************/

  private function getDataSource() {
    $product = new \Model\ProductsModel();

    $where = "";
    if (!empty($this->sCode)) $where .= " pro.procode LIKE '%$this->sCode%' AND ";
    if (!empty($this->sCode2)) $where .= " pro.procode2 LIKE '%$this->sCode2%' AND ";
    if (!empty($this->sCodeP)) $where .= " pro.procodep LIKE '%$this->sCodeP%' AND ";
    if (!empty($this->sName)) $where .= " pro.proname LIKE '%$this->sName%' AND ";
    if (!empty($this->sCatId)) $where .= " pro.proid IN (
      SELECT capproid
      FROM catplaces
      INNER JOIN catalogs ON (catid=capcatid)
      WHERE catpathids LIKE '%|$this->sCatId|%'
    ) AND ";
    if (!empty($this->sManId)) $where .= " pro.promanid=$this->sManId AND ";
    if (!empty($this->sTypId)) $where .= " pro.protypid=$this->sTypId AND ";
    if (!empty($this->sTypId2)) $where .= " pro.protypid2=$this->sTypId2 AND ";
    if (!empty($this->sTypId3)) $where .= " pro.protypid3=$this->sTypId3 AND ";
    if (!empty($this->sTypId4)) $where .= " pro.protypid4=$this->sTypId4 AND ";
    if (!empty($this->sTypId5)) $where .= " pro.protypid5=$this->sTypId5 AND ";
    if ((int)$this->sStatus > -1) $where .= " IF(pro.prostatus=1,pro.prostatus,COALESCE(promas.prostatus,pro.prostatus))=$this->sStatus AND ";

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }
    if (empty($this->sOrderBy)) $this->sOrderBy = "pro.procode";
    $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;

    return $product->getDataSource("
      SELECT pro.proid, pro.promasid, pro.proismaster, pro.procode, pro.procode2, pro.procodep, pro.proname, pro.proname2, IF(pro.prostatus=1,pro.prostatus,COALESCE(promas.prostatus,pro.prostatus)) AS prostatus, pro.proprice1a, manname, pro.proaccess, pro.proorder,
        coalesce(prscnt, 0) AS prscnt, pro.probigsize, pro.prooffer
        FROM products AS pro
        LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
        LEFT JOIN manufacturers ON (pro.promanid=manid)
        LEFT JOIN products_salestat ON (prsproid=pro.proid AND prsperiod=30)
        $where
        $orderBy");
  }

  public function renderDefault($level = 0) {
    $that = $this;

    $dataSource = $this->getDataSource();
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    //ciselnik statusu
    $product = new \Model\ProductsModel();
    $this->template->enum_prostatus = $product->getEnumProStatus();

    //polozky co nejsou v katalogu
    $this->template->prosNotInCatalog = array(); //= dibi::fetchAll("SELECT * FROM products WHERE NOT EXISTS (SELECT 1 FROM catplaces WHERE capproid=proid)");

    // Define event for example to redraw snippets
		$this['paginator']->onShowPage[] = (function ($component, $page) use ($that) {
			if ($that->isAjax()){
				$that->redrawControl("table-data");
			}
		});
  }

  public function actionCalculatePrice2() {
    if ($this->secondCurrency) {
      $digits = (int)$this->neonParameters["currency"][2]["decimals"];
      $rate = (double)$this->config["PRICE2RATE"];
      dibi::query("
        UPDATE products SET
        proprice2com = ROUND(proprice1com / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1com IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET
        proprice2a = ROUND(proprice1a / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1a IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET
        proprice2b = ROUND(proprice1b / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1b IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET
        proprice2c = ROUND(proprice1c / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1c IS NOT NULL
      ");
      dibi::query("
        UPDATE products SET
        proprice2d = ROUND(proprice1d / ".$rate."*IF(promanid=1,1.05,1), ".$digits.")
        WHERE proprice1d IS NOT NULL
      ");
      $this->flashMessage("Ceny byly přepočítány aktuálním kurzem.");
      $pros = new \Model\ProductsModel();
      $pros->cacheClean();
      $this->redirect("default");
    }
  }

  public function renderEdit($id) {
    $form = $this['productEditForm'];
    $dataRow = false;
    $this->template->subItems = false;
    $product = new \Model\ProductsModel();
    if ($id > 0) {
      $dataRow = dibi::fetch("SELECT * FROM products WHERE proid=%i", $id);
      if (!$dataRow) throw new Nette\Application\BadRequestException('Záznam nenalezen');
      //pokud je promasid > 0 doplnim kod
      if ($dataRow->promasid > 0) {
        $dataRow->mastercode = dibi::fetchSingle("SELECT procode FROM products WHERE proid=%i", $dataRow->promasid);
      }
      $form->setDefaults($dataRow);
      $this->template->subItems = dibi::fetchAll("SELECT * FROM products WHERE promasid=%i", $id);
      $this->template->enum_prostatus = $product->getEnumProStatus();
      $this->template->enum_proaccess = $product->getEnumProAccess();
    }
    $this->template->id = $id;
    //obrazky
    $emptypos = 0;
    if ($id > 0) {
      //nactu seznam obrazku
      $images = array();

      for($piccnt=0;$piccnt<=10;$piccnt++){
        if ($piccnt == 0) {
          $title = "Hlavní obrázek";
          $filename = $dataRow->propicname.'.jpg';
        }  else {
          $title = $piccnt.". pozice";
          $filename = $dataRow->propicname.'_'.$piccnt.'.jpg';
        }

        if (file_exists(WWW_DIR.'/pic/product/list/'.$filename)) {
          $images[$piccnt] = array("title"=>$title, 'filename'=>$filename);
        } else {
          if (empty($emptypos)) $emptypos = $piccnt;
        }
      }
      //obrazek slider
      $filename = $dataRow->propicname.'.jpg';
      if (file_exists(WWW_DIR.'/pic/product/slider/'.$filename)) {
        $images[11] = array("title"=>"Obrázek pro slider", 'filename'=>$filename);
      }
      $this->template->images = $images;
      $this->template->imageEmptyPos = $emptypos;

      //doplnim seznam produktu ktere maji stejny nazev obrazku
      $duplImg = "";
      if ($dataRow) {
        $this->template->proSamePicNames = dibi::fetchAll("SELECT * FROM products WHERE propicname=%s", $dataRow->propicname, " AND proid!=%i", $dataRow->proid);
      }
      if (!empty($duplImg)) $duplImg = "<p><strong>Nalezena duplicita názvů obrázků:</strong><br />".$duplImg."</p>";


      //načtu slevy
      $this->template->discounts = $product->getDiscounts($id);
    }

    //prilohy
    $this->template->duplicityProNames = array();
    $this->template->duplicityProNameSs = array();
    if ($id > 0) {
      //nactu aktualni prilohy
      $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $id);

      //doplnim duplicity
      $this->template->duplicityProNames = dibi::fetchAll("SELECT proid, proname FROM products WHERE proid!=%i", $dataRow->proid, " AND proname=%s", $dataRow->proname);
      if (!empty($dataRow->pronames)) {
        $this->template->duplicityProNameSs = dibi::fetchAll("SELECT proid, proname, pronames FROM products WHERE proid!=%i", $dataRow->proid, " AND pronames=%s", $dataRow->pronames);
      }
    }
    $this->template->duplicityEans = array();
    $this->template->eanCheck = "";
    if (!empty($dataRow->procode2)) {
      $this->template->duplicityEans = dibi::fetchAll("SELECT proid, procode2 FROM products WHERE proid!=%i", $dataRow->proid, " AND prostatus=0 AND COALESCE(procode2, '') != '' AND procode2=%s", $dataRow->procode2);
      $this->template->eanCheck = $this->checkEan13($dataRow->procode2);
    }

    $this->template->proGiftsList = array();
    if (!empty($dataRow->progifts)) {
      $arr = explode(',', trim($dataRow->progifts, ','));
      $this->template->proGiftsList = dibi::fetchAll("SELECT proid, proname FROM products WHERE procode IN (%s)", $arr);
    }

    $this->template->dataRow = $dataRow;

  }

  public function actionDeleteParam($proid, $parid) {
    if ($parid > 0) {
      $proParam = new \Model\ProParamsModel();
      $proParam->delete($parid);
      $this->flashMessage('Parametr byl vymazán');
    }
    $this->redirect('Product:edit#tabs_param', $proid);
  }

  public function actionDeleteDiscount($disId, $proId) {
    if ($disId > 0) {
      $diss = new \Model\ProductDiscountsModel();
      $diss->delete($disId);
      $this->flashMessage('Sleva byla vymazána');
    }
    $this->redirect('Product:edit#tabs_discount', $proId);
  }

  public function actionDeleteProductPrice($proid, $praid) {
    if ($praid > 0) {
      $pras = new \Model\ProductPricesModel();
      $pras->delete($praid);
      $this->flashMessage('Parametr byl vymazán');
    }
    $this->redirect('edit#tabs_product_prices', $proid);
  }

  public function actionRecalcSaleStat() {
    $products = new \Model\ProductsModel();
    $products->recalcSaleStat();
    $this->flashMessage("Statistika prodejnosti byla přepočítána");
    $this->redirect('Admin:default');
  }

  public function actionDeleteImage($proid, $filename, $path="") {
    if ($path == "") {
      @unlink(WWW_DIR.'/pic/product/list/'.$filename);
      @unlink(WWW_DIR.'/pic/product/detail/'.$filename);
      @unlink(WWW_DIR.'/pic/product/big/'.$filename);
      @unlink(WWW_DIR.'/pic/product/small/'.$filename);
    } else {
      @unlink(WWW_DIR.'/pic/product/'.$path.'/'.$filename);
    }
    $this->redirect('Product:edit#tabs_pic', $proid);
  }

  public function actionDelete($proid) {
    $products = new \Model\ProductsModel();
    $products->delete($proid);
    $this->redirect('Product:default');
  }

  public function actionDeleteFile($ataid, $proid) {
    $file = dibi::fetch("SELECT * FROM attachments WHERE ataid=%i", $ataid);
    if ($file) {
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $ataid);

      //soubor vymazu jen pokud neexstuje v jine priloze
      $filesCnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM attachments WHERE atafilename=%s", $file->atafilename);
      if ($filesCnt == 0) @unlink(WWW_DIR.'/files/'.$file->atafilename);
    }
    $this->redirect('Product:edit#tabs_attachment', $proid);
  }

  public function actionDeleteAccess($acsid, $proid) {
    $acss = new \Model\ProAccessModel();
    $acss->delete($acsid);
    $this->redirect('Product:edit#tabs_accessories', $proid);
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sCode2 = Null;
        $this->sCodeP = Null;
        $this->sName = Null;
        $this->sCatId = Null;
        $this->sManId = Null;
        $this->sTypId = Null;
        $this->sTypId2 = Null;
        $this->sTypId3 = Null;
        $this->sTypId4 = Null;
        $this->sTypId5 = Null;
        $this->sStatus = 0;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sCode2 = $vals["code2"];
        $this->sCodeP = $vals["codep"];
        $this->sName = $vals["name"];
        $this->sCatId = $vals["catid"];
        $this->sManId = $vals["manid"];

        $this->sTypId = $vals["typid"];
        $this->sTypId2 = $vals["typid2"];
        $this->sTypId3 = $vals["typid3"];
        $this->sTypId4 = $vals["typid4"];
        $this->sTypId5 = $vals["typid5"];

        $this->sStatus = $vals["status"];

        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("Product:default");
  }

  /********************* facilities *********************/

  protected function createComponentListEditForm() {
    $dataSource = $this->getDataSource();
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $products = new \Model\ProductsModel();
    $form = $this->createAppForm();
    foreach ($dataRows as $row) {
      $cont = $form->addContainer($row->proid);
      $cont->addSelect('proaccess', '', $products->getEnumProAccess())
        ->setDefaultValue($row->proaccess);
      $cont->addText('procode2', '', 10);
      $cont->addText('procodep', '', 10);
      $cont->addText('proorder', '', 3)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "hodnota musí být číslo");

      $cont->addCheckbox('prostatuschange')
        ->setDefaultValue(FALSE);
      $cont->addHidden('prostatus')
        ->setDefaultValue($row->prostatus);
    }
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = [$this, 'productListEditFormSubmitted'];
    return $form;
  }

  protected function createComponentProductEditForm() {
    $id = (int)$this->getParameter('id');

    $product = new \Model\ProductsModel();
    if ($id > 0) $dataRow = $product->load($id);

    $form = $this->createAppForm();

    $form->addText('procode', 'Katalogové číslo:', 30)
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit');

    $form->addText('procode2', 'EAN:', 30);
    //$form->addText('procodep', 'Kód Pohoda:', 30);

    $form->addText('mastercode', 'Kód nadřízené položky:', 30);

    $form->addText('proname', 'Název:', 100)
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit');

    $form->addText('proname2', 'Název 2:', 100);

    $form->addText('pronames', 'Název Heuréka:', 100);

    $form->addSelect('promanid', 'Výrobce:', $product->getEnumProManId())
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
      ->setPrompt("Vyberte výrobce ... ");

    //nastaveni typu produktu
    $labelTypId = 'Akce';
    if (isset($this->neonParameters["labels"]["protypid"])) $labelTypId = $this->neonParameters["labels"]["protypid"];
    $labelTypId2 = 'Novinka';
    if (isset($this->neonParameters["labels"]["protypid2"])) $labelTypId2 = $this->neonParameters["labels"]["protypid2"];
    $labelTypId3 = 'Tip';
    if (isset($this->neonParameters["labels"]["protypid3"])) $labelTypId3 = $this->neonParameters["labels"]["protypid3"];
    $labelTypId4 = 'Nejprodávanější';
    if (isset($this->neonParameters["labels"]["protypid4"])) $labelTypId4 = $this->neonParameters["labels"]["protypid4"];
    $labelTypId5 = 'Připravujeme';
    if (isset($this->neonParameters["labels"]["protypid5"])) $labelTypId5 = $this->neonParameters["labels"]["protypid5"];


    $form->addCheckbox('protypid', $labelTypId);
    $form->addCheckbox('protypid2', $labelTypId2);
    //novym polozka nastavim priznak novinka
     if ($id == 0) $form["protypid2"]->setDefaultValue(TRUE);

    $form->addCheckbox('protypid3', $labelTypId3);
    $form->addCheckbox('protypid4', $labelTypId4);
    $form->addCheckbox('protypid5', $labelTypId5);

    $form->addCheckbox('proisunpacked', 'Rozbaleno');
    $form->addCheckbox('proisused', 'Použito');
    $form->addCheckbox('proisone', 'Poslední kus, po nákupu odebratz nabídky');

    $form->addText('prorating', 'Hodnocení:', 100);

    if ($this->user->isAllowed($this->name."CA", 'gifts')) {
      //$form->addText('progift', 'Dárek textově:', 100);
      $form->addText('progifts', 'Dárky:', 100)
        ->setOption("description", 'Zadejte výčet kódů zboží oddělené čárkou');
    }

    $form->addTextArea('prodescs', 'Krátký popis:', 100, 4);

    //nastaveni popisku cen
    $labelCom = 'Cena běžná';
    if (isset($this->neonParameters["labels"]["com"])) $labelCom = $this->neonParameters["labels"]["com"];
    $labelA = 'Cena A';
    if (isset($this->neonParameters["labels"]["a"])) $labelA = $this->neonParameters["labels"]["a"];
    $labelB = 'Cena B';
    if (isset($this->neonParameters["labels"]["b"])) $labelB = $this->neonParameters["labels"]["b"];
    $labelC = 'Cena C';
    if (isset($this->neonParameters["labels"]["c"])) $labelC = $this->neonParameters["labels"]["c"];
    $labelD = 'Cena D';
    if (isset($this->neonParameters["labels"]["d"])) $labelD = $this->neonParameters["labels"]["d"];

    $form->addText('proprice1com', $labelCom.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1a', $labelA.' v '.$this->curCodes[1].':', 15)
      ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.')
      ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1b', $labelB.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1c', $labelC.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('proprice1d', $labelD.' v '.$this->curCodes[1].':', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    if ($this->secondCurrency) {
      $form->addText('proprice2com', $labelCom.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2a', $labelA.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2b', $labelB.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2c', $labelC.' v '.$this->curCodes[2].':', 15)->setDisabled();
      $form->addText('proprice2d', $labelD.' v '.$this->curCodes[2].':', 15)->setDisabled();
    }

    $form->addText('procpcheureka', 'Cena za klik Heureka:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");
    $form->addText('procpczbozi', 'Cena za klik zbozi.cz:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addCheckbox("pronotdisc", "Nezapočítávat do slevy");
    $form->addCheckbox("prodelfree", "Doprava zdarma");
    $form->addCheckbox("copyprice", "Kopírovat cenu a parametry do podřízených položek")
      ->setDefaultValue(TRUE);

    $form->addCheckbox("progoogleoff", "Neposílat do Google nákupy");

    $form->addSelect('provatid', 'Sazba DPH:', $product->getEnumProVatId());

    /*
    $form->addText('procredit', 'Kredity:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\FormNUMERIC, "Kredity: hodnota musí být číslo");

    $form->addText('prorecycle', 'Recyklační poplatek:', 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\FormFLOAT, "Recyklační poplatek: hodnota musí být číslo");
    */

    $form->addSelect('proaccess', 'Dostupnost:', $product->getEnumProAccess())
      ->setPrompt("Vyberte ...")
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit.');

    $form->addText('proaccesstext', 'Dostupnost textově:', 30);

    $form->addText('proqty', 'Kusů skladem:', 3);

    /*
    $form->addSelect('proorigin', 'Distribuce:', $product->getEnumProOrigin())
      ->setPrompt("Vyberte ...");
    */

    $form->addText('proorder', 'Pořadí:', 30)
      ->setOption('description', 'Číslo podle kterého je možno řadit zboží v katalogu');

    $form->addText('proweight', 'Hmotnost:', 15)
      ->setOption('description', 'Kg')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "Hodnota musí být číslo");

    $form->addSelect('prostatus', 'Status:', $product->getEnumProStatus());

    $form->addTextArea('pronoteint', 'Interní poznámka:', 100, 4);

    if ($id > 0) {
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(dogid) FROM watchdogs WHERE dogstore=1 AND dogproid=%i", $id);
      $form->addCheckbox('watchdog_store', 'Odeslat hlídacího psa na naskladnění. Čeká: '.$cnt.' zákazníků', 30);
    }

    if ($id > 0) {
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(dogid) FROM watchdogs WHERE dogprice=1 AND dogproid=%i", $id);
      $form->addCheckbox('watchdog_price', 'Odeslat hlídacího psa na snížení ceny. Čeká: '.$cnt.' zákazníků', 30);
    }

    $form->addSubmit('tabs_editmain', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //zarazeni do katalogu
    $cnt = 0;
    $cat = new \Model\CatalogsModel();
    $cont = $form->addContainer("catPlaces");
    if ($id > 0) {
      //nactu aktualni zarazeni s chkboxem - korenove urovne
      $result = dibi::query("SELECT catid, capid, catname, catpath FROM catalogs INNER JOIN catplaces ON (capcatid=catid) WHERE capproid=$id");
      foreach ($result as $n => $row) {
        $cnt++;
        $cont->addCheckbox("cat_" . $row["catid"], ' ' . str_replace('|', ' > ', $row["catpath"]))
          ->setDefaultValue(true);
      }
    }
    $arr = dibi::fetchPairs("SELECT catid, catname FROM catalogs WHERE catmasid=0 AND catstatus=0 ORDER BY catorder");
    $form->addSelect('procatrootid', 'Hlavní kořenová kategorie', $arr)
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit.')
      ->setPrompt('Vyberte hlavní kořenovou kategorii produktu ...');

    $form->addSelect('cat_0', '', $cat->getEnumCatalogCombo(0))
      ->setPrompt('Zařadit do další kategorie ...');

    $form->addSubmit('tabs_editcatalog', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //detailni popis
    $form->addTextArea('prodesc', 'Dlouhý popis', 130, 25);
    $form['prodesc']->getControlPrototype()->class('mceEditor');

    $form->addSubmit('tabs_editdesc', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //slevy
    $form->addText("prodiscountinfo", "Textová informace o množstevních slevách", 10);
    $form->addText("disqty", "Počet kusů", 10);
    $form->addText("disdiscount", "Sleva v %", 10);

    $form->addSubmit('tabs_discount', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //SEO
    $form->addText('prokey', 'URL klíč:', 100)
      ->setOption('description', 'Pokud ponecháte prázdné, generuje se z názvu zboží');

    $form->addText('protitle', 'Title:', 100)
      ->setOption('description', 'Zadávejte pokud chcete jiné TITLE nez je název zboží');

    $form->addText('prokeywords', 'Klíčové slova:', 100)
      ->setOption('description', 'Zadávejte výčet klíčových slov, které nejsou v názvu zboží oddělený čárkou');

    $form->addTextArea('prodescription', 'Description', 100, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));

    $form->addSubmit('tabs_seo', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //ostatni udaje
    $form->addText('provideo', 'ID Youtube.com videa:', 100);

    $form->addText('proico', 'Ikona ke zboží:', 100);

    $form->addText('prooptionskeywords', 'Související zboží:', 100)
      ->setOption('description', 'Zadávejte začátky názvu souvisejících položek oddělené čárkou');
    /*
    $form->addText('prooptions', 'Alternativní zboží:', 100)
      ->setOption('description', 'Zadávejte kódy zboží oddělené svislítkem');
    */
    $form->addText('prowarranty', 'Záruka:', 30)
      ->setOption('description', 'Textově délka záruky, např. "24 mesíců"');

    $form->addText('protrarestr', 'Omezení dopravy:', 30)
      ->setOption('description', 'Zadávejte výčet ID doprav oddělený čářkou, které nejsou vhodné pro toto zboží');

    $form->addSubmit('tabs_editrem', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //obrazky
    $emptypos = 1;
    if ($id > 0) {
      //nactu seznam obrazku
      $baseUri = (string)$this->getHttpRequest()->url->baseUrl;
      for($piccnt=0;$piccnt<=10;$piccnt++){
        if ($piccnt == 0) {
          $filename = $dataRow->propicname.'.jpg';
        }  else {
          $filename = $dataRow->propicname.'_'.$piccnt.'.jpg';
        }
        if (file_exists(WWW_DIR.'/pic/product/list/'.$filename)) {

        } else {
          if ($emptypos<=1) $emptypos = $piccnt;
        }
      }
    }

    $form->addText('propicname', 'Název obrázku:', 30)
      ->setRequired(FALSE)
      ->addRule(Nette\Forms\Form::PATTERN, 'Název obrázku: můžete vyplnit pouze malá a velká písmena, pomlčku a podtržítko', '^[a-zA-Z0-9_ .-]*$')
      ->setOption('description', '.jpg');

    //obrazek hlavni
    $form->addUpload('pic0', 'Hlavní obrázek:')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');
    /*
    //obrazek slider
    $form->addUpload('pics', 'Obrázek pro slider:')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku 200x284px jinak bude obrázek oříznutý.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');
    */
    //obrazek dalsi
    $form->addUpload('pic1', 'Další obrázek:')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    for($i=1;$i<=10;$i++) $arri[$i]=$i;
    $form->addSelect('pic1position', 'Pozice obrázku:', $arri)
      ->setDefaultValue($emptypos)
      ->setOption('description', 'Nahráním nového obrázku na obsazenou pozici ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.') ;

    $form->addUpload('pic_usrsize', 'Obrázek "usrsize":')
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku 40x40px jinak bude obrázek oříznutý.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    $form->addSubmit('tabs_pic', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //načtu aktuální přísluško
    $cont = $form->addContainer('accessories');
    if ($id > 0) {
      $rows = dibi::fetchAll("
        SELECT proid, proname, ", $product->getPriceField(), "
        FROM proaccess
        INNER JOIN products ON (acsacsproid=proid) WHERE acsproid=%i", $dataRow->proid);
      foreach ($rows as $row) {
        if (!isset($cont["proid_".$row->proid])) {
          $cont->addCheckbox("proid_".$row->proid, $row->proname)
            ->setDefaultValue(TRUE);
        }
      }
    }

    //novy souvisejici
    $cont = $form->addContainer('accessories_new');
    for ($i = 1; $i <= 3; $i++) {
      $cont->addText('proname_new'.$i, 'Nové příslušenství:', 80)
        ->setAttribute('class', 'autocomplete');
    }


    $form->addSubmit('tabs_accessories', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //prilohy
    $attachments = "";
    if ($id > 0) {
      //nactu aktualni prilohy
      $atts = dibi::fetchAll("SELECT * FROM attachments WHERE ataproid=%i", $id);
      foreach ($atts as $row) {
        $attachments .= '
          <a href="'.$baseUri.'/files/'.$row["atafilename"].'?'.time().'">'.$row["ataname"].'</a> <a href="'.$this->link('deleteFile', $row["ataid"], $id).'"><img src="'.$baseUri.'/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat '.$row["ataname"].'" /></a><br />';
      }
    }
    if (!empty($attachments)) {
      $form->addGroup()
        ->setOption('container', Nette\Utils\Html::el('div')->id('tabs_attachment')->class("tab-pane fade"))
        ->setOption('description', Nette\Utils\Html::el('div')->setHtml($attachments));
    } else {
      $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('tabs_attachment')->class("tab-pane fade"));
    }
    //nova priloha
    $form->addUpload('attAdd', 'Nová příloha:');
    $form->addText('ataname', 'Název přílohy:', 100)
      ->addConditionOn($form["attAdd"],Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Vyplňte název přílohy');

    $form->addSubmit('tabs_attachment', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    //parametry zbozi
    $rows = array();
    if ($id > 0) {
      //stavajici parametry

      $prds = new Model\ProparamDefsModel();
      $enumParametersByCatalog = $prds->getenumParametersByCatalog($dataRow->procatrootid);

      //novy parametr
      $form->addSelect('param_prdid_0', 'Nový parametr - systémový:', $enumParametersByCatalog)
        ->setPrompt("Není systémový parametr ...");
      $form->addText('param_name_0', 'Nový parametr - název:', 80);
      $form->addText('param_value_0', 'Nový parametr - hodnota:', 80)
        ->addConditionOn($form["param_name_0"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, 'Nový parametr - hodnota: je nutné vyplnit.');

      $edi = $form->addContainer('params_edit');

      $rows = dibi::fetchAll("SELECT * FROM proparams WHERE prpproid=$id ORDER BY IF(prpprdid > 0, 0, 1), prpname");
      foreach ($rows as $row) {
        $cont = $edi->addContainer($row->prpid);
        $cont->addSelect('param_prdid', "", $enumParametersByCatalog)
          ->setPrompt("Není systémový parametr ...")
          ->setDefaultValue($row->prpprdid);
        if ((int)$row->prpprdid == 0) {
          $cont->addText('param_name', "", 40)
            ->setDefaultValue($row->prpname);
        }
        $cont->addText('param_value', "", 60)
          ->setDefaultValue($row->prpvalue);
        if ((int)$row->prpprdid == 0) {
          $form["params_edit"][$row->prpid]['param_value']->addConditionOn($form["params_edit"][$row->prpid]['param_name'], Nette\Forms\Form::FILLED)
            ->addRule(Nette\Forms\Form::FILLED, 'Parametr - hodnota: je nutné vyplnit.');
        } else {
          $form["params_edit"][$row->prpid]['param_value']->addConditionOn($form["params_edit"][$row->prpid]['param_prdid'], Nette\Forms\Form::FILLED)
            ->addRule(Nette\Forms\Form::FILLED, 'Parametr - hodnota: je nutné vyplnit.');
        }
       }
    }
    $form->addSubmit('tabs_param', 'Uložit')->getControlPrototype()->class('btn btn-primary');

    if ($id > 0 && $this->user->isAllowed($this->name."CA", 'gifts')) {
      $usrs = new Model\UsersModel();
      $pras = new Model\ProductPricesModel();
      $pra = $form->addContainer('product_prices');
      $rows = dibi::fetchAll("SELECT * FROM product_prices WHERE praproid=%i", $id, " ORDER BY DATE(pradatefrom)");
      foreach ($rows as $row) {
        $cont = $pra->addContainer($row->praid);

        $cont->addHidden("pracurid", 1);
        $cont->addHidden("praproid", $id);

        $cont->addText('pradatefrom', "Datum od", 10)
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.')
          ->setDefaultValue($this->formatDate($row->pradatefrom));

        $cont->addText('pradateto', "Datum do", 10)
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.')
          ->setDefaultValue($this->formatDate($row->pradateto));

        $cont->addText('praprice', "Cena", 10)
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.')
          ->setDefaultValue($row->praprice);

        $cont->addSelect("praprccat", "Cenová hladina", $usrs->getEnumUsrPrcCat())
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.')
          ->setDefaultValue($row->praprccat);

        $cont->addCheckbox("pratypid", $labelTypId)
          ->setDefaultValue($row->pratypid);
        $cont->addCheckbox("pratypid2", $labelTypId2)
          ->setDefaultValue($row->pratypid2);
        $cont->addCheckbox("pratypid3", $labelTypId3)
          ->setDefaultValue($row->pratypid3);
        $cont->addCheckbox("pratypid4", $labelTypId4)
          ->setDefaultValue($row->pratypid4);
        $cont->addCheckbox("pratypid5", $labelTypId5)
          ->setDefaultValue($row->pratypid5);

        $cont->addSelect("prastatus", "Stav", $pras->getEnumPraStatus())
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.')
          ->setDisabled()
          ->setDefaultValue($row->prastatus);
      }
      //nová položka
      $cont = $pra->addContainer(0);
      $cont->addHidden("pracurid", 1);
      $cont->addHidden("praproid", $id);

      $cont->addText('pradatefrom', "Datum od", 10);

      $cont->addText('pradateto', "Datum do", 10)
        ->addConditionOn($cont["pradatefrom"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.');

      $cont->addText('praprice', "Cena", 10)
        ->addConditionOn($cont["pradatefrom"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.');

      $cont->addCheckbox("pratypid", $labelTypId);
      $cont->addCheckbox("pratypid2", $labelTypId2);
      $cont->addCheckbox("pratypid3", $labelTypId3);
      $cont->addCheckbox("pratypid4", $labelTypId4);
      $cont->addCheckbox("pratypid5", $labelTypId5);

      $cont->addSelect("praprccat", "Cenová hladina", $usrs->getEnumUsrPrcCat())
        ->addConditionOn($cont["pradatefrom"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit.');

      $form->addSubmit('tabs_product_prices', 'Uložit')->getControlPrototype()->class('btn btn-primary');
    }




    $form->addGroup()->setOption('container', Nette\Utils\Html::el('div')->id('save_button'));
    $form->addSubmit('save', 'Uložit a vrátit se na seznam');
    if ($id > 0) $form->addSubmit('saveAsNew', 'Uložit jako novou položku');

    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'productEditFormSubmitted');

    /*
    $form->onError[] = function() use ($form) {
      die($form->getErrors());
    };
    */

    return $form;
  }

  protected function createComponentSearchForm() {
    $products = new \Model\ProductsModel();
    $catalogs = new \Model\CatalogsModel();

    $form = $this->createAppForm();
    $form->getElementPrototype()->novalidate = 'novalidate';
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Katalogové č.", 10)
      ->setDefaultValue($this->sCode);
    $form->addText("code2", "EAN", 10)
      ->setDefaultValue($this->sCode2);
    $form->addText("codep", "Kód Pohoda", 10)
      ->setDefaultValue($this->sCodeP);
    $form->addText("name", "Název zboží", 10)
      ->setDefaultValue($this->sName);

    $form->addSelect("catid", "Katalog", $catalogs->getEnumCatalogCombo());
    if (!empty($this->sCatId)) $form["catid"]->setDefaultValue($this->sCatId);

    $form->addCheckbox("typid", "Akce")
      ->setDefaultValue($this->sTypId);

    $form->addCheckbox("typid2", "Novinka")
      ->setDefaultValue($this->sTypId2);

    $form->addCheckbox("typid3", "Doprodej")
      ->setDefaultValue($this->sTypId3);

    $form->addCheckbox("typid4", "Nejprodávanější")
      ->setDefaultValue($this->sTypId4);

    $form->addCheckbox("typid5", "Připravujeme")
      ->setDefaultValue($this->sTypId5);

    $arr = array(
      '-1'=>'Nefiltrovat',
      '0'=>'Aktivní',
      '1'=>'Blokované',
    );

    $form->addSelect("status", "Status", $arr)
      ->setDefaultValue($this->sStatus);

    $form->addSelect("manid", "Výrobce", $products->getEnumProManId())
      ->setPrompt("");
    if (!empty($this->sManId)) $form["manid"]->setDefaultValue($this->sManId);

    $arr = array(
      'pro.procode'=>'Katalogové číslo',
      'pro.proname'=>'Název zboží',
      'pro.procode2'=>'EAN',
      'prscnt'=>'Prodejnosti',
      'pro.proorder'=>'Pořadí',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat')->setValidationScope( FALSE);
    $form->addSubmit('clear', 'Vyčistit')->setValidationScope( FALSE);
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  protected function createComponentProductBatchUpdateForm() {
    $catalogs = new \Model\CatalogsModel();

    $form = $this->createAppForm();
    $form->addSelect("catid", "Katalog", $catalogs->addEnumCatalogRootLevel())
      ->addRule(Nette\Forms\Form::FILLED, 'Zařazení do katalogu musí být vyplněno');
    $form->addSelect("prcid", "Cenová hladina", array('a'=>'cena A', 'b'=>'cena B','c'=>'cena C','d'=>'cena D'))
      ->addRule(Nette\Forms\Form::FILLED, 'Cenová hladina musí být vyplněna');

    $form->addText("proprice", "Nová cena")
      ->addRule(Nette\Forms\Form::FILLED, 'Nová cena musí být vyplněna');
    $form->addSubmit('update', 'Aktualizovat');
    $form->onSuccess[] = array($this, 'productBatchUpdateFormSubmitted');
    return $form;
  }
}
