{default $pageTitle='<PERSON><PERSON><PERSON> - v<PERSON><PERSON><PERSON><PERSON> dopravy a platby'}
{default $pageRobots="nofollow,noindex"}

{block #content}

{/block}

{block #orderProgress}

<div class="order order--2">

  <div class="order-progress">

    <div class="container-fluid">

      <div class="order-progress__item"><a href="{plink default}">{_'Nákupní k<PERSON>'}</a></div>
      <div class="order-progress__item is-active">{_'Doprava a platba'}</div>
      <div class="order-progress__item">{_'Dodací údaje'}</div>
      <div class="order-progress__item">{_'Souhrn objednávky'}</div>

    </div>

  </div>

  <div class="container-fluid">
  <div class="row">

    {form orderDelModeForm}
    {* vyk<PERSON>leni chyb pokud ma vypnuty JS *}

    <div class="alert alert--danger" n:if="$form->hasErrors()">
      <ul>
        <li n:foreach="$form->errors as $error">{$error}</li>
      </ul>
    </div>

    <div class="row order-delivery">

      {foreach $delModes as $row}
      <div class="col-xs-12 col-sm-6">
      <div class="order-delivery__item">

        <h2 class="order-delivery__name" id="delid_{$row->delid}">
          <img src="{$baseUri}/{$row|getDelModePicName}" alt="{$row->delname}">
          {$row->delname}
          <small>{if $row->delprice == 0}(DOPRAVA ZDARMA){else}(DOPRAVA {$row->delprice|formatPrice}){/if}</small>
        </h2>

        {if $row->delcode == 'CESKA_POSTA_BALIKOVNA'}
          <div class="order__select order__select--balikovna">
          Vyberte pobočku:
          {input balikovnaac class => 'getBranchesBalikovna', autocomplete => 'off', size => 40, placeholder=>"Hledej ...", value=>(!empty($balBranch->balcity) ? $balBranch->balcity . ", " . $balBranch->balstreet : "")}
            <div id="detail_bal" style="display: {if isset($balBranch)}block{else}none{/if}">
              <span id="detail_balname">{if !empty($balBranch->balname)}{$balBranch->balname}{/if}</span>
              <span id="detail_balstreet">{if !empty($balBranch->balname)}{$balBranch->balstreet}{/if}</span>
              <span id="detail_balcity">{if !empty($balBranch->balname)}{$balBranch->balcity}{/if}</span>
              <span id="detail_balid2">{if !empty($balBranch->balname)}{$balBranch->balid2}{/if}</span>
              <span>Více informací o balíkovně <a id="detail_balurl" href="{if !empty($balBranch->balname)}https://www.postaonline.cz/detail-pobocky/-/pobocky/detail/{$balBranch->balpostcode}{/if}" target="_blank" rel="noopener noreferrer">zde</a>.</span>
            </div>
          </div>
        {/if}

        <div class="order-delivery__content">
          {if !empty($row->deltext) || !empty($row->delurlmap)}
          <p class="order-delivery__description">
            {if !empty($row->deltext)}{$row->deltext|nl2br|noescape}{/if}
            {if !empty($row->delurlmap)}<a class="map" href="{$row->delurlmap|noescape}" target="_blank">Orientační mapa</a>{/if}
          </p>
          {/if}

          <p><strong>Vyberte způsob platby:</strong></p>
          <p class="order-delivery__options">
            {foreach $payModes[$row->delid] as $irow}
              {input orddelid:$irow->delid} {label orddelid:$irow->delid}{$irow->delname}{/label}<br>
            {/foreach}
          </p>

        </div>

      </div>
      </div>
      {/foreach}

    </div>

    <div class="row">

      <div class="col-xs-12">

        <table class="order__table" cellpadding="3" cellspacing="0" border="1">
          <tr class="order__price-sum">
            <td>Cena za dopravu a platbu:</td>
            <td colspan="2" class="order__price"><strong id="priceDeliveryVat">{$selectedDeliveryPrice|formatPrice}</strong></td>
          </tr>
        </table>

      </div>

    </div>

  </div>
  </div>

  <div class="order__price-final">
  <div class="container-fluid">

    <div class="row">

      <div class="col-xs-12">

        <table class="order__table" cellpadding="3" cellspacing="0" border="1">

          <tr class="order__price-final">
            <td><strong>Cena celkem</strong> včetně dopravy</td>
            <td colspan="2" class="order__price"><strong id="priceSumTotalVat">{($basket->priceSumVat-$basket->discountVal+$selectedDeliveryPrice)|formatPrice}</strong></td>
          </tr>
        </table>

      </div>

    </div>

  </div>
  </div>

  <div class="container-fluid">

    <div class="row order-controls">

      <div class="col-xs-12 col-sm-6">
       <a href="{plink Basket:default}" class="btn btn--secondary btn--big"><i class="icon icon--arrow-left"></i> {_'zpět do košíku'}</a>
      </div>

      <div class="col-xs-12 col-sm-6">
        <button type="submit" id="frm-orderDelModeForm-submit" name="_submit" class="btn btn--buy btn--big">{_'Pokračovat k dodacím údajům'} <i class="icon icon--arrow-right"></i></button>
      </div>

    </div>

  {/form}

  </div>

</div>

{/block}

{block #blockFooter}

<script type="text/javascript">
  var basePath = {$baseUri};
</script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js" integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU=" crossorigin="anonymous"></script>
<script src="{$baseUri}/js/balikovnaautocomplete.js"></script>
{/block}
