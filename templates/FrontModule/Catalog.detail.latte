{$pageTitle       = $catNameFull}
{$pageKeywords    = $catalogData->catkeywords}
{$pageDescription = empty($catalogData->catdescription) ? ($catalogData->catdesc|striptags) : $catalogData->catdescription}
{php
$canonicalUrl = $presenter->link('//detail', array('id'=>$catalogData->catid, 'key'=> ($catalogData|getCatKey), 't'=>array(), 'f'=>array(), 'o'=>'', 'pF'=>'', 'pT'=>'', 'm'=>array()));

$GLOBALS["ecommProId"] = '';
$GLOBALS["ecommPageType"] = 'category';
$GLOBALS["ecommTotalValue"] = 0;
}

{block #content}

{* <!-- kategorie produktu start --> *}
<div class="category">

  <div class="category__description">

    {if !empty($catalogData->catdesc)}
    <div class="category__text">
      {$catalogData->catdesc|noescape}
    </div>
    {/if}

    <div class="sort">

      Řazení:
      {php $o = $presenter->o; }

      {if $o=='pa'}<span class="sort__item sort__item--active">nejlevnější</span>
      {else}<a href="{plink 'this', 'o'=>'pa'}"><span class="sort__item">nejlevnější</span></a>{/if}

      {if $o=='pd'}<span class="sort__item sort__item--active">nejdražší</span>
      {else}<a href="{plink 'this', 'o'=>'pd'}"><span class="sort__item">nejdražší</span></a>{/if}


      {if $o=='na'}<span class="sort__item sort__item--active">A - Z</span>
      {else}<a href="{plink 'this', 'o'=>'na'}"><span class="sort__item">A - Z</span></a>{/if}

      {if $o=='nd'}<span class="sort__item sort__item--active">Z - A</span>
      {else}<a href="{plink 'this', 'o'=>'nd'}"><span class="sort__item">Z - A</span></a>{/if}

      {if $o=='os'}<span class="sort__item sort__item--active">Skladem</span>
      {else}<a href="{plink 'this', 'o'=>'os'}"><span class="sort__item">Skladem</span></a>{/if}
    </div>

  </div>

  {* <!-- podkategorie start --> *}
  {*
  {if !empty($catalogSubItems)}
  <div class="row">
  {foreach $catalogSubItems as $key => $row}
    {if $iterator->isFirst()}
    {/if}
      <div class="col-sm-6 col-md-4 col-lg-3 category__item">
        <a href="{plink Catalog:detail, $row->catid, ($row|getCatKey)}">
          {$row->catname}
        </a>
      </div>
    {if $iterator->isLast()}
    {/if}
  {/foreach}
  </div>
  {/if}
  *}
  {* <!-- podkategorie end --> *}

  {* <!-- filtrování start --> *}
  {*
  {form catalogSearchForm}
    <fieldset class="filter">
      <legend>Filtrování</legend>

    <div class="alert alert--danger" n:if="$form->hasErrors()">
    <ul>
      <li n:foreach="$form->errors as $error">{($error|translate)}</li>
    </ul>
    </div>

    <p>
    {foreach $form['t']->components as $item}
      {label $item: class => "label label--filter"}{input $item:} {$item->caption}{/label}
    {/foreach}
    </p>

    <p>
    {foreach $form['m']->components  as $item}
      {label $item: class => "label label--filter"}{input $item:} {$item->caption}{/label}
    {/foreach}
    </p>

    <p>
      <label><strong>Cenové rozpětí:</strong></label>
      {input pF class=>"input--25"} - {input pT class=>"input--25"}
    </p>

    <p>{input search class => 'btn'}</p>

  </fieldset>
  {/form}
  *}
  {* <!-- filtrování end --> *}

  {* <!-- filtrování výsledky start --> *}
  {*
  {php
    $filterNotEmpty = (!empty($formVals["m"]) || !empty($formVals["t"]) || !empty($formVals["pF"]) || !empty($formVals["pT"]));
  }
  {if $filterNotEmpty}
    <div class="filter__results">
    <strong>Je vybráno:</strong>
  {/if}

  {foreach $formVals as $key => $value}
  {if $key == 'm'}
    {foreach $value as $key => $name}
    {if $iterator->isFirst()}
    <strong>Výrobci:</strong>
    {/if}
    <span class="label label--result">{$name} <a href="{plink 'this', 'um'=>$key}"> <i class="icon icon--close"></i></a></span>
    {/foreach}
  {elseif $key == 't'}
    {foreach $value as $key => $name}
    {if $iterator->isFirst()}
    <strong>Typ:</strong>
    {/if}
    <span class="label label--result">{$name} <a href="{plink 'this', 'ut'=>$key}"> <i class="icon icon--close"></i></a></span>
    {if $iterator->isLast()}
    {/if}
    {/foreach}
  {/if}
  {/foreach}

  {if !empty($formVals["pF"]) || !empty($formVals["pT"])}
    <strong>Cena:</strong>
    {if !empty($formVals["pF"])}
      <span class="label label--result">od: {$formVals["pF"]|formatPrice} <a href="{plink 'this', 'pF'=>NULL}"> <i class="icon icon--close"></i></a></span>
    {/if}

    {if !empty($formVals["pT"])}
      <span class="label label--result">do: {$formVals["pT"]|formatPrice} <a href="{plink 'this', 'pT'=>NULL}"> <i class="icon icon--close"></i></a></span>
    {/if}
  {/if}
  {if $filterNotEmpty}
    <span class="label label--result">Vymazat vše <a href="{plink 'this', 'm'=>NULL, 't'=>NULL, 'pF'=>NULL, 'pT'=>NULL}"> <i class="icon icon--close"></i></a></span>
    </div>
  {/if}
  *}
  {* <!-- filtrování výsledky end --> *}

  {foreach $saleStatProducts as $key => $row}
    {if $iterator->isFirst()}
      <h2>{_'Nejprodávanější zboží v kategorii'} {$rootCatalog->catname}</h2>
      <table>
      <tbody>
    {/if}
      <tr>
        <td>{$iterator->getCounter()}</td>
        <td><img src="{$baseUri}/{($row|getProductPicName:'list')|noescape}" alt="{$row->proname}" width="60" height="60"></td>
        <td><a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{$row->proname} {$row->manname}</a></td>
        <td>{$row->proprice|formatPrice}</td>
      </tr>
    {if $iterator->isLast()}
      </tbody>
      </table>
    {/if}
  {/foreach}

</div>
{* <!-- kategorie produktu end --> *}

{* <!-- výpis produktů start --> *}
{include @productsList.latte, products => $productsData, title => $pageTitle}
{* <!-- výpis produktů end --> *}

{/block}
