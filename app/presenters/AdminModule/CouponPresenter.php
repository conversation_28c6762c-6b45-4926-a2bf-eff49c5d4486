<?php
namespace AdminModule;

use <PERSON><PERSON>,
  di<PERSON>,
  <PERSON>;

final class CouponPresenter extends BasePresenter {
  /** @persistent */
  public $page = 1;

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sMail = '';

  /** @persistent */
  public $sStatus;


  /********************* view default *********************/

  public function renderDefault() {
    $cous = new \Model\CouponsModel();

    $where = "";
    if (!empty($this->sCode)) $where .= " coucode = '$this->sCode' AND ";
    if (!empty($this->sMail)) $where .= " coumail LIKE '%$this->sMail%' AND ";
    if ((string)$this->sStatus!='') $where .= " coustatus=$this->sStatus AND ";

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY coudatec DESC";
    }

    $dataSource = $cous->getDataSource("SELECT * FROM coupons $where $orderBy");

    $paginator = $this['paginator']->paginator;
    $paginator->page = $this->page;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->dataRows = $dataRows;
    //ciselnik statusu
    $this->template->enum_coustatus = $cous->getEnumCouStatus();
  }

  public function renderEdit($id) {

    $this->template->id = $id;

    $form = $this['editForm'];

    $cous = new \Model\CouponsModel();
    $pros = new \Model\ProductsModel();
    $pros->setPrcCat("a");
    $dataRow = array();
    if ($id > 0) {
      $dataRow = $cous->load($id);
      //doplnil info o produktech
      foreach ($dataRow->products as $product) {
        $pro = $pros->load($product["proid"]);
        $dataRow->products[$product["proid"]]["proname"] = $pro->proname;
        $dataRow->products[$product["proid"]]["proprice"] = $pro->proprice;
      }

      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $dataRow->couvalidto = $this->formatDate($dataRow->couvalidto);
      $form->setDefaults($dataRow);
    }
    $this->template->dataRow = $dataRow;
  }

  public function renderStats($id) {
    $cous = new \Model\CouponsModel();
    $pros = new \Model\ProductsModel();
    $cou = $cous->load($id);
    $proids = "";
    foreach ($cou->products as $proid => $item) {
      //dotahnu info o produktu
      $proids .= $proid . ",";
      $pro = $pros->load($proid);
      $cou->products[$pro->proid]["proname"] = $pro->proname;
      $cou->products[$pro->proid]["proprice"] = $pro->proprice;
    }
    $proids = trim($proids, ",");
    $this->template->dataRow = $cou;
    //celková suma objednávek obsahující tento kupón
    $this->template->orders = dibi::fetch("SELECT SUM(ordpricevat) AS ordprice, COUNT(ordid) AS cnt FROM orders WHERE ordcoucode=%s", $cou->coucode);

    //produkty na které se vzdahovala sleva
    $this->template->orditems = dibi::fetchAll("
      SELECT ordcode, SUM(oriprice*oriqty) AS oriprice_sum,
      SUM(oripriceoriginal*oriqty) AS oripriceoriginal_sum, oripriceoriginal, oriprice,
      proid, procode, proname FROM orders
      INNER JOIN orditems ON (ordid=oriordid)
      INNER JOIN products ON (proid=oriproid)
      WHERE oritypid=0 AND ordcoucode IS NOT NULL ".(!empty($proids) ? "AND oriproid IN ($proids)" : "")."
      GROUP BY oriproid
    ");
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');
    $cous = new \Model\CouponsModel();
    $pros = new \Model\ProductsModel();
    $form = $this->createAppForm();
    $form->addGroup("Nastavení slevy");
    $form->addText("coucode", "Kód")
      ->setOption("description", "Zadejte unikátní kód");

    $form->addText("couvalidto", "Platí do")
      ->setOption("description", "Zadejte datum ve tvaru dd.mm.rrrr")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addText("couqty", "Počet použití")
      ->setOption("description", "0 = neomezeně")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno")
      ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo")
      ->setDefaultValue(0);

    $form->addCheckbox("couonlyreg", "Jen pro registrované");

    $form->addText("coumail", "Email");

    $form->addText("couvalue", "Hodnota kupónu")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo");

    $form->addSelect("couvalueunit", "Jednotka kupónu", array("%" => "%", "Kč" => "Kč"))
      ->setPrompt("")
      ->addConditionOn($form["couvalue"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněna");

    $form->addText("couvaluelimit", "Minimální hodnota obj.")
      ->addConditionOn($form["couvalue"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněna pokud je vyplněna Hodnota kupónu");

    $form->addCheckbox("coudelfree", "Doprava zdarma");
    $form->addCheckbox("coupayfree", "Platba zdarma");
    $form->addGroup("Produkty se slevou");
    $cont = $form->addContainer("data");
    if ($id > 0) {
      $cou = $cous->load($id);
      foreach ($cou->products as $proid => $porduct) {
        $cont = $form["data"]->addContainer($proid);
        $pro = $pros->load($proid);
        $cont->addHidden("proid", $proid);
        $cont->addText("discount", $pro->proname." (cena: ".$pro->proprice."Kč)", 6)
          ->setOption("description", "Kč")
          ->setAttribute("placeholder", "sleva v Kč")
          ->setDefaultValue($porduct["prodiscount"]);
      }
    }
    $cont = $form["data"]->addContainer(0);
    $cont->addHidden("proid")
      ->setHtmlId("frm-editForm-data-0-proid");
    $cont->addText("proname", "Název nového produktu", 80)
      ->setAttribute('class', 'autocomplete');
    $cont->addText("discount", "Sleva", 6)
      ->setOption("description", "Kč")
      ->setAttribute("placeholder", "sleva v Kč");
    $form->addGroup("");
    $form->addSelect("coustatus", "Status", $cous->getEnumCouStatus());
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');
    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $coupons = new \Model\CouponsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      $vals->couvalidto = $this->formatDateMySQL($vals->couvalidto);
      $products = "";
      foreach ($vals["data"] as $proid => $item) {
        if ((int)$item->proid > 0 && (int)$item->discount > 0) $products .= "$item->proid;$item->discount"."|";
      }
      $vals->couproducts = trim($products, "|");
      unset($vals["data"]);

      try {

        $coupons->checkDuplicityCouCode($id, $vals["coucode"]);

        if ($id > 0) {
          $coupons->update($id, $vals);
        } else {
          $id = $coupons->insert($vals);
        }

      } catch (\Exception $e) {
        $form->addError($e->getMessage());
        return false;
      }

      $this->flashMessage('Uloženo v pořádku');
      $this->redirect('edit',$id);
    }
  }

  protected function createComponentSearchForm() {
    $cous = new \Model\CouponsModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Kód ", 10)
      ->setDefaultValue($this->sCode);

    $form->addText("mail", "Email ", 10)
      ->setDefaultValue($this->sMail);

    $form->addSelect("status", "Stav", $cous->getEnumCouStatus())
      ->setPrompt('Vše');
    if ($this->sStatus != '') $form["status"]->setDefaultValue($this->sStatus);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sMail = Null;
        $this->sStatus = Null;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sMail = $vals["mail"];
        $this->sStatus = $vals["status"];
      }
    }
    $this->redirect("default");
  }
}
?>
