// štítky
// lze pou<PERSON>t na cokoliv, názvy jsou zavádějící pro standardní v<PERSON>žití

.label {

  display: inline-block;
  margin-bottom: 4px;
  padding: 5px 8px;

  color: @color_white;
  font-size: 13px;
  line-height: 15px;
  text-transform: uppercase;

  background-color: @color_main;

  // pokud je uvnitř formulářový prvek (styly pro radio nebo checkbox)
  input {
    vertical-align: middle;
    margin-bottom: 0;
  }

}

  // hover efekt po najetí, pokud je štítek v odkazu
  a .label {

    &:hover, &:active, &:focus {
      opacity: 0.8;
    }

  }

  // akce
  .label--action {
    background-color: @color_label_action;
  }

  // tip
  .label--tip {
    background-color: @color_label_tip;
  }

  // sleva
  .label--discount {
    background-color: @color_label_discount;
  }

  // novinka
  .label--new {
    background-color: @color_label_new;
  }

  // doprava zdarma
  .label--delivery {
    background-color: @color_label_delivery;
  }

  // štítek pro řazení
  .label--sort {

    background-color: @color_main_light;

    // pokud obsahuje ikonu
    .icon {
      margin-left: 5px;
    }

  }

  // štítek pro filtrování
  .label--filter {
    color: @color_black;
    background-color: @color_white;
    border: 1px dotted @color_gray;
  }

  // štítek pro výsledky filtrování
  .label--result {

    background-color: @color_main_light;

    // zavírací tlačítko
    a {
      color: @color_white;
      text-decoration: none;
    }
    .icon {

      margin-left: 5px;
      cursor: pointer;

      &:hover, &:active, &:focus {
        opacity: 0.8;
      }

    }

  }
