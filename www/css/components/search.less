// hledání v h<PERSON>ič<PERSON>

.search {

  width: 170px;
  position: relative; // pro absolutní centrování tlačítka
  margin-top: 10px;

  @media (min-width: @mqsm) {
    float: left;
  }

  @media (min-width: @mqmd) {
    width: 260px;
    margin-top: 14px;
  }

  // úprava vzhledu na nižších rozlišeních
  @media (max-width: @mqsm) {
    width: 98%;
    margin: 10px 0 0 5px;
  }

}

  .search__submit {

    position: absolute;
    bottom: 1px;
    right: 2px;

    overflow: hidden; // pro skrytí textu

    width: 30px;
    height: 30px;

    padding: 0;

    background: transparent; // reset pozadí
    border-radius: 0;

    @media (max-width: @mqsm) {
      width: 35px;
      height: 35px;
    }

    &:hover, &:active, &:focus {
      background-color: transparent;
    }

    .icon {

      font-size: 25px;
      color: @color_main_light;
      padding: 1px 50px 50px 1px; // pro osazení textu

      &:hover, &:active, &:focus {
        color: @color_main_dark;
      }

    }

  }

  .search__input {

    padding: 4px 10px;
    font-size: 16px;
    border: none;
    background-color: @color_gray_light;

    @media (max-width: @mqsm) {
      padding: 10px 15px;
      border: 1px solid @color_gray_light;
    }

  }
