{var $title = 'Mailování'}

{block #content}
 <p><a href="{plink edit, 0}" class="btn btn-primary btn-sm">Nové mailování</a> <a href="{plink //:Front:Batch:mailing, 'k'=>'942a13a8cb5840'}" class="btn btn-primary btn-sm" onclick="return confirm('Opravdu spustit?')">Spustit mailovací dávku</a></p>
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Id</th>
    <th>Předmět</th>
    <th>Datum zahájení</th>
    <th><PERSON>as spuštění</th>
    <th><PERSON>as dokončení</th>
    <th>Zobrazení</th>
    <th>Kliky</th>
    <th>Celkem emailů</th>
    <th>Z<PERSON>ýv<PERSON> odeslat</th>
    <th>Status</th>
    <th colspan="3"></th>
  </tr>
  {foreach $dataRows as $row}
    <tr>
      <td>{$row->mamid}</td>
      <td>{$row->mamsubject}</td>
      <td>{$row->mamdate|date:'d.m.Y'}</td>
      <td>{$row->mamdatestart|date:'d.m.Y H:m:i'}</td>
      <td>{$row->mamdateend|date:'d.m.Y H:m:i'}</td>
      <td>{$row->mamviews}</td>
      <td>{$row->mamclicks}</td>
      <td>{$row->mamcntall}</td>
      <td>{$row->mamleft}</td>
      <td>{$enum_mamstatus[$row->mamstatus]}</td>
      <td><a href="{plink Mailing:edit, $row->mamid}">{('edit'|glyph)|noescape}</a></td>
      <td><a href="{plink :Front:Mailing:detail, $row->mamid, 1, $urlKey}">{('info'|glyph)|noescape}</a></td>
      <td><a href="{plink Mailing:stats, $row->mamid}">{('stats'|glyph)|noescape}</a></td>
    </tr>
  {/foreach}
  </table>
{/block}
