<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class CatPlacesModel extends BaseModel {

  protected $tableName = "catplaces";
  protected $fieldPrefix = "cap";

  public function updatePlace(int $proId, int $catId) {
    $capId = (int)dibi::fetchSingle("SELECT capid FROM catplaces WHERE capproid=? AND capcatid=?", $proId, $catId);
    if ($capId === 0) {
      $this->insert([
        "capproid"=>$proId,
        "capcatid"=>$catId
      ]);
    } else {
      $this->update($capId, [
        "capproid"=>$proId,
        "capcatid"=>$catId
      ]);
    }
  }

}
