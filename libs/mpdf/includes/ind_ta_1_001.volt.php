<?php
	$volt = array (
  0 => 
  array (
    'match' => '0BCD 200D',
    'replace' => '014B',
  ),
  1 => 
  array (
    'match' => '0BCD 200C',
    'replace' => 'E002',
  ),
  2 => 
  array (
    'match' => '200D 0BCD',
    'replace' => '014A',
  ),
  3 => 
  array (
    'match' => '0B95 0BCD 0BB7',
    'replace' => 'E005',
  ),
  4 => 
  array (
    'match' => '0BB8 0BCD 0BB0 0BC0',
    'replace' => 'E04B',
  ),
  5 => 
  array (
    'match' => '0B93 0BAE 0BCD',
    'replace' => 'E04C',
  ),
  6 => 
  array (
    'match' => '(0BB8) 0BC1',
    'replace' => '\\1 E00C',
  ),
  7 => 
  array (
    'match' => '(0BB8) 0BC2',
    'replace' => '\\1 E00D',
  ),
  8 => 
  array (
    'match' => '0B95 0BC2',
    'replace' => 'E00F',
  ),
  9 => 
  array (
    'match' => '0B9C 0BC1',
    'replace' => 'E014',
  ),
  10 => 
  array (
    'match' => '0B9C 0BC2',
    'replace' => 'E015',
  ),
  11 => 
  array (
    'match' => '0B9F 0BBF',
    'replace' => 'E018',
  ),
  12 => 
  array (
    'match' => '0BB2 0BBF',
    'replace' => 'E033',
  ),
  13 => 
  array (
    'match' => '0BB7 0BBF',
    'replace' => 'E03F',
  ),
  14 => 
  array (
    'match' => '0BB7 0BC1',
    'replace' => 'E041',
  ),
  15 => 
  array (
    'match' => '0BB7 0BC2',
    'replace' => 'E042',
  ),
  16 => 
  array (
    'match' => '0BB8 0BBF',
    'replace' => 'E043',
  ),
  17 => 
  array (
    'match' => '0BB9 0BC1',
    'replace' => 'E045',
  ),
  18 => 
  array (
    'match' => '0BB9 0BC2',
    'replace' => 'E046',
  ),
  19 => 
  array (
    'match' => 'E005 0BBF',
    'replace' => 'E047',
  ),
  20 => 
  array (
    'match' => 'E005 0BC1',
    'replace' => 'E049',
  ),
  21 => 
  array (
    'match' => 'E005 0BC2',
    'replace' => 'E04A',
  ),
  22 => 
  array (
    'match' => '((0BAA|0BAF|0B99|0BB5)) 0BC0',
    'replace' => '\\1 E00B',
  ),
  23 => 
  array (
    'match' => '((0BAE|0B9A|0BB9|0B9C|0BB4|0BB1)) 0BBF',
    'replace' => '\\1 E006',
  ),
  24 => 
  array (
    'match' => '((0BB0|0BB3|0BA3|0BA9)) 0BBF',
    'replace' => '\\1 E007',
  ),
  25 => 
  array (
    'match' => '((0B95|0BA4)) 0BBF',
    'replace' => '\\1 E008',
  ),
  26 => 
  array (
    'match' => '((0BAA|0BAF|0B99|0BB5)) 0BBF',
    'replace' => '\\1 E009',
  ),
  27 => 
  array (
    'match' => '((0BA8|0B9E)) 0BBF',
    'replace' => '\\1 E00A',
  ),
  28 => 
  array (
    'match' => '0BA3 200C 0BC8',
    'replace' => 'E01F',
  ),
  29 => 
  array (
    'match' => '0BA9 200C 0BC8',
    'replace' => 'E027',
  ),
  30 => 
  array (
    'match' => '0BB2 200C 0BC8',
    'replace' => 'E037',
  ),
  31 => 
  array (
    'match' => '0BB3 200C 0BC8',
    'replace' => 'E03A',
  ),
  32 => 
  array (
    'match' => '0B9F 0BC0',
    'replace' => 'E019',
  ),
  33 => 
  array (
    'match' => '0BB2 0BC0',
    'replace' => 'E034',
  ),
  34 => 
  array (
    'match' => '0BB7 0BC0',
    'replace' => 'E040',
  ),
  35 => 
  array (
    'match' => '0BB8 0BC0',
    'replace' => 'E044',
  ),
  36 => 
  array (
    'match' => 'E005 0BC0',
    'replace' => 'E048',
  ),
  37 => 
  array (
    'match' => '0B95 0BC1',
    'replace' => 'E00E',
  ),
  38 => 
  array (
    'match' => '0B99 0BC1',
    'replace' => 'E010',
  ),
  39 => 
  array (
    'match' => '0B99 0BC2',
    'replace' => 'E011',
  ),
  40 => 
  array (
    'match' => '0B9A 0BC1',
    'replace' => 'E012',
  ),
  41 => 
  array (
    'match' => '0B9A 0BC2',
    'replace' => 'E013',
  ),
  42 => 
  array (
    'match' => '0B9E 0BC1',
    'replace' => 'E016',
  ),
  43 => 
  array (
    'match' => '0B9E 0BC2',
    'replace' => 'E017',
  ),
  44 => 
  array (
    'match' => '0B9F 0BC1',
    'replace' => 'E01A',
  ),
  45 => 
  array (
    'match' => '0B9F 0BC2',
    'replace' => 'E01B',
  ),
  46 => 
  array (
    'match' => '0BA3 200C 0BBE',
    'replace' => 'E01C',
  ),
  47 => 
  array (
    'match' => '0BA3 0BC1',
    'replace' => 'E01D',
  ),
  48 => 
  array (
    'match' => '0BA3 0BC2',
    'replace' => 'E01E',
  ),
  49 => 
  array (
    'match' => '0BA4 0BC1',
    'replace' => 'E020',
  ),
  50 => 
  array (
    'match' => '0BA4 0BC2',
    'replace' => 'E021',
  ),
  51 => 
  array (
    'match' => '0BA8 0BC1',
    'replace' => 'E022',
  ),
  52 => 
  array (
    'match' => '0BA8 0BC2',
    'replace' => 'E023',
  ),
  53 => 
  array (
    'match' => '0BA9 200C 0BBE',
    'replace' => 'E024',
  ),
  54 => 
  array (
    'match' => '0BA9 0BC1',
    'replace' => 'E025',
  ),
  55 => 
  array (
    'match' => '0BA9 0BC2',
    'replace' => 'E026',
  ),
  56 => 
  array (
    'match' => '0BAA 0BC1',
    'replace' => 'E028',
  ),
  57 => 
  array (
    'match' => '0BAA 0BC2',
    'replace' => 'E029',
  ),
  58 => 
  array (
    'match' => '0BAE 0BC1',
    'replace' => 'E02A',
  ),
  59 => 
  array (
    'match' => '0BAE 0BC2',
    'replace' => 'E02B',
  ),
  60 => 
  array (
    'match' => '0BAF 0BC1',
    'replace' => 'E02C',
  ),
  61 => 
  array (
    'match' => '0BAF 0BC2',
    'replace' => 'E02D',
  ),
  62 => 
  array (
    'match' => '0BB0 0BC1',
    'replace' => 'E02E',
  ),
  63 => 
  array (
    'match' => '0BB0 0BC2',
    'replace' => 'E02F',
  ),
  64 => 
  array (
    'match' => '0BB1 200C 0BBE',
    'replace' => 'E030',
  ),
  65 => 
  array (
    'match' => '0BB1 0BC1',
    'replace' => 'E031',
  ),
  66 => 
  array (
    'match' => '0BB1 0BC2',
    'replace' => 'E032',
  ),
  67 => 
  array (
    'match' => '0BB2 0BC1',
    'replace' => 'E035',
  ),
  68 => 
  array (
    'match' => '0BB2 0BC2',
    'replace' => 'E036',
  ),
  69 => 
  array (
    'match' => '0BB3 0BC1',
    'replace' => 'E038',
  ),
  70 => 
  array (
    'match' => '0BB3 0BC2',
    'replace' => 'E039',
  ),
  71 => 
  array (
    'match' => '0BB4 0BC1',
    'replace' => 'E03B',
  ),
  72 => 
  array (
    'match' => '0BB4 0BC2',
    'replace' => 'E03C',
  ),
  73 => 
  array (
    'match' => '0BB5 0BC1',
    'replace' => 'E03D',
  ),
  74 => 
  array (
    'match' => '0BB5 0BC2',
    'replace' => 'E03E',
  ),
  75 => 
  array (
    'match' => '014B',
    'replace' => '0BCD',
  ),
  76 => 
  array (
    'match' => 'E002',
    'replace' => '0BCD',
  ),
  77 => 
  array (
    'match' => '014A',
    'replace' => '0BCD',
  ),
);
?>