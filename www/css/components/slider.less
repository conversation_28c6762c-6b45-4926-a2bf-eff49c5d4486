// úvodní slider

.slider {

  display: none; // na malých rozlišení skryto

  background-color: @color_black;
  background-position: center top;
  background-repeat: no-repeat;

  @media (min-width: @mqxs) {
    display: block;
  }

  // roztažení slideru na celou plochu
  .container-fluid {
    @media (max-width: @mqsm) {
      padding: 0;
    }
  }

  h2 {
    margin: 10px 0;
    color: @color_main_light;
    font-size: 25px;
    font-weight: 400;
    text-transform: uppercase;
  }

  p {
    margin: 10px 0;
    font-size: 15px;
    font-weight: 300;
  }

  p .btn {
    font-weight: 400;
  }

}

  // položka slideru
  .slider__item {
    position: relative;
    height: 293px;
  }

  // obsah slideru
  .slider__content {
    position: absolute;
    bottom: 30px;
    right: 30px;
    width: 480px;
    padding: 10px 20px;
    color: @color_white;
    background-color: @color_black;
    background-color: rgba(0, 0, 0, 0.7);
  }

  // přeražení stylů <PERSON>lick slideru
  .slick-dots {

    position: absolute;
    bottom: 0;
    z-index: 1;

    width: 98%;

    margin: 0;
    padding: 0;

    height: 26px;
    text-align: center;

    li {

      display: inline-block;
      margin: 5px;

      &.slick-active button {
        background-color: @color_main_dark;
      }

    }

    button {

      display: block;
      width: 14px;
      height: 14px;
      padding: 0;
      border-radius: 14px;
      overflow: hidden;
      line-height: 900px;

      // zrušení focusu
      &:focus {
        outline: none;
      }

    }


  }
