<?php

class BalikovnaException extends \Exception { }

class BalikovnaApi {
  const FEED_URL = 'http://napostu.cpost.cz/vystupy/balikovny.xml';

  public function updateBranches() {
    //dibi::query("DELETE FROM ulozenkapoints");
    //dibi::query("ALTER TABLE ulozenkapoints AUTO_INCREMENT=1");
    $ulos = new \Model\BalikovnapointsModel();

    $startTime = dibi::fetchSingle("SELECT Now()");

    $startDateTime = $startTime->format('Y-m-d H:i:s');

    $url = self::FEED_URL;
    $xml = new \SimpleXMLElement(file_get_contents($url));

    $days = array(
      0 => 'Pondělí',
      1 => 'Úterý',
      2 => 'Středa',
      3 => 'Čtvrtek',
      4 => 'Pátek',
      5 => 'Sobota',
      6 => 'Neděle'
    );

    $cnt = 0;

    foreach ($xml->row as $row) {
      $otevDoby = (array)$row->OTEV_DOBY->den;
      //jen mista v CR
      $openingHours = "";
      /*
      foreach ($days as $dayId => $dayName) {
        if (isset($otevDoby["od_do"])) {
          $openingHours .= $dayName . ": " . (string)$otevDoby["od_do"]->od . " - " . (string)$otevDoby["od_do"]->do . "<br>\n";
        }
      }
      */

      $arr = explode(', ', (string)$row->ADRESA);


      $data = array(
        'balid2' => (string)$row->PSC,
        'balname' => (string)$row->NAZEV,
        'balstreet' => (string)$arr[0],
        'balpostcode' => (string)$row->PSC,
        'balopeninghours' => $openingHours,
      );

      if (count($arr) === 3) {
          $data['balcity'] = (string)$arr[2];
      } else {
        $data['balcity'] = (string)$arr[3] . " " . (string)$arr[1];
      }

      //podivam se, zda existuje
      $balid = (int)dibi::fetchSingle("SELECT balid FROM balikovnapoints WHERE balid2=%i", (string)$row->PSC);
      if ($balid > 0) {
        $ulos->update($balid, $data);
      } else {
        $ulos->insert($data);
      }
      $cnt ++;
    }
    $cntDel = dibi::query("DELETE FROM balikovnapoints WHERE coalesce(baldateu, baldatec) < %s", $startDateTime);
  }

  public function setErrorMsg($msg) {
    Tracy\Debugger::log($msg);
    header("HTTP/1.0 500 Internal Server Error");
    $data = array(
      "id" => 500,
      "msg" => $msg,
    );
    echo json_encode($data);
    die();
  }

}
?>
