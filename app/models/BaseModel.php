<?php
namespace Model;
use Nette, dibi;

class ModelException extends \Exception { }

abstract class BaseModel {
  use Nette\SmartObject;

  //identifikatory vlastnosti datoveho pole
  /** @var string nazev tabulky */
  protected $tableName;

  /** @var string nastaveni */
  protected $config;

  /** @var string nazev tabulky s prefixem */
  protected $table;

  protected $cache = Null;

  /** nastaveni z neonu */
  public $neonParameters = array();

  /** ID aktualni meny; */
  protected $curId = 1;

  /** zaokrouleni pocet des. mist aktualni meny */
  protected $curDigits = 2;

  /** meny ktere eshop pouziva */
  protected $currencies = array();

  protected $prccat = 'a';

  public function __construct() {
    $this->table = $this->tableName;
  }

  /**
  * nastavi aktualni menu
  *
  */
  public function setCurrency($currencies, $curId) {
    $this->currencies = $currencies;
    $this->curId = (int)$curId;
    $this->curDigits = (int)$this->currencies[$this->curId]["decimals"];
  }

  /**
  * vraci vlastnosti sloupce
  *
  * @param string $colName nazev sloupce
  * @return array
  */
  public function getColProperties($colName) {
    return Null;
  }

  /**
  * vraci pro dany sloupec hodnotu prislusne property
  *
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @return string
  */
  public function getColProperty($colName, $colProperty) {
    return Null;
  }

  /**
  * nastavi proslusnemu sloupci hodnotu prislusne property
  *
  * @param string $colName nazev sloupce
  * @param string $colProperty nazev property [type|size|nullable|default]
  * @param string $propertyValue hodnota property
  * @return boolean
  */
  protected function setColProperty($colName, $colProperty, $propertyValue) {
    return true;
  }

  public function getDataSource($sql="") {
    if ($sql == "") $sql = $this->getSql();
    return dibi::dataSource($sql);
  }

  public function getSql () {
    return "SELECT * FROM $this->table";
  }

  /**
   * vraci jeden zaznam
   *
   * @param integer|string $id hodnota id ve smyslu jednoznacneho identifikatoru, nemusi byt primarni klic
   * @param string $col nazev sloupce bez prefixu
   * @return dibiRow
   * @throws \Dibi\Exception
   */
  public function load($id, $col='id') {
    $colName = $this->fieldPrefix.$col;
    if ($col == 'id') {
      $f = 'i';
    } else {
      $f = 's';
    }
    return dibi::fetch($this->getSql()." WHERE ".$colName.'=%'.$f, $id, " LIMIT 1");
  }

  public function update($id, $data,$setDateU = True) {
    $this->cacheClean();
    if ($setDateU) $data[$this->fieldPrefix.'dateu'] = new \DateTime;
    return dibi::update($this->table, $data)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function insert($data) {
    $this->cacheClean();
    if (!isset($data[$this->fieldPrefix.'datec'])) {
      $data[$this->fieldPrefix.'datec'] = new \DateTime;
    }
    return dibi::insert($this->table, $data)
      ->execute(dibi::IDENTIFIER);
  }

  public function save(&$id, $data, $setDateU = True) {
    if ($id > 0) {
      return $this->update($id, $data, $setDateU);
    }

    $id = $this->insert($data);
    return($id > 0);
  }

  public function delete($id) {
    $this->cacheClean();
    return dibi::delete($this->table)
      ->where($this->fieldPrefix.'id=%i', $id)
      ->execute();
  }

  public function fetchAll($sql) {
    $result = dibi::dataSource($sql)
      ->getResult();
    return $result->fetchAll();
  }

  /**
  * vraci obsah cache
  *
  * @param string $key - identifikator promenne
  */
  public function cacheGet($key) {
    return false;
    $value = $this->cache->load($key);
    if ($value === NULL) {
      return false;
    } else {
      return $value;
    }
  }

  /**
  * ulozi cache
  *
  * @param string $key - identifikator promenne
  * @param mixed $data
  */
  public function cacheSave($key, $data) {
    return false;

    return $this->cache->save($key, $data, array(Nette\Caching\Cache::TAGS => array($this->tableName)));
  }

  /**
  * vymaze cache vazanou k prislusne tabulce
  *
  */
  public function cacheClean() {
    return false;
    $this->cache->clean(array(Nette\Caching\Cache::TAGS => array($this->tableName)));
  }

  public function getConfig() {
    if (empty($this->config)) {
      $cfgs = new \Model\ConfigModel();
      $this->config = $cfgs->getConfig();
    }
    return $this->config;
  }

  /**
   * @param $storage
   * @return Nette\Caching\Cache
   */
  private function getCache($storage): Nette\Caching\Cache {
    if ($this->cache === NULL) {
      $this->cache = new Nette\Caching\Cache($storage, $this->tableName);
    }
    return $this->cache;
  }



}
