<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ProparamDefsModel extends BaseModel {

  protected $tableName = "proparamdefs";
  protected $fieldPrefix = "prd";

  /**
   * ciselnik prdstatus
   * @return array
   */

  public function getenumParametersByCatalog($catid) {
    $cacheName = 'parametersByCatalog'.(int)$catid;
    $cache = $this->cacheGet($cacheName);
    if (!$cache) {
      unset($cache);
      $arr = dibi::query("SELECT prdid, CONCAT(prdname, ' [', prdunit, ']') AS prdname FROM proparamdefs WHERE prdcatid=%i", $catid, " OR COALESCE(prdcatid, 0)=0")->fetchPairs("prdid", "prdname");
      $this->cacheSave($cacheName, $arr);
      $cache = $arr;
    }
    return $cache;
  }

  public function getEnumPrdStatus() {
    return array(
      0 => 'Aktivní',
      1 => '<PERSON><PERSON>kovaný',
    );
  }
}
?>
