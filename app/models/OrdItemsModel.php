<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class OrdItemsModel extends BaseModel {

  protected $tableName = "orditems";
  protected $fieldPrefix = "ori";

  public function insert($data) {
    if (empty($data["oripriceoriginal"]) && !empty($data["oriprice"])) $data["oripriceoriginal"] = $data["oriprice"];
    return (parent::insert($data));
  }

  public function delete($id) {
    $oi = $this->load($id);
    if ($oi->oritypid==0) {
      //vymazu svazane polozky
      $rows = dibi::fetchAll("SELECT oriid FROM ".$this->table." WHERE orioriid=%i", $id);
      foreach ($rows as $row) {
        $this->delete($row->oriid);
      }
    }
    return(parent::delete($id));
  }

}
