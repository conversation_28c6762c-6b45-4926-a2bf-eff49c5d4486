{* Product.detail.latte *}

{* nastaveni promennych *}
{$pageTitle       = (!empty($product->protitle) ? $product->protitle : $product->proname)}
{$pageKeywords    = $product->prokeywords}
{$pageDescription = (empty($productMasterData->prodescription) ? ($productMasterData->prodescs|striptags) : $productMasterData->prodescription)}
{$pageImage = $baseUri."/" . ($productMasterData|getProductPicName:'detail')}
{* do description doplnim nazev zbozi tak aby se neorezal *}
{php
  $len = strlen(trim($product->proname).' '.$product->manname);
  $len = 160-$len-1;
  $pageDescription = ($pageDescription|truncate:$len:'').' '.trim($product->proname).' '.$product->manname;

  $GLOBALS["ecommProId"] = $product->proid;
  $GLOBALS["ecommPageType"] = 'product';
  $GLOBALS["ecommTotalValue"] = $product->proprice;
}

{* drobky pro detal zbozi *}
{block #crumb}
<div class="breadcrumb">

  <a href="{$baseUri}">{$presenter->config["SERVER_NAMESHORT"]}</a>
  {ifset $catalogPath}
  {foreach $catalogPath as $row}
    <span>»</span>
    <a href="{plink Catalog:detail, $row->catid, ($row|getCatKey)}">{$row->catname}</a>
  {/foreach}
  {/ifset}
  <span>»</span> <strong>{$product->proname}</strong>

</div>
{/block}

{block #content}

<script>

  fbq('track', 'ViewContent', {
    content_ids: [{$product->proid}],
    content_type: 'product',
    value: {$product->proprice},
    currency: {$curKey}
  });
</script>

{*<!-- product detail start -->*}
<div class="product-detail">

{if $product->prostatus == 0}

  {* ritch snippets http://schema.org/Product *}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Product",
    "name": {$product->proname},
    "image": {$baseUri . '/' .( $productMasterData|getProductPicName:'detail')},
    "description": {$pageDescription},
    "manufacturer": {$manufacturer->manname},
    "url": {plink '//this'},
    "offers": {
      "@type": "Offer",
      "availability": {if $product->proaccess == 0}"http://schema.org/InStock"{else}"http://schema.org/OutOfStock"{/if},
      "price": {$product->proprice},
      "priceCurrency": "CZK"
    }
  }
  </script>

  <div class="row">

    {*<!-- hlavní část produktu start -->*}
    <div class="col-sm-7">

      {*<!-- popis start -->*}
      <div class="product-detail__description">
        {$productMasterData->prodescs|noescape}
      </div>
      {*<!-- popis end -->*}

      {*<!-- price start -->*}
      <div class="product-detail__price">
        {if count($subItems) == 0}
          <strong>{$product->proprice|formatPrice}</strong> {if $config["PRICEVAT"] == 'inclvat'} s DPH {else} bez DPH {/if}{if isset($config["VATTYPE_" . $product->provatid])}({$config["VATTYPE_" . $product->provatid]}% DPH){/if}

          <a href="{plink Basket:add $product->proid}" class="btn btn--buy btn--big">Koupit</a>
        {/if}

        {if $product->proaccess != 100 OR ($product->proaccess == 100 AND empty($product->proaccesstext))}
          <br><span class="stock{if $product->proaccess == 0} stock--available{else} stock--unavailable{/if}">{$enum_proaccess[$product->proaccess]}</span>
        {else}
          <br><span class="stock stock--available">{$product->proaccesstext}</span>
        {/if}
        {if $adminLogIn}<a href="{plink :Admin:Product:edit, $product->proid}" target="admin" class="control control--success"><i class="icon icon--wheel"></i></a>{/if}
      </div>
      {*<!-- price end -->*}

      {*<!-- product info start -->*}
      <div class="product-detail__info">

        <p>
          {if !empty($product->prowarranty)}{_'Záruka'}: <strong>{$product->prowarranty}</strong><br>{/if}
          {_'Objednací číslo'}: <strong>{$product->procode}</strong><br>
          {_'Výrobce'}: <strong>{$manufacturer->manname}</strong><br>
          {if !empty($product->prodiscountinfo)}
            <br>{_'Množstevní sleva'}:
            <strong>{$product->prodiscountinfo}</strong><br>
          {/if}
        </p>

      </div>
      {*<!-- product info end -->*}

      {*<!-- sociální sítě start -->*}
      {*
      <div class="share">
        <strong>{_'Sdílejte na:'}</strong>
        <a href="https://www.facebook.com/sharer/sharer.php?u={plink '//this'}" class="share--facebook" title="sdílet na Facebooku"><i class="icon icon--facebook"></i></a>
        <a href="https://plus.google.com/share?url={plink '//this'}" class="share--googleplus" title="sdílet na Google +"><i class="icon icon--googleplus"></i></a>
        <a href="https://twitter.com/intent/tweet?url={plink '//this'}&text={$product->proname}" class="share--twitter" title="sdílet na Twitteru"><i class="icon icon--twitter"></i></a>
      </div>
      *}
      {*<!-- sociální sítě end -->*}

      {*<!-- helpers start -->*}
      {*
      <div class="product-detail__helpers">

        <a class="btn btn--small" href="{plink compareAdd, $product->proid}">
          <i class="icon icon--wheel"></i>
          {_'Porovnat'}
        </a>
        <a class="btn btn--small" href="{plink bookmarkAdd, $product->proid}">
          <i class="icon icon--star"></i>
          {_'Přidat k oblíbeným'}
        </a>
        <a class="btn btn--small control--print" href="#">
          <i class="icon icon--print"></i>
          {_'Vytisknout'}
        </a>
        <a class="btn btn--small" href="#tab4" class="opentab">
          <i class="icon icon--chat"></i>
          {_'Dotazy a komentáře'}
        </a>

        <div class="product-detail__watchdog">

          {form watchDogPrice}
          <p>
            <strong><i class="icon icon--dog"></i> {_'Hlídat cenu:'}</strong><br>
            {input dogmail 'placeholder'=>'Zadejte Váš e-mail', class => 'input--50'}
            {input send class => 'btn btn--small'}
          </p>
          {/form}

          {if $product->proaccess > 0}
              {form watchDogStore}
              <p>
                <strong><i class="icon icon--dog"></i> {_'Hlídat naskladnění:'}</strong><br>
                {input dogmail 'placeholder'=>'Zadejte Váš e-mail', class => 'input--50'}
                {input send class => 'btn btn--small'}
              </p>
              {/form}
          {/if}

        </div>

      </div>
      *}
      {*<!-- helpers start -->*}

    </div>
    {*<!-- hlavní část produktu end -->*}

    {*<!-- boční lišta start -->*}
    <div class="col-sm-5">

      {* štítky *}
      {include @productLabel.latte, product => $product}

      {*<!-- foto produktu start -->*}
      <div class="product-detail__image gallery">
        <a href="{$baseUri}/{$productMasterData|getProductPicName:'big'}" title="{$product->proname}">
          <img src="{$baseUri}/{($productMasterData|getProductPicName:'detail')|noescape}" alt="{$product->proname}" itemprop="image">
        </a>
      </div>
      {*<!-- foto produktu end -->*}

      {*<!-- další fotografie start -->*}
      <div class="product-detail__gallery gallery">
        {foreach $images as $row}
        <a href="{$baseUri.'/pic/product/big/'.$row["name"]}" title="{$product->proname}">
          <img src="{$baseUri.'/pic/product/list/'.$row["name"]}" alt="{$product->proname}">
        </a>
        {/foreach}
      </div>
      {*<!-- další fotografie end -->*}

    </div>
    {*<!-- boční lišta end -->*}

  </div>

  <div class="row">

    {*<!-- variants start -->*}
    <div id="variants" class="col-xs-12 product-detail__variants article">

      {if count($subItems) > 0}
        {form basketAddFormVar}
        {* vypise chyby formulare *}
        {include ../@formErrors.latte form=>$form}

        {foreach $subItems as $row}
        {php
          if ($productMasterData->proaccess == 100) $row->proaccess = $productMasterData->proaccess;
        }
        {if $iterator->isFirst()}
          {if $row->promasid > 0}<h2>Vyberte si variantu produktu</h2>{/if}
          <table class="table table--variants" cellpadding="3" cellspacing="0" border="1">
          <tr>
            <th>{if $row->promasid > 0}Název zboží{else}Název zboží{/if}</th>
            <th>Cena s DPH</th>
            <th>Dostupnost</th>
            <th>Počet kusů</th>
          </tr>
        {/if}
        <tr>
          <td>
            {if $row->proid != $product->proid}<a href="{plink Product:detail, $row->proid, ($row|getProKey)}">{/if}
            {if $row->promasid > 0}
              {php $img = ($row|getProductPicName:'usrsize':TRUE); }
              {if !empty($img)}
                <img src="{$baseUri}/{$img|noescape}" alt="{$row->proname}" width="50">
              {/if}
              {$row->proname}
            {else}
              {$product->proname}
            {/if}
            {if $row->proid != $product->proid}</a>{/if}
            {if $adminLogIn}<a href="{plink :Admin:Product:edit, $row->proid}" target="admin" class="control control--success"><i class="icon icon--wheel"></i></a>{/if}
          </td>
          <td><strong>{$row->proprice|formatPrice}</strong></td>
          <td>
            {if $row->proaccess == 0}
              <span class="stock stock--available">Skladem {if $row->proqty > 0}{$row->proqty|getQty}{/if}</span>
            {elseif !empty($row->proaccesstext)}
              <br><span class="stock stock--available">{$row->proaccesstext}</span>
            {elseif $row->proaccess != 0}
              <span class="stock stock--unavailable">{$enum_proaccess[$row->proaccess]}</span>
            {/if}
          </td>
          <td>
            {ifset $form[$row->proid]}
            <span class="control--count">{php echo $form[$row->proid]['oricnt']->control->size(3)->class('clearonfocus') }</span>
            {/ifset}
          </td>
        </tr>
        {if $iterator->isLast()}
        </table>
        {if ($this->config["CHECK_STOCK"] == 1 && $productMasterData->proaccess == 0) || $this->config["CHECK_STOCK"] == 0}
        <p>{php echo $form['buy']->control->class('btn btn--buy btn--big'); }</p>
        {/if}
        {/if}
        {/foreach}
        {/form basketAddFormVar}
      {/if}

    </div>
    {*<!-- variants end -->*}

  </div>

  {*<!-- detail produktu start -->*}
  <div class="row article">

    <div class="col-xs-12">

      <h2>{$product->proname}</h2>

      {* text produktu *}
      {$productMasterData->prodesc|noescape}

      {* youtube videa *}
      {if !empty($product->provideo)}
      <h2>Prohlédněte si video</h2>
      <div id="video"><div><iframe width="560" height="315" src="https://www.youtube.com/embed/{$product->provideo}"  allowfullscreen></iframe></div></div>
      {/if}

      {* přílohy *}
      {foreach $files as $file}
        {if $iterator->first}
        <div class="article__attachements">
          <h3>Přílohy</h3>
          <ul>
          {/if}
            <li><a href="{$baseUri}/files/{$file->atafilename}" target="_blank"><i class="icon icon--download"></i>{$file->ataname}</a></li>
          {if $iterator->last}
          </ul>
        </div>
        {/if}
      {/foreach}

      {foreach $proParams as $row}
        {if $iterator->isFirst()}
          <h2>{_'Parametry'}</h2>
          <table class="table table--parameters" cellpadding="5" cellspacing="0" border="1">
        {/if}
        <tr {if $iterator->isEven()} class="even"{/if}>
          <td>{$row->prpname}:</td>
          <td><strong>{$row->prpvalue}</strong></td>
        </tr>
        {if $iterator->isLast()}
          </table>
        {/if}
      {/foreach}

    </div>

  </div>
  {*<!-- detail produktu end -->*}

{else}
  <p><strong>Tato položka byla vyřazena z nabídky.</strong></p>

  {* výpis produktů *}
  {include @productsList.latte, products => $footerTopProducts, title => 'Nejoblíbenější produkty'}

{/if}

</div>
{*<!-- product detail end -->*}

{* <!-- prislusenstvi zbozi start --> *}
{if count($proAccess) > 0}
  <h2>Související zboží</h2>
  {include @productsList.latte, products => $proAccess, title => 'Související zboží'}
{/if}
{* <!-- prislusenstvi zbozi end --> *}

{/block}
