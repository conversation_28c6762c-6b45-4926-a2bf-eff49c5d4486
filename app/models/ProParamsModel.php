<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ProParamsModel extends BaseModel {

  protected $tableName = "proparams";
  protected $fieldPrefix = "prp";


  public function getEnumParamVals() {
    $cache = $this->cacheGet('paramVals');
    if (!$cache) {
      unset($cache);
      $arr = array();
      //poskladam vsechny nazvy parametru
      $rows = $this->getEnumParamNames();
      foreach ($rows as $name) {
        $arr[$name] = array(0=>"");
        $arr[$name] += dibi::query("SELECT prpid,prpvalue FROM proparams WHERE prpname=%s", $name, " GROUP BY prpvalue")->fetchPairs("prpvalue","prpvalue");
      }
      $this->cacheSave('paramVals', $arr);
      $cache = $arr;
    }
    return $cache;
  }

  public function getEnumParamNames() {
    $cache = $this->cacheGet('paramNames');
    if (!$cache) {
      unset($cache);
      $arr = array();
      //poskladam vsechny nazvy parametru
      $arr = dibi::query("SELECT prpname FROM proparams WHERE prpvalue != '' GROUP BY prpname")->fetchPairs("prpname","prpname");
      $this->cacheSave('paramNames', $arr);
      $cache = $arr;
    }
    return $cache;
  }


}
