// box sizing
.boxsizing() {

  html {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  *, *:before, *:after {
    -moz-box-sizing: inherit;
    -webkit-box-sizing: inherit;
    box-sizing: inherit;
  }

  img {
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
  }

}

// clearfix
.clear() {

  zoom: 1;

  &:before, &:after {
    content: "";
    display: table;
  }

  &:after {
    clear:both;
  }

}

// mini mřížka
.grid(@cols:1, @gut:1) {
  width: ( ( 100 - ( @gut * ( @cols - 1) ) ) / @cols ) * 1%;
  margin-top: 0;
  margin-left: 0;
  margin-bottom: @gut * 1%;
  margin-right: @gut * 1%;
}
