{$pageTitle       = (empty($article->arttitle) ? $article->artname :$article->arttitle)}
{$pageKeywords    = $article->artkeywords}
{$pageDescription = $article->artdescription}
{$pageImage       = $baseUri."/". ($article|getArtPicName:'detail')}

{block #content}

  <div class="article">

    <h1>{$article->artname}</h1>

    {$article->artbody|noescape}

    {foreach $images as $row}
    {if $iterator->isFirst()}

      <h3><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h3>

      <div class="article__images gallery">
    {/if}
        <a href="{$baseUri}/files/{$row->atafilename}" title="{$row->ataname}"><img src="{$baseUri}/files/{$row->atafilename}" alt="{$row->ataname}"></a>
    {if $iterator->isLast()}
      </div>
    {/if}
    {/foreach}

    {foreach $attachments as $row}
    {if $iterator->isFirst()}
    <div class="article__attachements">

      <h3>Př<PERSON>lohy</h3>

      <ul>
    {/if}
        <li><a href="{$baseUri}/files/{$row->atafilename}" target="_blank"><i class="icon icon--download"></i>{$row->ataname} ({$row->atasize|bytes})</a></li>
    {if $iterator->isLast()}
      </ul>

    </div>
    {/if}
    {/foreach}

  </div>

{/block}
