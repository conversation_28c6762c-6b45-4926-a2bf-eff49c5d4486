<?php

namespace Heureka\ShopCertification;

/**
 * <AUTHOR> <vladim<PERSON>.<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
interface IRequester
{

    const ACTION_LOG_ORDER = 'order/log';

    /**
     * @param ApiEndpoint $endpoint
     */
    public function setApiEndpoint(ApiEndpoint $endpoint);

    /**
     * @param string $action @see self::ACTION_*
     * @param array  $data
     *
     * @return Response
     * @throws RequesterException
     */
    public function request($action, array $data);

}

class RequesterException extends Exception {}
