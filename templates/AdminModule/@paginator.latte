{if $paginator->pageCount > 1}
<ul class="pagination">
	{if $paginator->isFirst()}
	<li class="disabled"><span>« Předchozí</span></li>
	{else}
	<li><a href="{link $handle, 'page' => $paginator->page - 1}" rel="prev" n:class="$useAjax ? ajax">« Předchozí</a></li>
	{/if}

	{foreach $steps as $step}
	{if $step == $paginator->page}
	<li class="active"><span>{$step}</span></li>
	{else}
	<li><a href="{link $handle, 'page' => $step}" n:class="$useAjax ? ajax">{$step}</a></li>
	{/if}
	{if $iterator->nextValue > $step + 1}<li class="disabled"><span>…</span></li>{/if}
	{/foreach}

	{if $paginator->isLast()}
	<li class="disabled"><span><PERSON><PERSON><PERSON> »</span></li>
	{else}
	<li><a href="{link $handle, 'page' => $paginator->page + 1}" rel="next" n:class="$useAjax ? ajax">Další »</a></li>
	{/if}
</ul>
{/if}
