<?php


namespace FrontModule;
use dibi;
use Model\DeliveryModesModel;
use Nette\Application\ForbiddenRequestException;

final class ApiPresenter extends BasePresenter {

	public function startup(): void
  {
    $token = $this->getParameter('token');
    if ($token !== self::getToken()) {
      throw new ForbiddenRequestException('Invalid token.', 401);
    }
    parent::startup();
  }
  public function renderOrdersExport(): void {
      $this->setLayout(false);
      $delModes = new DeliveryModesModel();
      $template = $this->createTemplate();
      $orders = dibi::fetchAll("select * from orders where ordtransfered=0");
      foreach ($orders as $key => $order) {
        $orders[$key]->ordItems = dibi::fetchAll("
          SELECT *
          FROM orditems
          LEFT JOIN products ON (oriproid=proid)
          WHERE oriordid=%i", $order->ordid, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
        $delModes->setCurrency($this->currencies, $orders[$key]->ordcurid);
        $orders[$key]->payMode = $delModes->load($order->orddelid);
        $orders[$key]->delMode = $delModes->load($orders[$key]->payMode->delmasid);
      }

      $template->orders = $orders;

      $template->setFile(WWW_DIR.'/../templates/json/ordersJson.latte');
      $this->getHttpResponse()->setHeader('Content-Type', 'application/json; charset=utf-8');
      echo (string)$template;
      $this->terminate();
	}

  public function renderOrdersSetTransfered(array $ordCodes = []): void {
    foreach ($ordCodes as $ordId) {
      dibi::query('UPDATE orders SET ordtransfered=1 WHERE ordcode=%s', $ordId);
    }
    $this->terminate();
  }

  public static function getToken(): string
  {
    $key = 'ad9d158b-cc08-4093-8555-abccff894bac';
    $date = date('dym');
    return hash('sha256', $key . $date);
  }


}
