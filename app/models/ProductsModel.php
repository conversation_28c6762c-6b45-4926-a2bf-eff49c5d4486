<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class ProductsModel extends BaseModel {

  protected $tableName = "products";
  protected $fieldPrefix = "pro";

  public function getDataSource($sql="") {
    if (is_array($sql)) {
      if (count($sql) == 0) $sql = $this->getSql();
    } else if ($sql == "") {
      $sql = $this->getSql();
    }
    return dibi::dataSource($sql);
  }

  public function getSql() {
    return "
      SELECT $this->tableName.*, manname,
      proname, ".($this->curId == 1 ? "prodelfree" : "0 AS prodelfree").",
      IF(proprice".$this->curId.$this->prccat.">0,proprice".$this->curId.$this->prccat.",proprice".$this->curId."a) AS proprice,
      proprice".$this->curId."com AS propricecom
      FROM $this->tableName
      INNER JOIN manufacturers ON (promanid=manid)
    ";
  }

  public function getPriceField($prccat="") {
    $prccatsufix = "";
    if ($prccat == "") {
      $prccat = $this->prccat;
    } else {
      $prccatsufix = $prccat;
    }
    $sql = "IF(proprice".$this->curId.$prccat.">0,proprice".$this->curId.$prccat.",proprice".$this->curId."a) AS proprice".$prccatsufix;
    return ($sql);
  }

  public function getProCatRootId($proid) {
    $catPathIds = (string)dibi::fetchSingle("SELECT catpathids FROM catalogs INNER JOIN catplaces ON capcatid=catid WHERE capproid=%i", $proid);
    $arr = explode("|", trim($catPathIds, "|"));
    if (!empty($arr[0])) {
      return $arr[0];
    }  else {
      return false;
    }
  }

  /**
   * @param $product
   * @throws \Dibi\Exception
   */
  public function setProCatRootId($product) {
      $prorootid = (int)$this->getProCatRootId($product->proid);
      if ($prorootid == 0 && (int)$product->promasid > 0) {
        $prorootid = (int)$this->getProCatRootId($product->promasid);
      }
      if ($prorootid > 0) {
        //zjistim jestli kategorie existuje
        $cnt = 0;
        if ((int)$product->procatrootid > 0) {
          $cnt = (int)dibi::fetchSingle("SELECT COUNT(catid) FROM catalogs WHERE catid=%i", $product->procatrootid);
        }
        if ($cnt == 0 && $prorootid != (int)$product->procatrootid) {
          $this->update($product->proid, array("procatrootid" => $prorootid));
        }
      }
  }

  public function getGifts($product) {
    $progifts = "";
    if (!empty($product->progifts)) {
      $progifts = $product->progifts;
    } else {
      //pokud jde o variantu a varianta nemá dárek načítám dárek z master položky
      if ($product->promasid > 0) {
        $progifts = dibi::fetchSingle("SELECT progifts FROM products WHERE proid=%i", $product->promasid);
      }
    }
    //nactu darky
    if (!empty($progifts)) {
      $arr = explode(',', trim($progifts, ','));
      return dibi::fetchAll("SELECT * FROM products WHERE procode IN (%s)", $arr);
    } else {
      return array();
    }
  }

  /**
  * nastavi cenovou kategorii pro preddefinovane SQL
  *
  */
  public function setPrcCat($val) {
    if ($val == "") $val = "a";
    $this->prccat = $val;
  }

  public function fetchAllList($where) {
    return parent::fetchAll($where);
  }

  /**
  * prepocita statistiku prodejnosti
  *
  */
  public function recalcSaleStat() {
    dibi::query("DELETE FROM products_salestat");

    //x dni stara statistika
    $daysArr = array(10, 30);
    foreach ($daysArr as $days) {
      dibi::query("
    INSERT INTO products_salestat
    SELECT oriproid, SUM(oriqty), NULL, $days, NOW(), NULL
    FROM orditems
    INNER JOIN orders ON (oriordid=ordid)
    WHERE oritypid=0 AND
    ordstatus IN (3,4) AND
    ((COALESCE(orddateu, orddatec) + INTERVAL $days DAY) >= CURDATE())
    GROUP BY oriproid
    ");
    }

    //nactu vsechny produkty ze statistik a nactu jejich zarazeni do katalogu
    $rows = dibi::fetchAll("
    SELECT prsproid, catpathids
    FROM products_salestat
    INNER JOIN products AS pro ON (prsproid=pro.proid)
    LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
    INNER JOIN catplaces ON (capproid=COALESCE(promas.proid,pro.proid))
    INNER JOIN catalogs ON (capcatid=catid)
    GROUP BY prsproid");
    foreach ($rows as $key => $row) {
      dibi::query("UPDATE products_salestat SET prscatpathids=%s", $row->catpathids, " WHERE prsproid=%i", $row->prsproid);
    }

    $catalog = new CatalogsModel();
    $catalog->cacheClean();
  }

  /**
  * aktualizuje zarazeni do katalogu
  *
  * @param integer $proId
  * @param array $catPlaces
  */
  public function updateCatPlace($proId, $catIds) {
    $idlst = "";
    $catplace = new CatPlacesModel();
    foreach ($catIds as $key => $value) {
      $idlst .= $value.",";
      //zjistim jestli zaznam existuje
      $cnt = (integer)dibi::fetchSingle("SELECT count(*) FROM catplaces WHERE capcatid=$value AND capproid=$proId");
      //zaznam neexistuje
      $vals = array();
      if ($cnt == 0) {
        $vals = array('capcatid' => $value, 'capproid' => $proId);
        $catplace->insert($vals);
      }
    }
    $idlst = trim($idlst, ',');
    //vsechny ostatni zaznamy vymazu
    dibi::query("DELETE FROM catplaces WHERE capproid=$proId".($idlst != "" ? " AND capcatid NOT IN ($idlst)" : ""));

    $catrootid = $this->getProCatRootId($proId);
    if ($catrootid > 0) $this->update($proId, array("procatrootid"=>$catrootid));
  }

  /**
  * kontrola duplicity kodu zbozi
  *
  * @param integer $id - id polozky pokud se jedna o editaci
  * @param string $value  - kod zbozi
  */
  private function checkDuplicityProCode($id, $value) {
    $cnt = dibi::fetchSingle("SELECT COUNT(*) AS cnt FROM $this->tableName WHERE procode='$value'".($id>0 ? " AND proid!=$id" : ""));
    if ($cnt > 0) throw New ModelException("Tento kód zboží již existuje.");
  }

  public function save(&$id, $data, $setDateU = True) {
    if ($data["procode"] != "") $this->checkDuplicityProCode($id, $data["procode"]);
    return parent::save($id, $data, $setDateU);
  }

  public function update($id, $data,$setDateU = True) {
    if (!empty($data["prokey"])) $data["prokey"] = Nette\Utils\Strings::webalize($data["prokey"]);
    $pp = $this->load($id);
    $ret = parent::update($id,$data,$setDateU);
    //zjistim jestli se zmenila hodnota promasid
    if (isset($data["promasid"])) {
      if ($data["promasid"] > 0) $this->refreshMasterStatus($data["promasid"]);
      if ($pp->promasid > 0) $this->refreshMasterStatus($pp->promasid);
    }
    return $ret;
  }

  public function insert($data) {
    if (!empty($data["prokey"])) $data["prokey"] = Nette\Utils\Strings::webalize($data["prokey"]);
    $ret = parent::insert($data);
    //pokud vklada novou polozku jako podrizenou refreshnu masterstatus
    if (isset($data["promasid"])) {
      if ($data["promasid"] > 0) $this->refreshMasterStatus($data["promasid"]);
    }
    return $ret;
  }

  public function refreshMasterStatus($proid) {
    $cnt = dibi::fetchSingle("SELECT COUNT(proid) AS cnt FROM products WHERE promasid=%i", $proid);
    $proIsMaster = (int)($cnt > 0);
    $vals = array('proismaster'=>$proIsMaster);
    $this->update($proid, $vals);
  }

  public function refreshOnStockStatus($proid) {
    $cnt = dibi::fetchSingle("SELECT COUNT(proid) AS cnt FROM products WHERE promasid=%i", $proid);
    $proIsMaster = (int)($cnt > 0);
    $vals = array('proismaster'=>$proIsMaster);
    $this->update($proid, $vals);
  }

  public function delete($id) {
    $p = $this->load($id);
    $ret = parent::delete($id);

    //pokud ma master polozku refreshnu jeji master status
    if ($p->promasid > 0) $this->refreshMasterStatus($p->promasid);

    //pokud mazu master polozku odparuju podrizene polozky
    dibi::query("UPDATE products SET promasid=0 WHERE promasid=%i", $p->proid);

    //vymazu prilohy
    dibi::query("DELETE FROM attachments WHERE ataproid=%i", $id);
    //vymazu zarazeni do catalogu
    dibi::query("DELETE FROM catplaces WHERE capproid=%i", $id);
    //vymazu products_salestat
    dibi::query("DELETE FROM products_salestat WHERE prsproid=%i", $id);
    //vymazu proparams
    dibi::query("DELETE FROM proparams WHERE prpproid=%i", $id);
    return ($ret);
  }

  /********************* preddefinovane SQL *********************/

  /**
  * vraci SQL SELECT
  * seznam zbozi
  *
  * @param string $where
  * @return string
  */
  public function getSqlList($where, $orderBy="proorder") {
      $sql = "SELECT proid, protypid, promasid, proismaster, progifts, protypid2, protypid3, protypid4, protypid5, prokey, procode, proname, proname2, propicname, pronames, prodescs, proaccess, proaccesstext, proqty, proprice".$this->curId."com AS pricecom,
    IF(proprice".$this->curId.$this->prccat.">0,proprice".$this->curId.$this->prccat.",proprice".$this->curId."a) AS proprice, proisunpacked, proisused, proisone,prodiscountinfo,
    proprice".$this->curId."a AS propricea, proprice".$this->curId."b AS propriceb, proprice".$this->curId."c AS propricec, proprice".$this->curId."d AS propriced,
    proprice".$this->curId."com AS propricecom, provatid, prostatus, proorder, manname, prorating, ".($this->curId == 1 ? "prodelfree" : "0 AS prodelfree").", pronotdisc
FROM products
LEFT JOIN manufacturers ON (promanid=manid)";
    if (is_array($where)) {
      $query[] = $sql;
      if (count($where) > 0) $query[] = " WHERE ";
      if ($orderBy != "") $orderBy = "ORDER BY $orderBy";
      $query = array_merge($query, $where);
      array_push($query, " GROUP BY proid");
      array_push($query, $orderBy);
      return($query);
    } else {
      if ($where != "") $where = " WHERE $where";
      if ($orderBy != "") $orderBy = "ORDER BY $orderBy";
    }
    return("$sql $where $orderBy");
  }

  /**
  * vraci SQL SELECT
  * seznam zbozi v katalogu
  *
  * @param string $catid - id urovne katalogu
  * @param string $where
  * @return string
  */
  public function getSqlCatalogList($catid, $where = "", $orderBy="proorder", $subitems=FALSE) {
    if ($where != "") $where = " AND ($where)";
    if ($orderBy != "") $orderBy = "ORDER BY $orderBy";
    return("
SELECT proid, promasid, proismaster, progifts, protypid, protypid2, protypid3, protypid4, protypid5, prokey, procode, proname, pronames, prodescs, proaccess, proaccesstext, proqty, proprice".$this->curId."com AS propricecom,
propicname, IF(proprice".$this->curId.$this->prccat.">0,proprice".$this->curId.$this->prccat.",proprice".$this->curId."a) AS proprice, proisunpacked, proisused, proisone,
provatid, ".($this->curId == 1 ? "prodelfree" : "0 AS prodelfree").", pronotdisc, prostatus, proorder, manname, prorating
FROM products
INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (capcatid=catid)
LEFT JOIN manufacturers ON (promanid=manid)
WHERE ".($subitems ? "" : " promasid=0 AND ")." catpathids LIKE '%|$catid|%'
$where
GROUP BY proid
$orderBy");
  }

  public function fillProCatRootId($proid = 0) {
    $rows = dibi::fetchAll("SELECT proid, promasid, procatrootid from products".($proid > 0 ? " AND proid=".$proid : " coalesce(procatrootid,0)=0"));
    foreach ($rows as $row) {
      $prorootid = (int)$this->getProCatRootId($row->proid);
      if ($prorootid == 0 && (int)$row->promasid > 0) {
        $prorootid = (int)$this->getProCatRootId($row->promasid);
      }
      if ($prorootid > 0) {
        //zjistim jestli kategorie existuje
        $cnt = 0;
        if ((int)$row->procatrootid > 0) {
          $cnt = (int)dibi::fetchSingle("SELECT COUNT(catid) FROM catalogs WHERE catid=%i", $row->procatrootid);
        }
        if ($cnt == 0 && $prorootid != (int)$row->procatrootid) $this->update($row->proid, array("procatrootid" => $prorootid));
      }
    }
  }

  /**
  * vraci obsah cache
  *
  * @param string $key - identifikator promenne
  */
  public function cacheGet($key) {
    $key = $key.'_'.$this->curId.$this->prccat;
    return parent::cacheGet($key);
  }

  /**
  * ulozi cache
  *
  * @param string $key - identifikator promenne
  * @param mixed $data
  */
  public function cacheSave($key, $data) {
    $key = $key.'_'.$this->curId.$this->prccat;
    return parent::cacheSave($key, $data);
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik prostatus
  * @return array
  */
  public function getEnumProStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  /**
  * ciselnik vyrobcu
  * @return array
  */
  public function getEnumProManId() {
    return dibi::query("SELECT manid, manname FROM manufacturers ORDER BY manname")
      ->fetchPairs('manid', 'manname');
  }

  /**
  * ciselnik sazby DPH zbozi
  * @return array
  */
  public function getEnumProVatId() {
    return array(
      0 => 'Základní',
      1 => 'Snížená',
      2 => 'Třetí',
    );
  }

  /**
  * ciselnik dostupnosti
  * @return array
  */
  public function getEnumProAccess() {
    return array(
      0 => 'Skladem',
      3 => 'do 3 dnů',
      7 => 'do týdne',
      14 => 'do 2 týdnů',
      30 => 'do měsíce',
      32 => 'více než měsíc',
      100 => 'není skladem',
    );
  }

    /**
  * ciselnik puvod
  * @return array
  */
  public function getEnumProOrigin() {
    return array(
      'neznámá' => 'Neznámá',
      'CZ' => 'Česká distribuce',
      'EU' => 'Distribuce EU',
      'mimo EU' => 'Mimo EU',
    );
  }

  /**
  * ciselnik ikonka ke zbozi
  * @return array
  */
  public function getEnumProIcons() {
    return array(
  'darek.png' => 'Dárek zdarma',
    );
  }

  public function getDiscounts($proId): array {
    return dibi::fetchAll("SELECT * FROM product_discounts WHERE disproid=? ORDER BY disqty", $proId);
  }

  public function getDiscountByQty($row, $qty, $proPriceCat = "proprice1a") {
    $discInPer = 0;
    if (!empty($row->prodiscountinfo) && $qty > 1) {
      $discounts = $this->getDiscounts($row->proid);
      foreach ($discounts as $discount) {
        if ($qty >= $discount->disqty) {
          $discInPer = $discount->disdiscount;
        }
      }
    }

    return $discInPer;
  }
}
