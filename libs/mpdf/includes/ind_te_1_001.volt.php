<?php
	$volt = array (
  0 => 
  array (
    'match' => '0C4D 0C30 ((0C15|0C16|0C17|0C18|0C19|0C1A|0C1B|0C1C|0C1D|0C1E|0C1F|0C20|0C21|0C22|0C23|0C24|0C25|0C26|0C27|0C28|0C2A|0C2B|0C2C|0C2D|0C2E|0C2F|0C30|0C31|0C32|0C33|0C35|0C36|0C37|0C38|0C39))',
    'replace' => 'E046 \\1',
  ),
  1 => 
  array (
    'match' => '0C4D 200D',
    'replace' => '00C9',
  ),
  2 => 
  array (
    'match' => '0C4D 200C',
    'replace' => '00D0',
  ),
  3 => 
  array (
    'match' => '200D 0C4D',
    'replace' => '00D1',
  ),
  4 => 
  array (
    'match' => '((0C15|0C16|0C17|0C18|0C19|0C1A|0C1B|0C1C|0C1D|0C1E|0C1F|0C20|0C21|0C22|0C23|0C24|0C25|0C26|0C27|0C28|0C2A|0C2B|0C2C|0C2D|0C2E|0C2F|0C30|0C31|0C32|0C33|0C35|0C36|0C37|0C38|0C39)) 0C4D',
    'replace' => '\\1 00D1',
  ),
  5 => 
  array (
    'match' => '((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C)) 0C4D',
    'replace' => '\\1 00D1',
  ),
  6 => 
  array (
    'match' => '((0C41|0C42|0C43|0C44)) 0C4D',
    'replace' => '\\1 00D1',
  ),
  7 => 
  array (
    'match' => '(0020) 0C4D',
    'replace' => '\\1 00D1',
  ),
  8 => 
  array (
    'match' => '(25CC) 0C4D',
    'replace' => '\\1 00D1',
  ),
  9 => 
  array (
    'match' => '0C15 00D1 0C37',
    'replace' => 'E078',
  ),
  10 => 
  array (
    'match' => '0C36 00D1 0C1C',
    'replace' => 'E079',
  ),
  11 => 
  array (
    'match' => '00D1 0C15',
    'replace' => 'E02C',
  ),
  12 => 
  array (
    'match' => '00D1 0C16',
    'replace' => 'E02D',
  ),
  13 => 
  array (
    'match' => '00D1 0C17',
    'replace' => 'E02E',
  ),
  14 => 
  array (
    'match' => '00D1 0C18',
    'replace' => 'E02F',
  ),
  15 => 
  array (
    'match' => '00D1 0C19',
    'replace' => 'E030',
  ),
  16 => 
  array (
    'match' => '00D1 0C1A',
    'replace' => 'E031',
  ),
  17 => 
  array (
    'match' => '00D1 0C1B',
    'replace' => 'E032',
  ),
  18 => 
  array (
    'match' => '00D1 0C1C',
    'replace' => 'E033',
  ),
  19 => 
  array (
    'match' => '00D1 0C1D',
    'replace' => 'E034',
  ),
  20 => 
  array (
    'match' => '00D1 0C1E',
    'replace' => 'E035',
  ),
  21 => 
  array (
    'match' => '00D1 0C1F',
    'replace' => 'E036',
  ),
  22 => 
  array (
    'match' => '00D1 0C20',
    'replace' => 'E037',
  ),
  23 => 
  array (
    'match' => '00D1 0C21',
    'replace' => 'E038',
  ),
  24 => 
  array (
    'match' => '00D1 0C22',
    'replace' => 'E039',
  ),
  25 => 
  array (
    'match' => '00D1 0C23',
    'replace' => 'E03A',
  ),
  26 => 
  array (
    'match' => '00D1 0C24',
    'replace' => 'E03B',
  ),
  27 => 
  array (
    'match' => '00D1 0C25',
    'replace' => 'E03C',
  ),
  28 => 
  array (
    'match' => '00D1 0C26',
    'replace' => 'E03D',
  ),
  29 => 
  array (
    'match' => '00D1 0C27',
    'replace' => 'E03E',
  ),
  30 => 
  array (
    'match' => '00D1 0C28',
    'replace' => 'E03F',
  ),
  31 => 
  array (
    'match' => '00D1 0C2A',
    'replace' => 'E040',
  ),
  32 => 
  array (
    'match' => '00D1 0C2B',
    'replace' => 'E041',
  ),
  33 => 
  array (
    'match' => '00D1 0C2C',
    'replace' => 'E042',
  ),
  34 => 
  array (
    'match' => '00D1 0C2D',
    'replace' => 'E043',
  ),
  35 => 
  array (
    'match' => '00D1 0C2E',
    'replace' => 'E044',
  ),
  36 => 
  array (
    'match' => '00D1 0C2F',
    'replace' => 'E045',
  ),
  37 => 
  array (
    'match' => '00D1 0C30',
    'replace' => 'E046',
  ),
  38 => 
  array (
    'match' => '00D1 0C31',
    'replace' => 'E047',
  ),
  39 => 
  array (
    'match' => '00D1 0C32',
    'replace' => 'E048',
  ),
  40 => 
  array (
    'match' => '00D1 0C33',
    'replace' => 'E049',
  ),
  41 => 
  array (
    'match' => '00D1 0C35',
    'replace' => 'E04A',
  ),
  42 => 
  array (
    'match' => '00D1 0C36',
    'replace' => 'E04B',
  ),
  43 => 
  array (
    'match' => '00D1 0C37',
    'replace' => 'E04C',
  ),
  44 => 
  array (
    'match' => '00D1 0C38',
    'replace' => 'E04D',
  ),
  45 => 
  array (
    'match' => '00D1 0C39',
    'replace' => 'E04E',
  ),
  46 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E02C',
    'replace' => '\\1 E04F',
  ),
  47 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E02D',
    'replace' => '\\1 E050',
  ),
  48 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E02E',
    'replace' => '\\1 E051',
  ),
  49 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E02F',
    'replace' => '\\1 E052',
  ),
  50 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E030',
    'replace' => '\\1 E053',
  ),
  51 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E031',
    'replace' => '\\1 E054',
  ),
  52 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E032',
    'replace' => '\\1 E055',
  ),
  53 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E033',
    'replace' => '\\1 E056',
  ),
  54 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E034',
    'replace' => '\\1 E057',
  ),
  55 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E035',
    'replace' => '\\1 E058',
  ),
  56 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E036',
    'replace' => '\\1 E059',
  ),
  57 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E037',
    'replace' => '\\1 E05A',
  ),
  58 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E038',
    'replace' => '\\1 E05B',
  ),
  59 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E039',
    'replace' => '\\1 E05C',
  ),
  60 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E03A',
    'replace' => '\\1 E05D',
  ),
  61 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E03B',
    'replace' => '\\1 E05E',
  ),
  62 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E03C',
    'replace' => '\\1 E05F',
  ),
  63 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E03D',
    'replace' => '\\1 E060',
  ),
  64 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E03E',
    'replace' => '\\1 E061',
  ),
  65 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E03F',
    'replace' => '\\1 E062',
  ),
  66 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E040',
    'replace' => '\\1 E063',
  ),
  67 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E041',
    'replace' => '\\1 E064',
  ),
  68 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E042',
    'replace' => '\\1 E065',
  ),
  69 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E043',
    'replace' => '\\1 E066',
  ),
  70 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E044',
    'replace' => '\\1 E067',
  ),
  71 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E045',
    'replace' => '\\1 E068',
  ),
  72 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E046',
    'replace' => '\\1 E069',
  ),
  73 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E047',
    'replace' => '\\1 E06A',
  ),
  74 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E048',
    'replace' => '\\1 E06B',
  ),
  75 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E049',
    'replace' => '\\1 E06C',
  ),
  76 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E04A',
    'replace' => '\\1 E06D',
  ),
  77 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E04B',
    'replace' => '\\1 E06E',
  ),
  78 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E04C',
    'replace' => '\\1 E06F',
  ),
  79 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E04D',
    'replace' => '\\1 E070',
  ),
  80 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) E04E',
    'replace' => '\\1 E071',
  ),
  81 => 
  array (
    'match' => '((E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071)) E02C',
    'replace' => '\\1 E072',
  ),
  82 => 
  array (
    'match' => '((E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071)) E03F',
    'replace' => '\\1 E073',
  ),
  83 => 
  array (
    'match' => '((E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071)) E044',
    'replace' => '\\1 E074',
  ),
  84 => 
  array (
    'match' => '((E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071)) E045',
    'replace' => '\\1 E075',
  ),
  85 => 
  array (
    'match' => '((E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071)) E04A',
    'replace' => '\\1 E076',
  ),
  86 => 
  array (
    'match' => '((E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071)) E046',
    'replace' => '\\1 E077',
  ),
  87 => 
  array (
    'match' => '00C9',
    'replace' => '0C4D',
  ),
  88 => 
  array (
    'match' => '00D0',
    'replace' => '0C4D',
  ),
  89 => 
  array (
    'match' => '00D1',
    'replace' => '0C4D',
  ),
  90 => 
  array (
    'match' => '0C2A ((0C3E|0C4A|0C4B|0C4C))',
    'replace' => 'E028 \\1',
  ),
  91 => 
  array (
    'match' => '0C2B ((0C3E|0C4A|0C4B|0C4C))',
    'replace' => 'E029 \\1',
  ),
  92 => 
  array (
    'match' => '0C37 ((0C3E|0C4A|0C4B|0C4C))',
    'replace' => 'E02A \\1',
  ),
  93 => 
  array (
    'match' => '0C38 ((0C3E|0C4A|0C4B|0C4C))',
    'replace' => 'E02B \\1',
  ),
  94 => 
  array (
    'match' => 'E07B ((0C3E|0C4A|0C4B|0C4C))',
    'replace' => 'E0B2 \\1',
  ),
  95 => 
  array (
    'match' => '((E028|E029|E02A|E02B)) 0C4A',
    'replace' => '\\1 E009',
  ),
  96 => 
  array (
    'match' => '((E028|E029|E02A|E02B)) 0C4B',
    'replace' => '\\1 E00A',
  ),
  97 => 
  array (
    'match' => '((E028|E029|E02A|E02B)) 0C4C',
    'replace' => '\\1 E00B',
  ),
  98 => 
  array (
    'match' => '0C15 0C41',
    'replace' => 'E07F',
  ),
  99 => 
  array (
    'match' => '0C15 0C42',
    'replace' => 'E080',
  ),
  100 => 
  array (
    'match' => '0C16 0C3F',
    'replace' => 'E081',
  ),
  101 => 
  array (
    'match' => '0C16 0C40',
    'replace' => 'E082',
  ),
  102 => 
  array (
    'match' => '0C18 0C4A',
    'replace' => 'E083',
  ),
  103 => 
  array (
    'match' => '0C18 0C4B',
    'replace' => 'E084',
  ),
  104 => 
  array (
    'match' => '0C19 0C41',
    'replace' => 'E085',
  ),
  105 => 
  array (
    'match' => '0C19 0C42',
    'replace' => 'E086',
  ),
  106 => 
  array (
    'match' => '0C1A 0C3F',
    'replace' => 'E087',
  ),
  107 => 
  array (
    'match' => '0C1A 0C40',
    'replace' => 'E088',
  ),
  108 => 
  array (
    'match' => '0C1B 0C3F',
    'replace' => 'E089',
  ),
  109 => 
  array (
    'match' => '0C1B 0C40',
    'replace' => 'E08A',
  ),
  110 => 
  array (
    'match' => '0C1C 0C3F',
    'replace' => 'E08B',
  ),
  111 => 
  array (
    'match' => '0C1C 0C40',
    'replace' => 'E08C',
  ),
  112 => 
  array (
    'match' => '0C1C 0C41',
    'replace' => 'E08D',
  ),
  113 => 
  array (
    'match' => '0C1C 0C42',
    'replace' => 'E08E',
  ),
  114 => 
  array (
    'match' => '0C1D 0C4A',
    'replace' => 'E08F',
  ),
  115 => 
  array (
    'match' => '0C1D 0C4B',
    'replace' => 'E090',
  ),
  116 => 
  array (
    'match' => '0C24 0C3F',
    'replace' => 'E091',
  ),
  117 => 
  array (
    'match' => '0C24 0C40',
    'replace' => 'E092',
  ),
  118 => 
  array (
    'match' => '0C28 0C3F',
    'replace' => 'E093',
  ),
  119 => 
  array (
    'match' => '0C28 0C40',
    'replace' => 'E094',
  ),
  120 => 
  array (
    'match' => '0C2C 0C3F',
    'replace' => 'E095',
  ),
  121 => 
  array (
    'match' => '0C2C 0C40',
    'replace' => 'E096',
  ),
  122 => 
  array (
    'match' => '0C2D 0C3F',
    'replace' => 'E097',
  ),
  123 => 
  array (
    'match' => '0C2D 0C40',
    'replace' => 'E098',
  ),
  124 => 
  array (
    'match' => '0C2E 0C3F',
    'replace' => 'E099',
  ),
  125 => 
  array (
    'match' => '0C2E 0C40',
    'replace' => 'E09A',
  ),
  126 => 
  array (
    'match' => '0C2E 0C4A',
    'replace' => 'E09B',
  ),
  127 => 
  array (
    'match' => '0C2E 0C4B',
    'replace' => 'E09C',
  ),
  128 => 
  array (
    'match' => '0C2F 0C3F',
    'replace' => 'E09D',
  ),
  129 => 
  array (
    'match' => '0C2F 0C40',
    'replace' => 'E09E',
  ),
  130 => 
  array (
    'match' => '0C2F 0C4A',
    'replace' => 'E09F',
  ),
  131 => 
  array (
    'match' => '0C2F 0C4B',
    'replace' => 'E0A0',
  ),
  132 => 
  array (
    'match' => '0C32 0C3F',
    'replace' => 'E0A1',
  ),
  133 => 
  array (
    'match' => '0C32 0C40',
    'replace' => 'E0A2',
  ),
  134 => 
  array (
    'match' => '0C33 0C3F',
    'replace' => 'E0A3',
  ),
  135 => 
  array (
    'match' => '0C33 0C40',
    'replace' => 'E0A4',
  ),
  136 => 
  array (
    'match' => '0C35 0C3F',
    'replace' => 'E0A5',
  ),
  137 => 
  array (
    'match' => '0C35 0C40',
    'replace' => 'E0A6',
  ),
  138 => 
  array (
    'match' => '0C36 0C41',
    'replace' => 'E0A7',
  ),
  139 => 
  array (
    'match' => '0C36 0C42',
    'replace' => 'E0A8',
  ),
  140 => 
  array (
    'match' => '0C36 0C3F',
    'replace' => 'E0A9',
  ),
  141 => 
  array (
    'match' => '0C36 0C40',
    'replace' => 'E0AA',
  ),
  142 => 
  array (
    'match' => '0C39 0C3E',
    'replace' => 'E0AB',
  ),
  143 => 
  array (
    'match' => '0C39 0C41',
    'replace' => 'E0AC',
  ),
  144 => 
  array (
    'match' => '0C39 0C42',
    'replace' => 'E0AD',
  ),
  145 => 
  array (
    'match' => 'E078 0C41',
    'replace' => 'E0AE',
  ),
  146 => 
  array (
    'match' => 'E078 0C42',
    'replace' => 'E0AF',
  ),
  147 => 
  array (
    'match' => 'E07A 0C48',
    'replace' => 'E0B0',
  ),
  148 => 
  array (
    'match' => 'E07B 0C48',
    'replace' => 'E0B1',
  ),
  149 => 
  array (
    'match' => 'E07A 0C3F',
    'replace' => 'E0DA',
  ),
  150 => 
  array (
    'match' => 'E07A 0C40',
    'replace' => 'E0DB',
  ),
  151 => 
  array (
    'match' => 'E07B 0C3F',
    'replace' => 'E0DC',
  ),
  152 => 
  array (
    'match' => 'E07B 0C40',
    'replace' => 'E0DD',
  ),
  153 => 
  array (
    'match' => '0C15 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E00D \\1',
  ),
  154 => 
  array (
    'match' => '0C17 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E00E \\1',
  ),
  155 => 
  array (
    'match' => '0C18 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E00F \\1',
  ),
  156 => 
  array (
    'match' => '0C1A ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E010 \\1',
  ),
  157 => 
  array (
    'match' => '0C1B ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E011 \\1',
  ),
  158 => 
  array (
    'match' => '0C1C ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E012 \\1',
  ),
  159 => 
  array (
    'match' => '0C1D ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E013 \\1',
  ),
  160 => 
  array (
    'match' => '0C20 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E014 \\1',
  ),
  161 => 
  array (
    'match' => '0C21 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E015 \\1',
  ),
  162 => 
  array (
    'match' => '0C22 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E016 \\1',
  ),
  163 => 
  array (
    'match' => '0C24 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E017 \\1',
  ),
  164 => 
  array (
    'match' => '0C25 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E018 \\1',
  ),
  165 => 
  array (
    'match' => '0C26 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E019 \\1',
  ),
  166 => 
  array (
    'match' => '0C27 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E01A \\1',
  ),
  167 => 
  array (
    'match' => '0C28 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E01B \\1',
  ),
  168 => 
  array (
    'match' => '0C2A ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E01C \\1',
  ),
  169 => 
  array (
    'match' => '0C2B ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E01D \\1',
  ),
  170 => 
  array (
    'match' => '0C2D ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E01E \\1',
  ),
  171 => 
  array (
    'match' => '0C2E ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E01F \\1',
  ),
  172 => 
  array (
    'match' => '0C2F ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E020 \\1',
  ),
  173 => 
  array (
    'match' => '0C30 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E021 \\1',
  ),
  174 => 
  array (
    'match' => '0C33 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E022 \\1',
  ),
  175 => 
  array (
    'match' => '0C35 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E023 \\1',
  ),
  176 => 
  array (
    'match' => '0C36 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E024 \\1',
  ),
  177 => 
  array (
    'match' => '0C37 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E025 \\1',
  ),
  178 => 
  array (
    'match' => '0C38 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E026 \\1',
  ),
  179 => 
  array (
    'match' => '0C39 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E027 \\1',
  ),
  180 => 
  array (
    'match' => 'E078 ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E07C \\1',
  ),
  181 => 
  array (
    'match' => 'E07A ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E07D \\1',
  ),
  182 => 
  array (
    'match' => 'E07B ((0C3E|0C3F|0C40|0C46|0C47|0C56|0C4A|0C4B|0C4C))',
    'replace' => 'E07E \\1',
  ),
  183 => 
  array (
    'match' => '0C15 (0C3E)',
    'replace' => 'E00D \\1',
  ),
  184 => 
  array (
    'match' => '0C17 (0C3E)',
    'replace' => 'E00E \\1',
  ),
  185 => 
  array (
    'match' => '0C18 (0C3E)',
    'replace' => 'E00F \\1',
  ),
  186 => 
  array (
    'match' => '0C1A (0C3E)',
    'replace' => 'E010 \\1',
  ),
  187 => 
  array (
    'match' => '0C1B (0C3E)',
    'replace' => 'E011 \\1',
  ),
  188 => 
  array (
    'match' => '0C1C (0C3E)',
    'replace' => 'E012 \\1',
  ),
  189 => 
  array (
    'match' => '0C1D (0C3E)',
    'replace' => 'E013 \\1',
  ),
  190 => 
  array (
    'match' => '0C20 (0C3E)',
    'replace' => 'E014 \\1',
  ),
  191 => 
  array (
    'match' => '0C21 (0C3E)',
    'replace' => 'E015 \\1',
  ),
  192 => 
  array (
    'match' => '0C22 (0C3E)',
    'replace' => 'E016 \\1',
  ),
  193 => 
  array (
    'match' => '0C24 (0C3E)',
    'replace' => 'E017 \\1',
  ),
  194 => 
  array (
    'match' => '0C25 (0C3E)',
    'replace' => 'E018 \\1',
  ),
  195 => 
  array (
    'match' => '0C26 (0C3E)',
    'replace' => 'E019 \\1',
  ),
  196 => 
  array (
    'match' => '0C27 (0C3E)',
    'replace' => 'E01A \\1',
  ),
  197 => 
  array (
    'match' => '0C28 (0C3E)',
    'replace' => 'E01B \\1',
  ),
  198 => 
  array (
    'match' => '0C2A (0C3E)',
    'replace' => 'E01C \\1',
  ),
  199 => 
  array (
    'match' => '0C2B (0C3E)',
    'replace' => 'E01D \\1',
  ),
  200 => 
  array (
    'match' => '0C2D (0C3E)',
    'replace' => 'E01E \\1',
  ),
  201 => 
  array (
    'match' => '0C2E (0C3E)',
    'replace' => 'E01F \\1',
  ),
  202 => 
  array (
    'match' => '0C2F (0C3E)',
    'replace' => 'E020 \\1',
  ),
  203 => 
  array (
    'match' => '0C30 (0C3E)',
    'replace' => 'E021 \\1',
  ),
  204 => 
  array (
    'match' => '0C33 (0C3E)',
    'replace' => 'E022 \\1',
  ),
  205 => 
  array (
    'match' => '0C35 (0C3E)',
    'replace' => 'E023 \\1',
  ),
  206 => 
  array (
    'match' => '0C36 (0C3E)',
    'replace' => 'E024 \\1',
  ),
  207 => 
  array (
    'match' => '0C37 (0C3E)',
    'replace' => 'E025 \\1',
  ),
  208 => 
  array (
    'match' => '0C38 (0C3E)',
    'replace' => 'E026 \\1',
  ),
  209 => 
  array (
    'match' => '0C39 (0C3E)',
    'replace' => 'E027 \\1',
  ),
  210 => 
  array (
    'match' => 'E078 (0C3E)',
    'replace' => 'E07C \\1',
  ),
  211 => 
  array (
    'match' => 'E07A (0C3E)',
    'replace' => 'E07D \\1',
  ),
  212 => 
  array (
    'match' => 'E07B (0C3E)',
    'replace' => 'E07E \\1',
  ),
  213 => 
  array (
    'match' => '0C15 (0C4C)',
    'replace' => 'E00D \\1',
  ),
  214 => 
  array (
    'match' => '0C17 (0C4C)',
    'replace' => 'E00E \\1',
  ),
  215 => 
  array (
    'match' => '0C18 (0C4C)',
    'replace' => 'E00F \\1',
  ),
  216 => 
  array (
    'match' => '0C1A (0C4C)',
    'replace' => 'E010 \\1',
  ),
  217 => 
  array (
    'match' => '0C1B (0C4C)',
    'replace' => 'E011 \\1',
  ),
  218 => 
  array (
    'match' => '0C1C (0C4C)',
    'replace' => 'E012 \\1',
  ),
  219 => 
  array (
    'match' => '0C1D (0C4C)',
    'replace' => 'E013 \\1',
  ),
  220 => 
  array (
    'match' => '0C20 (0C4C)',
    'replace' => 'E014 \\1',
  ),
  221 => 
  array (
    'match' => '0C21 (0C4C)',
    'replace' => 'E015 \\1',
  ),
  222 => 
  array (
    'match' => '0C22 (0C4C)',
    'replace' => 'E016 \\1',
  ),
  223 => 
  array (
    'match' => '0C24 (0C4C)',
    'replace' => 'E017 \\1',
  ),
  224 => 
  array (
    'match' => '0C25 (0C4C)',
    'replace' => 'E018 \\1',
  ),
  225 => 
  array (
    'match' => '0C26 (0C4C)',
    'replace' => 'E019 \\1',
  ),
  226 => 
  array (
    'match' => '0C27 (0C4C)',
    'replace' => 'E01A \\1',
  ),
  227 => 
  array (
    'match' => '0C28 (0C4C)',
    'replace' => 'E01B \\1',
  ),
  228 => 
  array (
    'match' => '0C2A (0C4C)',
    'replace' => 'E01C \\1',
  ),
  229 => 
  array (
    'match' => '0C2B (0C4C)',
    'replace' => 'E01D \\1',
  ),
  230 => 
  array (
    'match' => '0C2D (0C4C)',
    'replace' => 'E01E \\1',
  ),
  231 => 
  array (
    'match' => '0C2E (0C4C)',
    'replace' => 'E01F \\1',
  ),
  232 => 
  array (
    'match' => '0C2F (0C4C)',
    'replace' => 'E020 \\1',
  ),
  233 => 
  array (
    'match' => '0C30 (0C4C)',
    'replace' => 'E021 \\1',
  ),
  234 => 
  array (
    'match' => '0C33 (0C4C)',
    'replace' => 'E022 \\1',
  ),
  235 => 
  array (
    'match' => '0C35 (0C4C)',
    'replace' => 'E023 \\1',
  ),
  236 => 
  array (
    'match' => '0C36 (0C4C)',
    'replace' => 'E024 \\1',
  ),
  237 => 
  array (
    'match' => '0C37 (0C4C)',
    'replace' => 'E025 \\1',
  ),
  238 => 
  array (
    'match' => '0C38 (0C4C)',
    'replace' => 'E026 \\1',
  ),
  239 => 
  array (
    'match' => '0C39 (0C4C)',
    'replace' => 'E027 \\1',
  ),
  240 => 
  array (
    'match' => 'E078 (0C4C)',
    'replace' => 'E07C \\1',
  ),
  241 => 
  array (
    'match' => 'E07A (0C4C)',
    'replace' => 'E07D \\1',
  ),
  242 => 
  array (
    'match' => 'E07B (0C4C)',
    'replace' => 'E07E \\1',
  ),
  243 => 
  array (
    'match' => '0C15 (0C4D)',
    'replace' => 'E00D \\1',
  ),
  244 => 
  array (
    'match' => '0C17 (0C4D)',
    'replace' => 'E00E \\1',
  ),
  245 => 
  array (
    'match' => '0C18 (0C4D)',
    'replace' => 'E00F \\1',
  ),
  246 => 
  array (
    'match' => '0C1A (0C4D)',
    'replace' => 'E010 \\1',
  ),
  247 => 
  array (
    'match' => '0C1B (0C4D)',
    'replace' => 'E011 \\1',
  ),
  248 => 
  array (
    'match' => '0C1C (0C4D)',
    'replace' => 'E012 \\1',
  ),
  249 => 
  array (
    'match' => '0C1D (0C4D)',
    'replace' => 'E013 \\1',
  ),
  250 => 
  array (
    'match' => '0C20 (0C4D)',
    'replace' => 'E014 \\1',
  ),
  251 => 
  array (
    'match' => '0C21 (0C4D)',
    'replace' => 'E015 \\1',
  ),
  252 => 
  array (
    'match' => '0C22 (0C4D)',
    'replace' => 'E016 \\1',
  ),
  253 => 
  array (
    'match' => '0C24 (0C4D)',
    'replace' => 'E017 \\1',
  ),
  254 => 
  array (
    'match' => '0C25 (0C4D)',
    'replace' => 'E018 \\1',
  ),
  255 => 
  array (
    'match' => '0C26 (0C4D)',
    'replace' => 'E019 \\1',
  ),
  256 => 
  array (
    'match' => '0C27 (0C4D)',
    'replace' => 'E01A \\1',
  ),
  257 => 
  array (
    'match' => '0C28 (0C4D)',
    'replace' => 'E01B \\1',
  ),
  258 => 
  array (
    'match' => '0C2A (0C4D)',
    'replace' => 'E01C \\1',
  ),
  259 => 
  array (
    'match' => '0C2B (0C4D)',
    'replace' => 'E01D \\1',
  ),
  260 => 
  array (
    'match' => '0C2D (0C4D)',
    'replace' => 'E01E \\1',
  ),
  261 => 
  array (
    'match' => '0C2E (0C4D)',
    'replace' => 'E01F \\1',
  ),
  262 => 
  array (
    'match' => '0C2F (0C4D)',
    'replace' => 'E020 \\1',
  ),
  263 => 
  array (
    'match' => '0C30 (0C4D)',
    'replace' => 'E021 \\1',
  ),
  264 => 
  array (
    'match' => '0C33 (0C4D)',
    'replace' => 'E022 \\1',
  ),
  265 => 
  array (
    'match' => '0C35 (0C4D)',
    'replace' => 'E023 \\1',
  ),
  266 => 
  array (
    'match' => '0C36 (0C4D)',
    'replace' => 'E024 \\1',
  ),
  267 => 
  array (
    'match' => '0C37 (0C4D)',
    'replace' => 'E025 \\1',
  ),
  268 => 
  array (
    'match' => '0C38 (0C4D)',
    'replace' => 'E026 \\1',
  ),
  269 => 
  array (
    'match' => '0C39 (0C4D)',
    'replace' => 'E027 \\1',
  ),
  270 => 
  array (
    'match' => 'E078 (0C4D)',
    'replace' => 'E07C \\1',
  ),
  271 => 
  array (
    'match' => 'E07A (0C4D)',
    'replace' => 'E07D \\1',
  ),
  272 => 
  array (
    'match' => 'E07B (0C4D)',
    'replace' => 'E07E \\1',
  ),
  273 => 
  array (
    'match' => 'E00F (0C3E)',
    'replace' => '0C18 \\1',
  ),
  274 => 
  array (
    'match' => 'E013 (0C3E)',
    'replace' => '0C1D \\1',
  ),
  275 => 
  array (
    'match' => 'E01F (0C3E)',
    'replace' => '0C2E \\1',
  ),
  276 => 
  array (
    'match' => 'E020 (0C3E)',
    'replace' => '0C2F \\1',
  ),
  277 => 
  array (
    'match' => 'E027 (0C3E)',
    'replace' => '0C39 \\1',
  ),
  278 => 
  array (
    'match' => 'E00F (0C4C)',
    'replace' => '0C18 \\1',
  ),
  279 => 
  array (
    'match' => 'E013 (0C4C)',
    'replace' => '0C1D \\1',
  ),
  280 => 
  array (
    'match' => 'E01F (0C4C)',
    'replace' => '0C2E \\1',
  ),
  281 => 
  array (
    'match' => 'E020 (0C4C)',
    'replace' => '0C2F \\1',
  ),
  282 => 
  array (
    'match' => 'E027 (0C4C)',
    'replace' => '0C39 \\1',
  ),
  283 => 
  array (
    'match' => '((E00F|0C1F|E01C|E01D|E025|E026|E027)) 0C46',
    'replace' => '\\1 E007',
  ),
  284 => 
  array (
    'match' => '((E00F|0C1F|E01C|E01D|E025|E026|E027)) 0C47',
    'replace' => '\\1 E008',
  ),
  285 => 
  array (
    'match' => '((0C16|0C18|0C1F|0C2C|E01E|0C2E|0C2F|0C32|0C1D)) 0C3E',
    'replace' => '\\1 E002',
  ),
  286 => 
  array (
    'match' => '((0C19|0C1C)) 0C3E',
    'replace' => '\\1 E003',
  ),
  287 => 
  array (
    'match' => '((0C1E|E022)) 0C3E',
    'replace' => '\\1 E004',
  ),
  288 => 
  array (
    'match' => '((E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E)) 0C56',
    'replace' => '\\1 E00C',
  ),
  289 => 
  array (
    'match' => '((E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071)) 0C56',
    'replace' => '\\1 E00C',
  ),
  290 => 
  array (
    'match' => '(0C33) 0C41',
    'replace' => '\\1 E005',
  ),
  291 => 
  array (
    'match' => '(0C33) 0C42',
    'replace' => '\\1 E0DE',
  ),
  292 => 
  array (
    'match' => '((0C2A|0C2B|0C35)) 0C41',
    'replace' => '\\1 E006',
  ),
  293 => 
  array (
    'match' => '((0C2A|0C2B|0C35)) 0C42',
    'replace' => '\\1 E0DF',
  ),
  294 => 
  array (
    'match' => 'E046 (E07C 0C46 0C56)',
    'replace' => 'E077 \\1',
  ),
  295 => 
  array (
    'match' => 'E046 ((0C15|E00D|0C16|0C17|E00E|0C18|E00F|0C19|0C1A|E010|0C1B|E011|0C1C|E012|0C1D|E013|0C1E|0C1F|0C20|E014|0C21|E015|0C22|E016|0C23|0C24|E017|0C25|E018|0C26|E019|0C27|E01A|0C28|E01B|0C2A|E01C|0C2B|E01D|0C2C|0C2D|E01E|0C2E|E01F|0C2F|E020|0C30|E021|0C32|0C33|E022|0C35|E023|0C36|E024|0C37|E025|0C38|E026|0C39|E027|E078|E07C|E079) 0C46 0C56)',
    'replace' => 'E069 \\1',
  ),
  296 => 
  array (
    'match' => '0C4D',
    'replace' => 'E0E0',
  ),
);
?>