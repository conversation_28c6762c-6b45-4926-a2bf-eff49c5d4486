// obs<PERSON><PERSON>, z<PERSON><PERSON><PERSON><PERSON> vzhled

.article {

  line-height: 1.2; // pro v<PERSON><PERSON><PERSON><PERSON> čitelnost

  // základn<PERSON> podoba nadpisů
  h1,
  h2,
  h3,
  h4 {
    color: @color_main_light;
    font-weight: 400;
  }

  h2 {
    font-size: 25px;
  }

  h3 {
    font-size: 21px;
  }

  h4 {
    font-size: 18px;
  }

  // základní odkaz
  a {

    color: @color_main;

    &:hover, &:active, &:focus {
      color: @color_main_dark;
    }

  }

  // základní seznam
  ul {
    padding-left: 1.5em;
  }

  // základní číslovaný seznam
  ol {
    padding-left: 2em;
  }

  // základní citace
  blockquote {
    margin: 1em 0;
    padding: 0.1em 1.5em;
    background-color: lighten(@color_main, 60%);
    border-left: 2px solid @color_main;
  }

}

  // velký oddělující nadpis
  .article__bigheader {

    margin: 0 -10px;
    padding: 25px 0;
    font-size: 30px;
    font-weight: 300;
    text-align: center;
    text-transform: uppercase;
    background-color: @color_gray_light;

    // zarovnání pozadí bloku
    @media (min-width: @mqsm) {
      margin: 0 0 0 -20px;
    }

  }

  // vložené obrázky
  .article__images {

    img {
      max-height: 200px;
      display: inline-block;
    }

  }

  // vložené přílohy
  .article__attachements {

    ul {
      list-style-type: none;
      padding-left: 2px;
    }

    li {
      margin-top: 5px;
      margin-bottom: 5px;
    }

    .icon {
      margin-top: -3px;
      margin-bottom: 3px;
      margin-right: 5px;
      font-size: 22px;
      text-decoration: none;
    }

  }

  // související zboží a příslušenství
  .article__related {

    h2 {
      font-size: 25px;
      color: @color_main_light;
      font-weight: 400;
    }

  }
