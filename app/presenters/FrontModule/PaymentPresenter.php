<?php

namespace FrontModule;
use dibi;
use Model\OrdersModel;
use Model\PaymentTransactionsModel;
use Nette;
use Pixidos\GPWebPay\Data\Operation;
use Pixidos\GPWebPay\Param\Amount;
use Pixidos\GPWebPay\Param\Currency;
use Pixidos\GPWebPay\Param\MerOrderNum;
use Pixidos\GPWebPay\Param\OrderNumber;
use Pixidos\GPWebPay\Param\ResponseUrl;
use Tracy\Debugger;
use Pixidos\GPWebPay\Enum\Currency as CurrencyEnum;

final class PaymentPresenter extends BasePresenter {

  public function actionOpenGate($id) {
    $ords = new OrdersModel();
    $ordId = substr($id, 0,-8);
    $key = substr($id, -8);

    $order = NULL;

    if (!empty($ordId) && strlen($key) === 8) {

      $order = $ords->load($ordId);

      if ($order) {
        //kontrola klíče
        $key2 = substr(md5($order->ordid . $order->orddatec->getTimestamp()), 0, 8);
        if ($key !== $key2) {
          $order = NULL;
        }
      } else {
        $order = NULL;
      }
    }

    if ($order === NULL) {
      $this->flashMessage("Neplatné volání stránky.", 'err');
      $this->redirect("Basket:default");
    }

    if ($order->ordpaystatus) {
      $this->flashMessage("Objednávka je již uhrazena", "err");
      $this->redirect("Payment:status", $this->getOrderStatusKey($order));
    }

    //otevřu bránu
    $pays = new PaymentTransactionsModel();
    $vals = [
      "payip" => $this->getHttpRequest()->getRemoteAddress(),
      "payordid" => $order->ordid
    ];
    $payId = $pays->insert($vals);

    $gate = new \GPWebPayPayment($this->neonParameters["gpwebpay"]);
    $ret = $gate->openGate($order, $payId, $this->link("//Payment:processResponse"));

    if ($ret === FALSE) {
      $this->flashMessage("Nastala chyba při otevření platební brány, prosím kontaktujte nás.", "err");

      $vals["payresulttext"] = "Chyba při otevření brány: " . $gate->lastError;
      $vals["paydateu"] = new \DateTime();
      $pays->update($payId, $vals);
      $this->redirect("Payment:status", $this->getOrderStatusKey($order));

    }

    $this->redirectUrl($ret);
  }

  public function actionProcessResponse() {
    $parameters = $this->getParameters();

    $gate = new \GPWebPayPayment($this->neonParameters["gpwebpay"]);
    $response = $gate->getResponse($parameters);

    $ord = NULL;

    if ($response !== FALSE) {
      $orderCode = $response->getMerOrderNumber();
      $ords = new OrdersModel();
      $ord = $ords->load($orderCode, 'code');
    }

    if ($ord) {
      if ((int)$ord->ordpaystatus === 1) {
        $this->flashMessage("Objednávka je již uhrazena");
        $this->redirect("Payment:status", $this->getOrderStatusKey($ord));
      }

      $transactionId = (int)$response->getOrderNumber();

      $transactions = new PaymentTransactionsModel();
      $vals = [];
      $vals["paysrcode"] = $response->getSrcode();
      $vals["payprcode"] = $response->getPrcode();
      $vals["payresulttext"] = ($response->hasError() ? "Err:" : "") . $response->getResultText();
      $vals["paydateu"] = new \DateTime();
      $transactions->update($transactionId, $vals);

      if ($gate->verifyPaymentResponse($response)) {
        if (!$response->hasError()) {
          //nenastala chyba verifikace OK - zapíšu, že bylo uhrazeno
          $vals = array(
            'ordid' => $ord->ordid,
            'ordstatus'=> 6,
            'ordpaystatus' => 1,
            'orddatepayed' => New \DateTime()
          );

          $this->changeOrderStatus($vals, FALSE, 'ONLINE platba kartou');

          $this->flashMessage("Platba kartou proběhla v pořádku");

          $this->redirect("Payment:status", $this->getOrderStatusKey($ord));
        }
      } else {
        Debugger::log("ProcessResponse error");
      }
    }

    $adInfo = "";

    if ($response->hasError()) {
      $adInfo = $response->getResultText();
    }

    $this->flashMessage("Platba kartou se nezdařila" . ($adInfo ? " ($adInfo)" : ""), "err");


    if ($ord) {
      $this->redirect("Payment:status", $this->getOrderStatusKey($ord));
    } else {
      $this->redirect("Basket:default");
    }

  }


  public function actionStatus($id) {

    $ords = new OrdersModel();
    $ordId = substr($id, 0,-8);
    $key = substr($id, -8);

    $order = NULL;

    if (!empty($ordId) && strlen($key) === 8) {

      $order = $ords->load($ordId);

      if ($order) {
        //kontrola klíče
        $key2 = substr(md5($order->ordid . $order->orddatec->getTimestamp()), 0, 8);
        if ($key !== $key2) {
          $order = NULL;
        }
      } else {
        $order = NULL;
      }
    }

    if ($order === NULL) {
      $this->flashMessage("Neplatné volání stránky.", 'err');
      $this->redirect("Basket:default");
    }

  }

}
