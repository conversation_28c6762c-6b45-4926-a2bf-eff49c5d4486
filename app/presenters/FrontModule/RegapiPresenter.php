<?php
namespace FrontModule;
use dibi;
use Model\UsersModel;
use Nette;

final class RegapiPresenter extends BasePresenter {

  /**
   * @throws Nette\Application\AbortException
   */
  public function renderDuo($hash) {

    $this->takeCareOfWipBinRequest(\RegApi::TYPE_DUO, $hash);

    $this->terminate();
  }

  /**
   * @throws Nette\Application\AbortException
   */
  public function renderWip($hash) {

    $this->takeCareOfWipBinRequest(\RegApi::TYPE_WIP, $hash);

    $this->terminate();
  }

  public function renderUni($hash) {

    $this->takeCareOfWipBinRequest(\RegApi::TYPE_UNI, $hash);

    $this->terminate();
  }

  /**
   * @param $type
   * @param $hash
   * @return false|string
   * @throws Nette\Application\AbortException|\Dibi\Exception
   */
  private function takeCareOfWipBinRequest($type, $hash) {
    $response = [
        'status' => 'ERR',
        'code' => 500,
        'message' => 'Unknown error'
      ];

    $api = new \RegApi($this->neonParameters["regApi"]);

    $data = $api->getDataByHash($type, $hash);

    if ($data === FALSE) {
      $errors = $api->getErrors();
      $errorMess = 'Unknown error';
      if (!empty($errors)) {
        $errorMess = implode(", ", $errors);

      }
      $response = [
        'status' => 'ERR',
        'code' => 500,
        'message' => $errorMess
      ];

      $this->setResponse($response);

    } else if (empty($data)) {
      $response = [
        'status' => 'ERR',
        'code' => 404,
        'message' => 'Not found',
      ];

      $this->setResponse($response);
    }

    $email = $data->email;
    $usrs = new UsersModel();
    $usr = $usrs->load($email, 'mail');

    $mailTemplate = $this->createTemplate();
    $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailUserBin.latte');

    if ($usr) {
      $response = [
        'status' => 'OK',
        'code' => 200,
        'message' => 'Account exists'
      ];

      $mailTemplate->type = "exists";
      $mailTemplate->user = $usr;

      if ($type === \RegApi::TYPE_WIP && $usr->usrprccat !== 'a') {
        $usrs->update($usr->usrid, array('usrprccat' => 'a'));
      }

      $this->mailSend($usr->usrmail, "Registrace", $mailTemplate);

    } else {
      $mailTemplate->type = "new";
      $vals = array(
        'usrmail' => $email,
        'usrprccat' => 'a',
        'usriname' => $data->jmeno,
        'usrilname' => $data->prijmeni,
        'usristreet' => $data->adresa1 . " " . $data->adresa2,
        'usricity' => $data->mesto,
        'usrmaillist' => (int)$data->novinky,
        'usripostcode' => $data->psc,
        'usrmailvcode' => NULL,
        'usrmailverified' => 1
      );

      $passw = Nette\Utils\Random::generate(6);
      $vals["usrpassw"] = $this->passwordHash($passw);

      //print_r($vals);
      $usrid = $usrs->insert($vals);

      if ($usrid > 0) {
        $response = [
          'status' => 'OK',
          'code' => 201,
          'message' => 'Account created'
        ];
      }

      //zapíšu souhlas GDPR
      //zaloguji, regiistraci
      $usrs->logEvent($usrid, UsersModel::EVENT_REGISTER);
      //zaloguji souhlas s ucováním údajů
      $usrs->logEvent($usrid, UsersModel::EVENT_GDPR);
      if ($data->novinky) {
        //zaloguji přihlášení do maillingu
        $usrs->logEvent($usrid, UsersModel::EVENT_MAILLIST_ADD);
      }

      $userRow = $usrs->load($usrid);

      //ulozim si do sablony heslo nez se zaheshuje
      $mailTemplate->usrpassw = $passw;

      $mailTemplate->user = $userRow;

      //$this->mailSend($userRow->usrmail, "Registrace", $mailTemplate);
      $mailTemplate->usrpassw = "";
      //$this->mailSend($this->config["SERVER_MAIL"], "Nová registrace BIN", "Pravě se registroval nový uživatel: ".$userRow->usriname." ".$userRow->usrilname.' <a href="mailto:'.$userRow->usrmail.'">'.$userRow->usrmail.'</a>');
    }

    $this->setResponse($response);
  }

  private function setResponse($response) {
    $this->getHttpResponse()->setHeader('Content-Type', 'application/json; charset=utf-8');
    echo json_encode($response);

    $this->terminate();
  }

}
