# nastaveni ftp serveru
variables:
  # DEV server
  FTP_DEV_URL: "sftp://*************:222/"
  FTP_DEV_USERNAME: "widexdev"
  FTP_DEV_PASSWORD: "jvTJaNr4fBM-pJ"

  FTP_URL: "sftp://*************:222/"
  FTP_USERNAME: "widexdeploy"
  FTP_PASSWORD: "ZB*f*dUky7Yof@tj"

# definice akci

stages:
  - deploy
  - deploy_dev

# natazeni docker image s git-ftp
image: danielstrelec/git-ftp

# deploy
deploy:
  # nazev akce
  stage: deploy
  # spustime pozadovane skripty
  script:
    - git --version
    - git ftp --version
    # pouze pro vstupni deploy
    #- git ftp init --user $FTP_USERNAME --passwd $FTP_PASSWORD $FTP_URL
    # deploy posledniho commitu
    - echo $FTP_USERNAME
    - echo $FTP_URL
    - git ftp push --insecure --user $FTP_USERNAME --passwd $FTP_PASSWORD $FTP_URL
    - curl -O https://shop.widex.cz/cc.php?k=lZwJIL
  # nastaveni spusteni - pouze na master vetvi
  only:
    - master
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual

# deploy - DEV
deploy_dev:
  # nazev akce
  stage: deploy_dev
  # spustime pozadovane skripty
  script:
    - git --version
    - git ftp --version
    # pouze pro vstupni deploy
    #- git ftp init --user $FTP_DEV_USERNAME --passwd $FTP_DEV_PASSWORD $FTP_DEV_URL
    # deploy posledniho commitu
    - git ftp push --insecure --user $FTP_DEV_USERNAME --passwd $FTP_DEV_PASSWORD $FTP_DEV_URL
    - curl -O -k https://test:<EMAIL>/cc.php?k=lZwJIL
  # nastaveni spusteni - pouze na dev
  only:
    - master
  # manualni spusteni, kdyz je zakomentovano spousti se automaticky
  #when: manual
