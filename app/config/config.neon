php: # PHP configuration
  date.timezone: Europe/Prague
  zlib.output_compression: yes
nette:
  application:
    catchExceptions: TRUE
    errorPresenter: Front:Error
  session:
    autoStart: true
  security:
    debugger: true
    roles:
      guest:
      editor:
      expedice:
      admin:
    resources:
      Admin:Admin:
      Admin:User:
      Admin:Catalog:
      Admin:Product:
      Admin:ProductCA:
      Admin:ProparamDef:
      Admin:Export:
      Admin:Import:
      Admin:Order:
      Admin:Coupon:
      Admin:DeliveryMode:
      Admin:Manufacturer:
      Admin:Page:
      Admin:Article:
      Admin:Menu:
      Admin:New:
      Admin:MenuIndex:
      Admin:Config:
      Admin:Dictionary:
      Admin:Discount:
      Admin:Comment:
      Admin:Mailing:
      Admin:WidexImport:
  debugger:
      email: <EMAIL>
      strictMode: TRUE
parameters:
  app:
    domain: shop.widex.cz
    basketOrderOnOnePage: FALSE
  adminModule:
    menuBackgroundColor: "#009cdd"
  labels:
    #nazvy cenovych hladin
    com: Cena běž<PERSON>
    a: Cena A
    b: Cena B
    c: Cena C
    d: Cena D
    #nazvy priznaku u zbozi (akce, novinka, atd.)
    protypid: Akce
    protypid2: Novinka
    protypid3: Tip
    protypid4: Nejprodávánější
    protypid5: Připravujeme
  currency:
    1:
      id: 1
      key: CZK
      code: Kč
      decimals: 0
    #2:
    #  id: 2
    #  code: €
    #  key: EUR
    #  decimals: 1

  hosts:
    localhost:
      curId: 1
    127.0.0.1:
      curId: 1
    shop.widex.cz:
      curId: 1

  heureka:
    IdOverenoZakazniky:
    IdOverenoZakaznikyCertifikat:
    KeyMereniKonverzi:

  google:
    ua: UA-*********-1 #GoogleAnalytics UA
    conversionId: #Google Retargeting

  seznam:
    conversionId: #seznam retargeting

  facebook:
    pixelCodeId:

  toplist:
    id:

  sms:
    login:
    passw:

  fio:
    account:
    token:

  regApi:
    username: 'WIDapi/2022'
    password: 'BohusP9'

  mssql:
    driver: 'mssql'
    host: '***********:1433'
    username: 'ew'
    password: 'ew'
    database: 'Widex2006'
    charset: 'WINDOWS-1250'

  csob:
    merchantId: M1MIPS1454
    password: NULL
    shopName: shop.widex.cz
    eetPremiseId: # 41
    eetCashRegisterId: EshopOnline
    eetVerificationMode: FALSE
    isProduction: TRUE

  gpwebpay:
    privateKeyFile: gpwebpay-pvk.key #test: gpwebpay-test-pvk.key
    privateKeyPassphrase: 'GPpm24**' #test: fEeh7NdoPFFT2WUTRiFW
    publicKeyFile: gpe.signing_prod.pem # gpe.signing_test.pem
    gateUrl: https://3dsecure.gpwebpay.com/pgw/order.do #test: https://test.3dsecure.gpwebpay.com/pgw/order.do
    merchantNumber: ********
    #responseUrl: <on this url client get redirect back after payment will done> #optional you can set in Control
    depositFlag: 1 #optional you can set in Operation. Can set 1 or 0. Default is 1

  payU1:
    posId:
    key1:
    key2:
    posAuthKey:

  payU2:
    posId:
    key1:
    key2:
    posAuthKey:

  onlinePayTypes:
    c: "Platební kartou - online"
    rf: "eKonto (Raiffeisenbank)"
    kb: "MojePlatba (Komerční banka)"
    cs: "PLATBA 24 (Česká spořitelna)"
    pf: "Fio banka"
    pg: "GE Money Bank"
    mp: "mPeníze (mBank)"
    uc: "UniCredit bank"
    pv: "Sberbank"
    cb: "ČSOB"
    era: "Era banka"
    mo: "Mobito (peníze v mobilu)"
    psc: "PaySec"

extensions:
  mobileDetect: IPub\MobileDetect\DI\MobileDetectExtension
  dibi: Dibi\Bridges\Nette\DibiExtension22
  recaptcha: Contributte\ReCaptcha\DI\ReCaptchaExtension
  visualPaginator: IPub\VisualPaginator\DI\VisualPaginatorExtension

recaptcha:
  secretKey: '6LerKFAUAAAAAC_1uC3fnyP0Qi-EQeAcgR8owKx2'
  siteKey: '6LerKFAUAAAAAHtwYnFpcsHTllACAN-urxBfKN23'

dibi:
  driver: mysqli
  host: 127.0.0.1
  database: ekramek
  username: root
  password: root

services:
  cache.storage: Nette\Caching\Storages\FileStorage(%tempDir%)
  - Classes\NeonParametersRepository(@container::getParameters())

  myTemplateFilters:
    factory: \TemplateFilters(%wwwDir%, @application)

  connection.panel:
    class: Dibi\Bridges\Tracy\Panel

  authenticator:
    class: MyAuthenticator

  nette.authorizator:
    setup:
     #guest
      - @self::allow( guest, 'Admin:Admin', ['login', 'logout', 'default'])
      - @self::allow( guest, 'Admin:WidexImport', ['import'])
     #admin - moduly
      - @self::allow( admin, 'Admin:Admin', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:User', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Catalog', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Product', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:ProparamDef', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Import', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Order', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:DeliveryMode', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Manufacturer', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Page', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Article', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Menu', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:New', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:MenuIndex', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Config', Nette\Security\Permission::ALL)
     #- @self::allow( admin, 'Admin:Discount', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Coupon', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Mailing', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:WidexImport', Nette\Security\Permission::ALL)
     #admin - akce
      - @self::allow( admin, 'Admin:Admin', ['changeForeign', 'orderSetAuthor', 'multiaccounts'])
     #CustomAction práva Název modulu+CA
      - @self::allow( admin, 'Admin:ProductCA', ['gifts'])
     #- @self::allow( admin, 'Admin:ProductCA', ['ActionPrices'])
     #editor - moduly
      - @self::allow( editor, 'Admin:Admin', ['login', 'logout', 'default', 'edit'])
      - @self::allow( editor, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( editor, 'Admin:Article', Nette\Security\Permission::ALL)
     #expedice - moduly
      - @self::allow( expedice, 'Admin:Admin', ['login', 'logout', 'default', 'edit'])
      - @self::allow( expedice, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:Article', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:New', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:Order', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:User', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:DeliveryMode', Nette\Security\Permission::ALL)
