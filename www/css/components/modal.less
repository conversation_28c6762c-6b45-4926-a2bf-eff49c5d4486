// modal okno
  // modal se spouští kliknutím na blok, který má class="modal--show"
  // ID okna musí být uvedeno v atributu REL rel="modal-name"
  // konkrétní okno musí mít ID shodné s REL atributem
  // pokud půjde o odkaz, je doporučeno dát ID do hrefu jako kotvu href="#modal-name"

.modal {

  // v základním stavu skryté
  display: none;

  // základní pozice na menším rozlišení
  position: absolute;
  top: 0.5%;
  left: 1%;
  width: 100%;

  @media (min-width: @mqxs) {

    // pozice přes celou obrazovku
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;

    // zabránění přetečení
    overflow: hidden;

    // pozadí - zešednutí
    background-color: rgba(0,0,0,0.75);

  }

  // přeražení z-indexu
  z-index: 1000;

}

  // reset modalu při nízké výšce okna
  // přidáváno pouze JavaScriptem, vše přeraženo
  .modal--reset {

    position: absolute !important;
    overflow: visible !important;
    background-color: transparent !important;

    // reset pozice a centrování
    .modal__body {
      top: 0 !important;
      transform: none !important;
    }

  }

  // tělo
  .modal__body {

    // absolutní pozicování
    position: absolute;

    // stín boxu pro lepší přehnednost na nižších rozlišeních
    box-shadow: 0 0 13px 2px rgba(0,0,0,.5);

    // doprostřed obrazovky - pouze na vyšším rozlišení
    @media (min-width: @mqxs) {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    // šířka modal okna
    width: 98%;
    @media (min-width: @mq_modal) {
      width: 680px;
    }

    background: @color_white;

  }

  // hlavička
  .modal__head {

    padding: 0.5em;

    @media (min-width: @mq_modal) {
      padding: 1em 1.25em;
    }

    color: @color_white;
    background-color: @color_main;

  }

  // zavírací tlačítko
  .modal__close {

    float: right;
    display: inline-block;
    font-size: 22px;
    cursor: pointer;

    &:hover, &:active, &:focus {
      opacity: 0.8;
    }

  }

  // nadpis
  .modal__header {
    margin: 0;
    font-size: 20px;
  }

  // obsah
  .modal__content {

    padding: 0.5em;

    @media (min-width: @mq_modal) {
      padding: 1em 1.25em;
    }

  }

  // patička
  .modal__footer {

    padding: 0.5em;

    @media (min-width: @mq_modal) {
      padding: 1em 1.25em;
    }

  }
