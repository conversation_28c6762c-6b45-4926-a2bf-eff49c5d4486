<?php
class RegApi {

  const API_URL = 'https://portal.widex.cz/regapibc/api/';

  const TYPE_WIP = "WIP";
  const TYPE_DUO = "DUO";
  const TYPE_UNI = "UNI";

  /* @string autorizační token */
  private $token;
  private $config;
  private $errors;

  public function __construct($config) {
    $this->config = $config;
    $token = $this->getToken();

    if (!$token) {
      throw new \Exception("Nelze autorizovat.", 403);
    }

    $this->token = $token;
  }

  private function getToken(): bool|string {

    $data = [
      'username' => $this->config["username"],
      'password' => $this->config["password"],
    ];

    $request = $this->curlExec('Auth/Authorize', $data);

    if (!$request) {
      return false;
    }

    return (string)$request[0]->access_token;
  }

  public function getDataByHash($type, $hash) {
    $data = [
      "access_token" => $this->token,
      "hash" => $hash
    ];

    $path = 'Registrace/' . $type . '/Hash';

    return $this->curlExec($path, $data, 'GET');
  }

  private function curlExec($path, $data, $request='POST') {
    $ch = curl_init();
    // set URL and other appropriate options

    if (isset($data["access_token"])) {
      curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json' , "Authorization: Bearer " . $data["access_token"]));
      unset($data["access_token"]);
    }

    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $request);
    if ($request === 'POST') {
      curl_setopt($ch, CURLOPT_URL, self::API_URL . $path);
      curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    } else {
      curl_setopt($ch, CURLOPT_URL, self::API_URL . $path . "?" . http_build_query($data));
    }
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    $ret = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);
    $arr = json_decode($ret);

    if (!empty($err)) {
      $this->setErrorMsg("cUrl error: $err");
      return false;
    }
    return($arr);
  }

  private function setErrorMsg($text) {
    $this->errors[] = $text;
  }

  public function getErrors() {
    return $this->errors;
  }

}
