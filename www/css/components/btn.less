// tla<PERSON><PERSON>tko

.btn, button {

  margin-bottom: 4px;
  padding: 10px 12px;

  color: @color_white;
  font: inherit; // pro sladění vzhledu buttonu a odkazu
  font-size: 16px;
  text-decoration: none;
  text-transform: uppercase;

  background-color: @color_main_light;
  border: 1px solid transparent;
  border-radius: @radius;

  cursor: pointer;

  &:hover, &:active, &:focus {
    background-color: @color_main_dark;
  }

  // ikona v tlačítku
  .icon {
    vertical-align: bottom;
    margin: 0 3px;
    font-size: 16px;
  }

  // na malém rozlišení bude vždy malý
  @media (max-width: @mqxxs) {
    padding: 8px 10px;
    font-size: 14px;
    .icon { font-size: 14px; }
  }

}

  // úprava odkazu
  a.btn {

    display: inline-block;
    color: @color_white;

    &:hover, &:active, &:focus {
      color: @color_white;
    }

  }

  // kompaktní tla<PERSON>tko
  .btn--small {

    padding: 8px 25px;
    font-size: 14px;

    // úprava velikosti ikony
    .icon { font-size: 15px; }

  }

  // výrazné tlačítko
  .btn--big {

    padding: 10px 18px;
    font-size: 20px;

    // úprava velikosti ikony
    .icon { font-size: 20px; }

    // na malém rozlišení bude vždy malý
    @media (max-width: @mqxxs) {
      padding: 8px 10px;
      font-size: 14px;
      .icon { font-size: 14px; }
    }

  }

  // potvrzující tlačítko výrazné
  .btn--success {

    background-color: @color_success;

    &:hover, &:active, &:focus {
      background-color: darken(@color_success, 10%);
    }

  }

  // informativní tlačítko
  .btn--info {

    background-color: @color_info;

      &:hover, &:active, &:focus {
        background-color: darken(@color_info, 10%);
      }

  }

  // výstražné tlačítko
  .btn--danger {

    background-color: @color_danger;

    &:hover, &:active, &:focus {
      background-color: darken(@color_danger, 10%);
    }

  }

  // tlačítko koupit
  .btn--buy {

    background-color: @color_buy;

    &:hover, &:active, &:focus {
      background-color: darken(@color_buy, 10%);
    }

  }

  // tlačítko koupit
  .btn--back {

    background-color: @color_gray_middle;

    &:hover, &:active, &:focus {
      background-color: darken(@color_gray_middle, 10%);
    }

  }
