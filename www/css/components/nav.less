// navigace

.nav {

  clear: both;

  position: relative;
  padding-top: 10px;
  color: @color_gray_dark;
  font-size: 14px;
  text-transform: uppercase;

  @media (min-width: @mqsm) {
    margin: 0 0 0 185px;
  }

  // na mobilní verzi bude místo loga pouze ikona
  @media (max-width: @mqsm) {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 40px;
    height: 40px;
    overflow: hidden;
    background: url('../../img/menu-icon.svg') center center no-repeat;
  }

  ul {

    list-style-type: none;
    margin: 0;
    padding: 0;

    // zobrazíme až na vyšším rozlišení
    display: none;
    @media (min-width: @mqsm) {
      display: block;
    }

  }

  li {
    display: inline-block;
  }

  a {

    display: block;
    padding: 0 30px 0 0;
    color: @color_gray_dark;
    text-decoration: none;

    &:hover, &:active, &:focus, &.is-active {
      color: @color_main_light;
    }

  }

}
