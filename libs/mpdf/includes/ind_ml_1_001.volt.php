<?php
	$volt = array (
  0 => 
  array (
    'match' => '0D4D 200D',
    'replace' => '007E',
  ),
  1 => 
  array (
    'match' => '0D4D 200C',
    'replace' => '2018',
  ),
  2 => 
  array (
    'match' => '200D 0D4D',
    'replace' => '2019',
  ),
  3 => 
  array (
    'match' => '0D15 0D4D 0D37',
    'replace' => 'E010',
  ),
  4 => 
  array (
    'match' => '0D1C 0D4D 0D1E',
    'replace' => 'E011',
  ),
  5 => 
  array (
    'match' => '0D1A 0D4D 0D1A',
    'replace' => 'E03E',
  ),
  6 => 
  array (
    'match' => '0D2C 0D4D 0D2C',
    'replace' => 'E04B',
  ),
  7 => 
  array (
    'match' => '0D24 0D4D 0D24',
    'replace' => 'E023',
  ),
  8 => 
  array (
    'match' => '0D28 0D4D 0D28',
    'replace' => 'E02A',
  ),
  9 => 
  array (
    'match' => '0D23 0D4D 0D23',
    'replace' => 'E043',
  ),
  10 => 
  array (
    'match' => '0D2E 0D4D 0D2E',
    'replace' => 'E030',
  ),
  11 => 
  array (
    'match' => '0D33 0D4D 0D33',
    'replace' => 'E036',
  ),
  12 => 
  array (
    'match' => '0D32 0D4D 0D32',
    'replace' => 'E05D',
  ),
  13 => 
  array (
    'match' => '0D26 0D4D 0D26',
    'replace' => 'E015',
  ),
  14 => 
  array (
    'match' => '0D1F 0D4D 0D1F',
    'replace' => 'E014',
  ),
  15 => 
  array (
    'match' => '0D1C 0D4D 0D1C',
    'replace' => 'E01B',
  ),
  16 => 
  array (
    'match' => '0D2A 0D4D 0D2A',
    'replace' => 'E046',
  ),
  17 => 
  array (
    'match' => '0D17 0D4D 0D17',
    'replace' => 'E03B',
  ),
  18 => 
  array (
    'match' => '0D15 0D4D 0D15',
    'replace' => 'E012',
  ),
  19 => 
  array (
    'match' => '0D31 0D4D 0D31',
    'replace' => 'E05C',
  ),
  20 => 
  array (
    'match' => '0D19 0D4D 0D15',
    'replace' => 'E013',
  ),
  21 => 
  array (
    'match' => '0D19 0D4D 0D19',
    'replace' => 'E016',
  ),
  22 => 
  array (
    'match' => '0D15 0D4D 0D24',
    'replace' => 'E017',
  ),
  23 => 
  array (
    'match' => '0D17 0D4D 0D2E',
    'replace' => 'E018',
  ),
  24 => 
  array (
    'match' => '0D17 0D4D E029',
    'replace' => 'E019',
  ),
  25 => 
  array (
    'match' => '0D17 0D4D 0D28',
    'replace' => 'E01A',
  ),
  26 => 
  array (
    'match' => '0D1E 0D4D 0D1C',
    'replace' => 'E01C',
  ),
  27 => 
  array (
    'match' => '0D1E 0D4D 0D1E',
    'replace' => 'E01D',
  ),
  28 => 
  array (
    'match' => '0D1E 0D4D 0D1A',
    'replace' => 'E01E',
  ),
  29 => 
  array (
    'match' => '0D1E 0D4D 0D1B',
    'replace' => 'E01F',
  ),
  30 => 
  array (
    'match' => '0D23 0D4D 0D2E',
    'replace' => 'E020',
  ),
  31 => 
  array (
    'match' => '0D23 0D4D 0D21',
    'replace' => 'E021',
  ),
  32 => 
  array (
    'match' => '0D23 0D4D 0D1F',
    'replace' => 'E022',
  ),
  33 => 
  array (
    'match' => '0D24 0D4D 0D38',
    'replace' => 'E024',
  ),
  34 => 
  array (
    'match' => '0D24 0D4D 0D25',
    'replace' => 'E025',
  ),
  35 => 
  array (
    'match' => '0D24 0D4D 0D2E',
    'replace' => 'E026',
  ),
  36 => 
  array (
    'match' => '0D24 0D4D 0D2D',
    'replace' => 'E027',
  ),
  37 => 
  array (
    'match' => '0D24 0D4D 0D28',
    'replace' => 'E028',
  ),
  38 => 
  array (
    'match' => '0D26 0D4D 0D27',
    'replace' => 'E029',
  ),
  39 => 
  array (
    'match' => '0D28 0D4D 0D26',
    'replace' => 'E02B',
  ),
  40 => 
  array (
    'match' => '0D28 0D4D 0D24',
    'replace' => 'E02C',
  ),
  41 => 
  array (
    'match' => '0D28 0D4D 0D27',
    'replace' => 'E02D',
  ),
  42 => 
  array (
    'match' => '0D28 0D4D 0D2A',
    'replace' => 'E031',
  ),
  43 => 
  array (
    'match' => '0D28 0D4D 0D2E',
    'replace' => 'E02E',
  ),
  44 => 
  array (
    'match' => '0D28 0D4D 0D25',
    'replace' => 'E02F',
  ),
  45 => 
  array (
    'match' => '0D2E 0D4D 0D2A',
    'replace' => 'E031',
  ),
  46 => 
  array (
    'match' => '0D36 0D4D 0D1A',
    'replace' => 'E032',
  ),
  47 => 
  array (
    'match' => '0D38 0D4D 0D25',
    'replace' => 'E033',
  ),
  48 => 
  array (
    'match' => '0D39 0D4D 0D28',
    'replace' => 'E034',
  ),
  49 => 
  array (
    'match' => '0D39 0D4D 0D2E',
    'replace' => 'E035',
  ),
  50 => 
  array (
    'match' => '0D15 0D4D 0D32',
    'replace' => 'E037',
  ),
  51 => 
  array (
    'match' => '0D15 0D4D 0D33',
    'replace' => 'E037',
  ),
  52 => 
  array (
    'match' => '0D15 0D4D 0D38',
    'replace' => 'E038',
  ),
  53 => 
  array (
    'match' => '0D15 0D4D 0D1F',
    'replace' => 'E039',
  ),
  54 => 
  array (
    'match' => '0D18 0D4D 0D28',
    'replace' => 'E03A',
  ),
  55 => 
  array (
    'match' => '0D17 0D4D 0D32',
    'replace' => 'E03C',
  ),
  56 => 
  array (
    'match' => '0D17 0D4D 0D33',
    'replace' => 'E03C',
  ),
  57 => 
  array (
    'match' => '0D1A 0D4D 0D1B',
    'replace' => 'E03F',
  ),
  58 => 
  array (
    'match' => '0D21 0D4D 0D21',
    'replace' => 'E040',
  ),
  59 => 
  array (
    'match' => '0D21 0D4D 0D22',
    'replace' => 'E041',
  ),
  60 => 
  array (
    'match' => '0D21 0D4D 0D1C',
    'replace' => 'E042',
  ),
  61 => 
  array (
    'match' => '0D23 0D4D 0D20',
    'replace' => 'E044',
  ),
  62 => 
  array (
    'match' => '0D24 0D4D 0D32',
    'replace' => 'E045',
  ),
  63 => 
  array (
    'match' => '0D2A 0D4D 0D28',
    'replace' => 'E047',
  ),
  64 => 
  array (
    'match' => '0D2A 0D4D 0D32',
    'replace' => 'E048',
  ),
  65 => 
  array (
    'match' => '0D2A 0D4D 0D33',
    'replace' => 'E048',
  ),
  66 => 
  array (
    'match' => '0D2A 0D4D 0D38',
    'replace' => 'E049',
  ),
  67 => 
  array (
    'match' => '0D2A 0D4D 0D24',
    'replace' => 'E04A',
  ),
  68 => 
  array (
    'match' => '0D2C 0D4D 0D32',
    'replace' => 'E04C',
  ),
  69 => 
  array (
    'match' => '0D2C 0D4D 0D33',
    'replace' => 'E04C',
  ),
  70 => 
  array (
    'match' => '0D2C 0D4D 0D26',
    'replace' => 'E04D',
  ),
  71 => 
  array (
    'match' => '0D2C 0D4D 0D27',
    'replace' => 'E04E',
  ),
  72 => 
  array (
    'match' => '0D2C 0D4D 0D15',
    'replace' => 'E04F',
  ),
  73 => 
  array (
    'match' => '0D2B 0D4D 0D32',
    'replace' => 'E050',
  ),
  74 => 
  array (
    'match' => '0D2B 0D4D 0D33',
    'replace' => 'E050',
  ),
  75 => 
  array (
    'match' => '0D2B 0D4D 0D15',
    'replace' => 'E051',
  ),
  76 => 
  array (
    'match' => '0D2B 0D4D 0D24',
    'replace' => 'E052',
  ),
  77 => 
  array (
    'match' => '0D2E 0D4D 0D32',
    'replace' => 'E053',
  ),
  78 => 
  array (
    'match' => '0D2E 0D4D 0D33',
    'replace' => 'E053',
  ),
  79 => 
  array (
    'match' => '0D2E 0D4D 0D28',
    'replace' => 'E054',
  ),
  80 => 
  array (
    'match' => '0D2F 0D4D 0D2F',
    'replace' => 'E055',
  ),
  81 => 
  array (
    'match' => '0D2F 0D4D 0D2E',
    'replace' => 'E056',
  ),
  82 => 
  array (
    'match' => '0D2F 0D4D 0D2A',
    'replace' => 'E057',
  ),
  83 => 
  array (
    'match' => '0D2F 0D4D 0D15',
    'replace' => 'E058',
  ),
  84 => 
  array (
    'match' => '0D2F 0D4D 0D24',
    'replace' => 'E05A',
  ),
  85 => 
  array (
    'match' => '0D32 0D4D 0D32',
    'replace' => 'E05D',
  ),
  86 => 
  array (
    'match' => '0D32 0D4D 0D2A',
    'replace' => 'E05E',
  ),
  87 => 
  array (
    'match' => '0D32 0D4D 0D15',
    'replace' => 'E05F',
  ),
  88 => 
  array (
    'match' => '0D32 0D4D 0D2E',
    'replace' => 'E060',
  ),
  89 => 
  array (
    'match' => '0D35 0D4D 0D35',
    'replace' => 'E061',
  ),
  90 => 
  array (
    'match' => '0D35 0D4D 0D32',
    'replace' => 'E062',
  ),
  91 => 
  array (
    'match' => '0D35 0D4D 0D33',
    'replace' => 'E062',
  ),
  92 => 
  array (
    'match' => '0D36 0D4D 0D36',
    'replace' => 'E063',
  ),
  93 => 
  array (
    'match' => '0D36 0D4D 0D32',
    'replace' => 'E064',
  ),
  94 => 
  array (
    'match' => '0D36 0D4D 0D33',
    'replace' => 'E064',
  ),
  95 => 
  array (
    'match' => '0D36 0D4D 0D28',
    'replace' => 'E065',
  ),
  96 => 
  array (
    'match' => '0D36 0D4D 0D2E',
    'replace' => 'E066',
  ),
  97 => 
  array (
    'match' => '0D37 0D4D 0D2A',
    'replace' => 'E067',
  ),
  98 => 
  array (
    'match' => '0D37 0D4D 0D15',
    'replace' => 'E068',
  ),
  99 => 
  array (
    'match' => '0D37 0D4D 0D23',
    'replace' => 'E069',
  ),
  100 => 
  array (
    'match' => '0D37 0D4D 0D1F',
    'replace' => 'E06A',
  ),
  101 => 
  array (
    'match' => '0D37 0D4D 0D2E',
    'replace' => 'E06B',
  ),
  102 => 
  array (
    'match' => '0D37 0D4D E05C',
    'replace' => 'E06C',
  ),
  103 => 
  array (
    'match' => '0D37 0D4D 0D2B',
    'replace' => 'E06D',
  ),
  104 => 
  array (
    'match' => '0D37 0D4D 0D20',
    'replace' => 'E06E',
  ),
  105 => 
  array (
    'match' => '0D37 0D4D 0D24',
    'replace' => 'E06F',
  ),
  106 => 
  array (
    'match' => '0D38 0D4D 0D38',
    'replace' => 'E070',
  ),
  107 => 
  array (
    'match' => '0D38 0D4D 0D32',
    'replace' => 'E071',
  ),
  108 => 
  array (
    'match' => '0D38 0D4D 0D33',
    'replace' => 'E071',
  ),
  109 => 
  array (
    'match' => '0D38 0D4D E05C',
    'replace' => 'E072',
  ),
  110 => 
  array (
    'match' => '0D38 0D4D 0D2A',
    'replace' => 'E073',
  ),
  111 => 
  array (
    'match' => '0D38 0D4D 0D24',
    'replace' => 'E074',
  ),
  112 => 
  array (
    'match' => '0D38 0D4D 0D15',
    'replace' => 'E075',
  ),
  113 => 
  array (
    'match' => '0D38 0D4D 0D28',
    'replace' => 'E076',
  ),
  114 => 
  array (
    'match' => '0D38 0D4D 0D2E',
    'replace' => 'E077',
  ),
  115 => 
  array (
    'match' => '0D39 0D4D 0D32',
    'replace' => 'E078',
  ),
  116 => 
  array (
    'match' => '0D39 0D4D 0D33',
    'replace' => 'E078',
  ),
  117 => 
  array (
    'match' => '0D34 0D4D 0D24',
    'replace' => 'E079',
  ),
  118 => 
  array (
    'match' => '0D34 0D4D 0D15',
    'replace' => 'E07A',
  ),
  119 => 
  array (
    'match' => '0D34 0D4D 0D2E',
    'replace' => 'E07C',
  ),
  120 => 
  array (
    'match' => 'E006 0D4D 0D31',
    'replace' => 'E07D',
  ),
  121 => 
  array (
    'match' => '0D28 0D4D 0D30',
    'replace' => 'E07D',
  ),
  122 => 
  array (
    'match' => '0D28 0D4D 0D31',
    'replace' => 'E07D',
  ),
  123 => 
  array (
    'match' => '0D34 0D4D E023',
    'replace' => 'E07B',
  ),
  124 => 
  array (
    'match' => '0D2F 0D4D E023',
    'replace' => 'E05B',
  ),
  125 => 
  array (
    'match' => '0D2F 0D4D E012',
    'replace' => 'E059',
  ),
  126 => 
  array (
    'match' => 'E010 0D4D 0D2E',
    'replace' => 'E03D',
  ),
  127 => 
  array (
    'match' => '2019 0D31',
    'replace' => 'E00E',
  ),
  128 => 
  array (
    'match' => '2019 0D31',
    'replace' => 'E00E',
  ),
  129 => 
  array (
    'match' => '2019 0D2F',
    'replace' => 'E00D',
  ),
  130 => 
  array (
    'match' => '2019 0D35',
    'replace' => 'E00F',
  ),
  131 => 
  array (
    'match' => '0D4D 0D31 ((0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39))',
    'replace' => 'E00E \\1',
  ),
  132 => 
  array (
    'match' => '0D4D 0D30 ((0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39))',
    'replace' => 'E00E \\1',
  ),
  133 => 
  array (
    'match' => '0D4D 0D31 ((E010|E011|E012|E013|E014|E015|E016|E017|E018|E019|E01A|E01B|E01C|E01D|E01E|E01F|E020|E021|E022|E023|E024|E025|E026|E027|E028|E029|E02A|E02B|E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E075|E076|E077|E078|E079|E07A|E07B|E07C|E07D))',
    'replace' => 'E00E \\1',
  ),
  134 => 
  array (
    'match' => '0D4D 0D30 ((E010|E011|E012|E013|E014|E015|E016|E017|E018|E019|E01A|E01B|E01C|E01D|E01E|E01F|E020|E021|E022|E023|E024|E025|E026|E027|E028|E029|E02A|E02B|E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E075|E076|E077|E078|E079|E07A|E07B|E07C|E07D))',
    'replace' => 'E00E \\1',
  ),
  135 => 
  array (
    'match' => '0D4D 0D31 ((0020|25CC))',
    'replace' => 'E00E \\1',
  ),
  136 => 
  array (
    'match' => '0D4D 0D30 ((0020|25CC))',
    'replace' => 'E00E \\1',
  ),
  137 => 
  array (
    'match' => '((0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 0D4D 0D2F',
    'replace' => '\\1 E00D',
  ),
  138 => 
  array (
    'match' => '((0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 2018 0D2F',
    'replace' => '\\1 E00D',
  ),
  139 => 
  array (
    'match' => '((E010|E011|E012|E013|E014|E015|E016|E017|E018|E019|E01A|E01B|E01C|E01D|E01E|E01F|E020|E021|E022|E023|E024|E025|E026|E027|E028|E029|E02A|E02B|E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E075|E076|E077|E078|E079|E07A|E07B|E07C|E07D)) 0D4D 0D2F',
    'replace' => '\\1 E00D',
  ),
  140 => 
  array (
    'match' => '((E010|E011|E012|E013|E014|E015|E016|E017|E018|E019|E01A|E01B|E01C|E01D|E01E|E01F|E020|E021|E022|E023|E024|E025|E026|E027|E028|E029|E02A|E02B|E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E075|E076|E077|E078|E079|E07A|E07B|E07C|E07D)) 2018 0D2F',
    'replace' => '\\1 E00D',
  ),
  141 => 
  array (
    'match' => '((0020|25CC)) 0D4D 0D2F',
    'replace' => '\\1 E00D',
  ),
  142 => 
  array (
    'match' => '((0020|25CC)) 2018 0D2F',
    'replace' => '\\1 E00D',
  ),
  143 => 
  array (
    'match' => '((0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 0D4D 0D35',
    'replace' => '\\1 E00F',
  ),
  144 => 
  array (
    'match' => '((0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 2018 0D35',
    'replace' => '\\1 E00F',
  ),
  145 => 
  array (
    'match' => '((E010|E011|E012|E013|E014|E015|E016|E017|E018|E019|E01A|E01B|E01C|E01D|E01E|E01F|E020|E021|E022|E023|E024|E025|E026|E027|E028|E029|E02A|E02B|E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E075|E076|E077|E078|E079|E07A|E07B|E07C|E07D)) 0D4D 0D35',
    'replace' => '\\1 E00F',
  ),
  146 => 
  array (
    'match' => '((E010|E011|E012|E013|E014|E015|E016|E017|E018|E019|E01A|E01B|E01C|E01D|E01E|E01F|E020|E021|E022|E023|E024|E025|E026|E027|E028|E029|E02A|E02B|E02C|E02D|E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E051|E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E075|E076|E077|E078|E079|E07A|E07B|E07C|E07D)) 2018 0D35',
    'replace' => '\\1 E00F',
  ),
  147 => 
  array (
    'match' => '((0020|25CC)) 0D4D 0D35',
    'replace' => '\\1 E00F',
  ),
  148 => 
  array (
    'match' => '((0020|25CC)) 2018 0D35',
    'replace' => '\\1 E00F',
  ),
  149 => 
  array (
    'match' => '0D28 0D4D',
    'replace' => 'E006',
  ),
  150 => 
  array (
    'match' => '0D28 007E',
    'replace' => 'E006',
  ),
  151 => 
  array (
    'match' => '0D23 0D4D',
    'replace' => 'E005',
  ),
  152 => 
  array (
    'match' => '0D23 007E',
    'replace' => 'E005',
  ),
  153 => 
  array (
    'match' => '0D32 0D4D',
    'replace' => 'E008',
  ),
  154 => 
  array (
    'match' => '0D32 007E',
    'replace' => 'E008',
  ),
  155 => 
  array (
    'match' => '0D33 0D4D',
    'replace' => 'E009',
  ),
  156 => 
  array (
    'match' => '0D33 007E',
    'replace' => 'E009',
  ),
  157 => 
  array (
    'match' => '0D31 0D4D',
    'replace' => 'E007',
  ),
  158 => 
  array (
    'match' => '0D31 007E',
    'replace' => 'E007',
  ),
  159 => 
  array (
    'match' => '0D30 0D4D',
    'replace' => 'E007',
  ),
  160 => 
  array (
    'match' => '0D30 007E',
    'replace' => 'E007',
  ),
  161 => 
  array (
    'match' => '(E00E) E005',
    'replace' => '\\1 E005 E00E',
  ),
  162 => 
  array (
    'match' => '(E00E) E006',
    'replace' => '\\1 E006 E00E',
  ),
  163 => 
  array (
    'match' => '(E00E) E007',
    'replace' => '\\1 E007 E00E',
  ),
  164 => 
  array (
    'match' => '(E00E) E008',
    'replace' => '\\1 E008 E00E',
  ),
  165 => 
  array (
    'match' => '(E00E) E009',
    'replace' => '\\1 E009 E00E',
  ),
  166 => 
  array (
    'match' => 'E00E E005 (E00E)',
    'replace' => 'E005 \\1',
  ),
  167 => 
  array (
    'match' => 'E00E E006 (E00E)',
    'replace' => 'E006 \\1',
  ),
  168 => 
  array (
    'match' => 'E00E E007 (E00E)',
    'replace' => 'E007 \\1',
  ),
  169 => 
  array (
    'match' => 'E00E E008 (E00E)',
    'replace' => 'E008 \\1',
  ),
  170 => 
  array (
    'match' => 'E00E E009 (E00E)',
    'replace' => 'E009 \\1',
  ),
  171 => 
  array (
    'match' => '(E00E (0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 0D4D ((0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39))',
    'replace' => '\\1 0D4D E00E \\3',
  ),
  172 => 
  array (
    'match' => 'E00E 0D15 (0D4D E00E)',
    'replace' => '0D15 \\1',
  ),
  173 => 
  array (
    'match' => 'E00E 0D16 (0D4D E00E)',
    'replace' => '0D16 \\1',
  ),
  174 => 
  array (
    'match' => 'E00E 0D17 (0D4D E00E)',
    'replace' => '0D17 \\1',
  ),
  175 => 
  array (
    'match' => 'E00E 0D18 (0D4D E00E)',
    'replace' => '0D18 \\1',
  ),
  176 => 
  array (
    'match' => 'E00E 0D19 (0D4D E00E)',
    'replace' => '0D19 \\1',
  ),
  177 => 
  array (
    'match' => 'E00E 0D1A (0D4D E00E)',
    'replace' => '0D1A \\1',
  ),
  178 => 
  array (
    'match' => 'E00E 0D1B (0D4D E00E)',
    'replace' => '0D1B \\1',
  ),
  179 => 
  array (
    'match' => 'E00E 0D1C (0D4D E00E)',
    'replace' => '0D1C \\1',
  ),
  180 => 
  array (
    'match' => 'E00E 0D1D (0D4D E00E)',
    'replace' => '0D1D \\1',
  ),
  181 => 
  array (
    'match' => 'E00E 0D1E (0D4D E00E)',
    'replace' => '0D1E \\1',
  ),
  182 => 
  array (
    'match' => 'E00E 0D1F (0D4D E00E)',
    'replace' => '0D1F \\1',
  ),
  183 => 
  array (
    'match' => 'E00E 0D20 (0D4D E00E)',
    'replace' => '0D20 \\1',
  ),
  184 => 
  array (
    'match' => 'E00E 0D21 (0D4D E00E)',
    'replace' => '0D21 \\1',
  ),
  185 => 
  array (
    'match' => 'E00E 0D22 (0D4D E00E)',
    'replace' => '0D22 \\1',
  ),
  186 => 
  array (
    'match' => 'E00E 0D23 (0D4D E00E)',
    'replace' => '0D23 \\1',
  ),
  187 => 
  array (
    'match' => 'E00E 0D24 (0D4D E00E)',
    'replace' => '0D24 \\1',
  ),
  188 => 
  array (
    'match' => 'E00E 0D25 (0D4D E00E)',
    'replace' => '0D25 \\1',
  ),
  189 => 
  array (
    'match' => 'E00E 0D26 (0D4D E00E)',
    'replace' => '0D26 \\1',
  ),
  190 => 
  array (
    'match' => 'E00E 0D27 (0D4D E00E)',
    'replace' => '0D27 \\1',
  ),
  191 => 
  array (
    'match' => 'E00E 0D28 (0D4D E00E)',
    'replace' => '0D28 \\1',
  ),
  192 => 
  array (
    'match' => 'E00E 0D2A (0D4D E00E)',
    'replace' => '0D2A \\1',
  ),
  193 => 
  array (
    'match' => 'E00E 0D2B (0D4D E00E)',
    'replace' => '0D2B \\1',
  ),
  194 => 
  array (
    'match' => 'E00E 0D2C (0D4D E00E)',
    'replace' => '0D2C \\1',
  ),
  195 => 
  array (
    'match' => 'E00E 0D2D (0D4D E00E)',
    'replace' => '0D2D \\1',
  ),
  196 => 
  array (
    'match' => 'E00E 0D2E (0D4D E00E)',
    'replace' => '0D2E \\1',
  ),
  197 => 
  array (
    'match' => 'E00E 0D2F (0D4D E00E)',
    'replace' => '0D2F \\1',
  ),
  198 => 
  array (
    'match' => 'E00E 0D30 (0D4D E00E)',
    'replace' => '0D30 \\1',
  ),
  199 => 
  array (
    'match' => 'E00E 0D31 (0D4D E00E)',
    'replace' => '0D31 \\1',
  ),
  200 => 
  array (
    'match' => 'E00E 0D32 (0D4D E00E)',
    'replace' => '0D32 \\1',
  ),
  201 => 
  array (
    'match' => 'E00E 0D33 (0D4D E00E)',
    'replace' => '0D33 \\1',
  ),
  202 => 
  array (
    'match' => 'E00E 0D34 (0D4D E00E)',
    'replace' => '0D34 \\1',
  ),
  203 => 
  array (
    'match' => 'E00E 0D35 (0D4D E00E)',
    'replace' => '0D35 \\1',
  ),
  204 => 
  array (
    'match' => 'E00E 0D36 (0D4D E00E)',
    'replace' => '0D36 \\1',
  ),
  205 => 
  array (
    'match' => 'E00E 0D37 (0D4D E00E)',
    'replace' => '0D37 \\1',
  ),
  206 => 
  array (
    'match' => 'E00E 0D38 (0D4D E00E)',
    'replace' => '0D38 \\1',
  ),
  207 => 
  array (
    'match' => 'E00E 0D39 (0D4D E00E)',
    'replace' => '0D39 \\1',
  ),
  208 => 
  array (
    'match' => '(0D46) E005',
    'replace' => '\\1 E005 0D46',
  ),
  209 => 
  array (
    'match' => '(0D46) E006',
    'replace' => '\\1 E006 0D46',
  ),
  210 => 
  array (
    'match' => '(0D46) E007',
    'replace' => '\\1 E007 0D46',
  ),
  211 => 
  array (
    'match' => '(0D46) E008',
    'replace' => '\\1 E008 0D46',
  ),
  212 => 
  array (
    'match' => '(0D46) E009',
    'replace' => '\\1 E009 0D46',
  ),
  213 => 
  array (
    'match' => '(0D47) E005',
    'replace' => '\\1 E005 0D47',
  ),
  214 => 
  array (
    'match' => '(0D47) E006',
    'replace' => '\\1 E006 0D47',
  ),
  215 => 
  array (
    'match' => '(0D47) E007',
    'replace' => '\\1 E007 0D47',
  ),
  216 => 
  array (
    'match' => '(0D47) E008',
    'replace' => '\\1 E008 0D47',
  ),
  217 => 
  array (
    'match' => '(0D47) E009',
    'replace' => '\\1 E009 0D47',
  ),
  218 => 
  array (
    'match' => '(0D48) E005',
    'replace' => '\\1 E005 0D48',
  ),
  219 => 
  array (
    'match' => '(0D48) E006',
    'replace' => '\\1 E006 0D48',
  ),
  220 => 
  array (
    'match' => '(0D48) E007',
    'replace' => '\\1 E007 0D48',
  ),
  221 => 
  array (
    'match' => '(0D48) E008',
    'replace' => '\\1 E008 0D48',
  ),
  222 => 
  array (
    'match' => '(0D48) E009',
    'replace' => '\\1 E009 0D48',
  ),
  223 => 
  array (
    'match' => '0D46 E005',
    'replace' => 'E005',
  ),
  224 => 
  array (
    'match' => '0D46 E006',
    'replace' => 'E006',
  ),
  225 => 
  array (
    'match' => '0D46 E007',
    'replace' => 'E007',
  ),
  226 => 
  array (
    'match' => '0D46 E008',
    'replace' => 'E008',
  ),
  227 => 
  array (
    'match' => '0D46 E009',
    'replace' => 'E009',
  ),
  228 => 
  array (
    'match' => '0D47 E005',
    'replace' => 'E005',
  ),
  229 => 
  array (
    'match' => '0D47 E006',
    'replace' => 'E006',
  ),
  230 => 
  array (
    'match' => '0D47 E007',
    'replace' => 'E007',
  ),
  231 => 
  array (
    'match' => '0D47 E008',
    'replace' => 'E008',
  ),
  232 => 
  array (
    'match' => '0D47 E009',
    'replace' => 'E009',
  ),
  233 => 
  array (
    'match' => '0D48 E005',
    'replace' => 'E005',
  ),
  234 => 
  array (
    'match' => '0D48 E006',
    'replace' => 'E006',
  ),
  235 => 
  array (
    'match' => '0D48 E007',
    'replace' => 'E007',
  ),
  236 => 
  array (
    'match' => '0D48 E008',
    'replace' => 'E008',
  ),
  237 => 
  array (
    'match' => '0D48 E009',
    'replace' => 'E009',
  ),
  238 => 
  array (
    'match' => '(0D46 (0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 0D4D',
    'replace' => '\\1 0D4D 0D46',
  ),
  239 => 
  array (
    'match' => '(0D47 (0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 0D4D',
    'replace' => '\\1 0D4D 0D47',
  ),
  240 => 
  array (
    'match' => '(0D48 (0D15|0D16|0D17|0D18|0D19|0D1A|0D1B|0D1C|0D1D|0D1E|0D1F|0D20|0D21|0D22|0D23|0D24|0D25|0D26|0D27|0D28|0D2A|0D2B|0D2C|0D2D|0D2E|0D2F|0D30|0D31|0D32|0D33|0D34|0D35|0D36|0D37|0D38|0D39)) 0D4D',
    'replace' => '\\1 0D4D 0D48',
  ),
  241 => 
  array (
    'match' => '0D46 0D15 (0D4D)',
    'replace' => '0D15 \\1',
  ),
  242 => 
  array (
    'match' => '0D46 0D16 (0D4D)',
    'replace' => '0D16 \\1',
  ),
  243 => 
  array (
    'match' => '0D46 0D17 (0D4D)',
    'replace' => '0D17 \\1',
  ),
  244 => 
  array (
    'match' => '0D46 0D18 (0D4D)',
    'replace' => '0D18 \\1',
  ),
  245 => 
  array (
    'match' => '0D46 0D19 (0D4D)',
    'replace' => '0D19 \\1',
  ),
  246 => 
  array (
    'match' => '0D46 0D1A (0D4D)',
    'replace' => '0D1A \\1',
  ),
  247 => 
  array (
    'match' => '0D46 0D1B (0D4D)',
    'replace' => '0D1B \\1',
  ),
  248 => 
  array (
    'match' => '0D46 0D1C (0D4D)',
    'replace' => '0D1C \\1',
  ),
  249 => 
  array (
    'match' => '0D46 0D1D (0D4D)',
    'replace' => '0D1D \\1',
  ),
  250 => 
  array (
    'match' => '0D46 0D1E (0D4D)',
    'replace' => '0D1E \\1',
  ),
  251 => 
  array (
    'match' => '0D46 0D1F (0D4D)',
    'replace' => '0D1F \\1',
  ),
  252 => 
  array (
    'match' => '0D46 0D20 (0D4D)',
    'replace' => '0D20 \\1',
  ),
  253 => 
  array (
    'match' => '0D46 0D21 (0D4D)',
    'replace' => '0D21 \\1',
  ),
  254 => 
  array (
    'match' => '0D46 0D22 (0D4D)',
    'replace' => '0D22 \\1',
  ),
  255 => 
  array (
    'match' => '0D46 0D23 (0D4D)',
    'replace' => '0D23 \\1',
  ),
  256 => 
  array (
    'match' => '0D46 0D24 (0D4D)',
    'replace' => '0D24 \\1',
  ),
  257 => 
  array (
    'match' => '0D46 0D25 (0D4D)',
    'replace' => '0D25 \\1',
  ),
  258 => 
  array (
    'match' => '0D46 0D26 (0D4D)',
    'replace' => '0D26 \\1',
  ),
  259 => 
  array (
    'match' => '0D46 0D27 (0D4D)',
    'replace' => '0D27 \\1',
  ),
  260 => 
  array (
    'match' => '0D46 0D28 (0D4D)',
    'replace' => '0D28 \\1',
  ),
  261 => 
  array (
    'match' => '0D46 0D2A (0D4D)',
    'replace' => '0D2A \\1',
  ),
  262 => 
  array (
    'match' => '0D46 0D2B (0D4D)',
    'replace' => '0D2B \\1',
  ),
  263 => 
  array (
    'match' => '0D46 0D2C (0D4D)',
    'replace' => '0D2C \\1',
  ),
  264 => 
  array (
    'match' => '0D46 0D2D (0D4D)',
    'replace' => '0D2D \\1',
  ),
  265 => 
  array (
    'match' => '0D46 0D2E (0D4D)',
    'replace' => '0D2E \\1',
  ),
  266 => 
  array (
    'match' => '0D46 0D2F (0D4D)',
    'replace' => '0D2F \\1',
  ),
  267 => 
  array (
    'match' => '0D46 0D30 (0D4D)',
    'replace' => '0D30 \\1',
  ),
  268 => 
  array (
    'match' => '0D46 0D31 (0D4D)',
    'replace' => '0D31 \\1',
  ),
  269 => 
  array (
    'match' => '0D46 0D32 (0D4D)',
    'replace' => '0D32 \\1',
  ),
  270 => 
  array (
    'match' => '0D46 0D33 (0D4D)',
    'replace' => '0D33 \\1',
  ),
  271 => 
  array (
    'match' => '0D46 0D34 (0D4D)',
    'replace' => '0D34 \\1',
  ),
  272 => 
  array (
    'match' => '0D46 0D35 (0D4D)',
    'replace' => '0D35 \\1',
  ),
  273 => 
  array (
    'match' => '0D46 0D36 (0D4D)',
    'replace' => '0D36 \\1',
  ),
  274 => 
  array (
    'match' => '0D46 0D37 (0D4D)',
    'replace' => '0D37 \\1',
  ),
  275 => 
  array (
    'match' => '0D46 0D38 (0D4D)',
    'replace' => '0D38 \\1',
  ),
  276 => 
  array (
    'match' => '0D46 0D39 (0D4D)',
    'replace' => '0D39 \\1',
  ),
  277 => 
  array (
    'match' => '0D47 0D15 (0D4D)',
    'replace' => '0D15 \\1',
  ),
  278 => 
  array (
    'match' => '0D47 0D16 (0D4D)',
    'replace' => '0D16 \\1',
  ),
  279 => 
  array (
    'match' => '0D47 0D17 (0D4D)',
    'replace' => '0D17 \\1',
  ),
  280 => 
  array (
    'match' => '0D47 0D18 (0D4D)',
    'replace' => '0D18 \\1',
  ),
  281 => 
  array (
    'match' => '0D47 0D19 (0D4D)',
    'replace' => '0D19 \\1',
  ),
  282 => 
  array (
    'match' => '0D47 0D1A (0D4D)',
    'replace' => '0D1A \\1',
  ),
  283 => 
  array (
    'match' => '0D47 0D1B (0D4D)',
    'replace' => '0D1B \\1',
  ),
  284 => 
  array (
    'match' => '0D47 0D1C (0D4D)',
    'replace' => '0D1C \\1',
  ),
  285 => 
  array (
    'match' => '0D47 0D1D (0D4D)',
    'replace' => '0D1D \\1',
  ),
  286 => 
  array (
    'match' => '0D47 0D1E (0D4D)',
    'replace' => '0D1E \\1',
  ),
  287 => 
  array (
    'match' => '0D47 0D1F (0D4D)',
    'replace' => '0D1F \\1',
  ),
  288 => 
  array (
    'match' => '0D47 0D20 (0D4D)',
    'replace' => '0D20 \\1',
  ),
  289 => 
  array (
    'match' => '0D47 0D21 (0D4D)',
    'replace' => '0D21 \\1',
  ),
  290 => 
  array (
    'match' => '0D47 0D22 (0D4D)',
    'replace' => '0D22 \\1',
  ),
  291 => 
  array (
    'match' => '0D47 0D23 (0D4D)',
    'replace' => '0D23 \\1',
  ),
  292 => 
  array (
    'match' => '0D47 0D24 (0D4D)',
    'replace' => '0D24 \\1',
  ),
  293 => 
  array (
    'match' => '0D47 0D25 (0D4D)',
    'replace' => '0D25 \\1',
  ),
  294 => 
  array (
    'match' => '0D47 0D26 (0D4D)',
    'replace' => '0D26 \\1',
  ),
  295 => 
  array (
    'match' => '0D47 0D27 (0D4D)',
    'replace' => '0D27 \\1',
  ),
  296 => 
  array (
    'match' => '0D47 0D28 (0D4D)',
    'replace' => '0D28 \\1',
  ),
  297 => 
  array (
    'match' => '0D47 0D2A (0D4D)',
    'replace' => '0D2A \\1',
  ),
  298 => 
  array (
    'match' => '0D47 0D2B (0D4D)',
    'replace' => '0D2B \\1',
  ),
  299 => 
  array (
    'match' => '0D47 0D2C (0D4D)',
    'replace' => '0D2C \\1',
  ),
  300 => 
  array (
    'match' => '0D47 0D2D (0D4D)',
    'replace' => '0D2D \\1',
  ),
  301 => 
  array (
    'match' => '0D47 0D2E (0D4D)',
    'replace' => '0D2E \\1',
  ),
  302 => 
  array (
    'match' => '0D47 0D2F (0D4D)',
    'replace' => '0D2F \\1',
  ),
  303 => 
  array (
    'match' => '0D47 0D30 (0D4D)',
    'replace' => '0D30 \\1',
  ),
  304 => 
  array (
    'match' => '0D47 0D31 (0D4D)',
    'replace' => '0D31 \\1',
  ),
  305 => 
  array (
    'match' => '0D47 0D32 (0D4D)',
    'replace' => '0D32 \\1',
  ),
  306 => 
  array (
    'match' => '0D47 0D33 (0D4D)',
    'replace' => '0D33 \\1',
  ),
  307 => 
  array (
    'match' => '0D47 0D34 (0D4D)',
    'replace' => '0D34 \\1',
  ),
  308 => 
  array (
    'match' => '0D47 0D35 (0D4D)',
    'replace' => '0D35 \\1',
  ),
  309 => 
  array (
    'match' => '0D47 0D36 (0D4D)',
    'replace' => '0D36 \\1',
  ),
  310 => 
  array (
    'match' => '0D47 0D37 (0D4D)',
    'replace' => '0D37 \\1',
  ),
  311 => 
  array (
    'match' => '0D47 0D38 (0D4D)',
    'replace' => '0D38 \\1',
  ),
  312 => 
  array (
    'match' => '0D47 0D39 (0D4D)',
    'replace' => '0D39 \\1',
  ),
  313 => 
  array (
    'match' => '0D48 0D15 (0D4D)',
    'replace' => '0D15 \\1',
  ),
  314 => 
  array (
    'match' => '0D48 0D16 (0D4D)',
    'replace' => '0D16 \\1',
  ),
  315 => 
  array (
    'match' => '0D48 0D17 (0D4D)',
    'replace' => '0D17 \\1',
  ),
  316 => 
  array (
    'match' => '0D48 0D18 (0D4D)',
    'replace' => '0D18 \\1',
  ),
  317 => 
  array (
    'match' => '0D48 0D19 (0D4D)',
    'replace' => '0D19 \\1',
  ),
  318 => 
  array (
    'match' => '0D48 0D1A (0D4D)',
    'replace' => '0D1A \\1',
  ),
  319 => 
  array (
    'match' => '0D48 0D1B (0D4D)',
    'replace' => '0D1B \\1',
  ),
  320 => 
  array (
    'match' => '0D48 0D1C (0D4D)',
    'replace' => '0D1C \\1',
  ),
  321 => 
  array (
    'match' => '0D48 0D1D (0D4D)',
    'replace' => '0D1D \\1',
  ),
  322 => 
  array (
    'match' => '0D48 0D1E (0D4D)',
    'replace' => '0D1E \\1',
  ),
  323 => 
  array (
    'match' => '0D48 0D1F (0D4D)',
    'replace' => '0D1F \\1',
  ),
  324 => 
  array (
    'match' => '0D48 0D20 (0D4D)',
    'replace' => '0D20 \\1',
  ),
  325 => 
  array (
    'match' => '0D48 0D21 (0D4D)',
    'replace' => '0D21 \\1',
  ),
  326 => 
  array (
    'match' => '0D48 0D22 (0D4D)',
    'replace' => '0D22 \\1',
  ),
  327 => 
  array (
    'match' => '0D48 0D23 (0D4D)',
    'replace' => '0D23 \\1',
  ),
  328 => 
  array (
    'match' => '0D48 0D24 (0D4D)',
    'replace' => '0D24 \\1',
  ),
  329 => 
  array (
    'match' => '0D48 0D25 (0D4D)',
    'replace' => '0D25 \\1',
  ),
  330 => 
  array (
    'match' => '0D48 0D26 (0D4D)',
    'replace' => '0D26 \\1',
  ),
  331 => 
  array (
    'match' => '0D48 0D27 (0D4D)',
    'replace' => '0D27 \\1',
  ),
  332 => 
  array (
    'match' => '0D48 0D28 (0D4D)',
    'replace' => '0D28 \\1',
  ),
  333 => 
  array (
    'match' => '0D48 0D2A (0D4D)',
    'replace' => '0D2A \\1',
  ),
  334 => 
  array (
    'match' => '0D48 0D2B (0D4D)',
    'replace' => '0D2B \\1',
  ),
  335 => 
  array (
    'match' => '0D48 0D2C (0D4D)',
    'replace' => '0D2C \\1',
  ),
  336 => 
  array (
    'match' => '0D48 0D2D (0D4D)',
    'replace' => '0D2D \\1',
  ),
  337 => 
  array (
    'match' => '0D48 0D2E (0D4D)',
    'replace' => '0D2E \\1',
  ),
  338 => 
  array (
    'match' => '0D48 0D2F (0D4D)',
    'replace' => '0D2F \\1',
  ),
  339 => 
  array (
    'match' => '0D48 0D30 (0D4D)',
    'replace' => '0D30 \\1',
  ),
  340 => 
  array (
    'match' => '0D48 0D31 (0D4D)',
    'replace' => '0D31 \\1',
  ),
  341 => 
  array (
    'match' => '0D48 0D32 (0D4D)',
    'replace' => '0D32 \\1',
  ),
  342 => 
  array (
    'match' => '0D48 0D33 (0D4D)',
    'replace' => '0D33 \\1',
  ),
  343 => 
  array (
    'match' => '0D48 0D34 (0D4D)',
    'replace' => '0D34 \\1',
  ),
  344 => 
  array (
    'match' => '0D48 0D35 (0D4D)',
    'replace' => '0D35 \\1',
  ),
  345 => 
  array (
    'match' => '0D48 0D36 (0D4D)',
    'replace' => '0D36 \\1',
  ),
  346 => 
  array (
    'match' => '0D48 0D37 (0D4D)',
    'replace' => '0D37 \\1',
  ),
  347 => 
  array (
    'match' => '0D48 0D38 (0D4D)',
    'replace' => '0D38 \\1',
  ),
  348 => 
  array (
    'match' => '0D48 0D39 (0D4D)',
    'replace' => '0D39 \\1',
  ),
  349 => 
  array (
    'match' => '0D41 0D4D',
    'replace' => 'E003',
  ),
  350 => 
  array (
    'match' => '007E',
    'replace' => '0D4D',
  ),
  351 => 
  array (
    'match' => '2018',
    'replace' => '0D4D',
  ),
  352 => 
  array (
    'match' => '2019',
    'replace' => '0D4D',
  ),
);
?>