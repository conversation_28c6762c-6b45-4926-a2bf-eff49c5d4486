{* reklama na produkty *}
{if !empty($footerAdd)}
<div class="product-tip">

  <a href="
    {if $footerAdd->meipagid > 0}
      {plink Page:detail $footerAdd->pagurlkey}
    {elseif $footerAdd->meicatid > 0}
      {plink Catalog:detail $footerAdd->catid, ($footerAdd|getCatKey)}
    {elseif !empty($footerAdd->meiprocode)}
      {plink Product:detail $footerAdd->proid, ($footerAdd|getProKey)}
    {else}
      {$footerAdd->meiurl}
    {/if}
    ">

    <div class="product-tip__wrapper">
      {php
      $fileName = WWW_DIR."/pic/menuindex/".$footerAdd->meiid.".jpg";

      $fileExist = file_exists($fileName);

      $arrTexts = array();
      if (!empty($footerAdd->meidesc)){
        $arrTexts = explode("\n", trim($footerAdd->meidesc));
      }
      }
      {if $fileExist}<p class="product-tip__image"><img src="{$baseUri}/pic/menuindex/{$footerAdd->meiid}.jpg" alt="{$footerAdd->meiname}"></p>{/if}
      {foreach $arrTexts as $text}
        {if $iterator->getCounter() == 1}
          <h3 class="product-tip__header">{$text|noescape} <i class="icon icon--dash"></i></h3>
        {elseif $iterator->getCounter() == 2}
          <p class="product-tip__content">{$text|noescape}</p>
        {elseif $iterator->getCounter() == 3}
          {$text|noescape}
        {/if}
      {/foreach}

    </div>

  </a>

</div>
{/if}
