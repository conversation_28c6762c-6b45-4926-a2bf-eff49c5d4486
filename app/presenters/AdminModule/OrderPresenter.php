<?php
namespace AdminModule;

use Nette,
  dibi,
  Model;

final class OrderPresenter extends BasePresenter {

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sAdmin = Null;

  /** @persistent */
  public $sStatus = '';

  /** @persistent */
  public $sStatStat = '';

  /** @persistent */
  public $sCoupon = '';

  /** @persistent */
  public $sDateFrom;

  /** @persistent */
  public $sDateTo;

  /** @persistent */
  public $sNotClosed =  true;

  /** @persistent */
  public $sOrderBy = 'orddatec';

  /** @persistent */
  public $sOrderByType = 'ASC';

  public function orderEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $orders = new \Model\OrdersModel();
      $id = $this->getParameter('id');
      $values = $form->getValues();
      //nactu si objednavku pokud existuje
      $order = false;
      if ($id > 0) {
        $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
        $orders->setCurrency($this->currencies, $order->ordcurid);
      }

      try {
        $orders->save($id, $values);
      } catch (\Exception $e) {
        $this->flashMessage($e->getMessage(), "err");

        $this->redirect("edit", $id);
      }

      $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
      $orders->recalcOrder($id);
      $this->flashMessage('Uloženo v pořádku');
    }
    $this->redirect('edit', $id);
  }

  public function orderChangeStateFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVals = $form->getValues();
      $formVals["ordid"] = $this->getParameter('id');
      $this->changeOrderStatus($formVals, TRUE,  $this->adminData->admid);
    }
    $this->redirect('default');
  }

  public function renderStats() {
    $where = "";

    if ($this->sStatStat === 'closed') {
      $where .= " ordstatus IN (4) AND ";
    } else if ($this->sStatStat === 'storno') {
      $where .= " ordstatus IN (5,7,10) AND ";
    }

    if (!empty($this->sDateFrom)) {
      $where .= " orddatec>='" . $this->formatDateMySQL($this->sDateFrom)  .  "' AND ";
    }

    if (!empty($this->sDateTo)) {
      $where .= " orddatec<='" . $this->formatDateMySQL($this->sDateTo)  .  "' AND ";
    }
    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }
    $this->template->dataRows = dibi::fetchAll("
      SELECT MONTH(orddatec) ordmonth, YEAR(orddatec) as ordyear, SUM(ordpricevat) AS ordpricevatsum
        FROM orders " . $where . " GROUP BY CONCAT(MONTH(orddatec), YEAR(orddatec)) order by orddatec
    ");
  }


  public function renderDefault() {
    $orders = new \Model\OrdersModel();
    //$where = "orddatec + INTERVAL 3 DAY >= Now() AND ";
    $where = "";
    if (!empty($this->sCode)) $where .= " ordcode LIKE '%$this->sCode%' AND ";
    if (!empty($this->sAdmin)) $where .= " ordadmid = ".$this->sAdmin." AND ";
    if (!empty($this->sName)) $where .= " (ordilname LIKE '%$this->sName%' OR ordstlname LIKE '%$this->sName%' OR ordstfirname LIKE '%$this->sName%' OR ordifirname LIKE '%$this->sName%') AND ";
    if ($this->sNotClosed) $where .= " ordstatus NOT IN (4,5,7,10) AND ";
    if ((string)$this->sStatus!='') $where .= " ordstatus=$this->sStatus AND ";
    if (!empty($this->sCoupon)) $where .= " ordcoucode = '$this->sCoupon' AND ";

    // Filtrování podle datumu od-do
    if (!empty($this->sDateFrom)) {
      $where .= " orddatec>='" . $this->formatDateMySQL($this->sDateFrom)  .  "' AND ";
    }
    if (!empty($this->sDateTo)) {
      $where .= " orddatec<='" . $this->formatDateMySQL($this->sDateTo)  .  "' AND ";
    }

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY orddatec ASC";
    }
    $dataSource = dibi::dataSource("
    SELECT orders.*, d.delname AS delname, dm.delname AS delnamemas, dm.delcode AS delcode, admname,
    (SELECT orldatec FROM orders_log WHERE orlordid=ordid AND orlstatus=3 ORDER BY orldatec DESC LIMIT 1) AS datesend,
    (SELECT MAX(oriprobigsize) FROM orditems WHERE oriordid=ordid) AS oriprobigsize,
    (SELECT MAX(oriprooffer) FROM orditems WHERE oriordid=ordid) AS oriprooffer
    FROM orders
    LEFT JOIN deliverymodes AS d ON (orddelid=d.delid)
    LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
    LEFT JOIN admins ON (ordadmid=admid)
    $where
    GROUP BY ordid
    $orderBy
    ");

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    /*
    foreach ($dataRows as $row) {
      $row->bl = $orders->blAnalyse($row);
    }
    */

    //ciselnik statusu
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }

  public function renderAutocompleteProducts() {
    $term = $this->getParameter('term');
    if (!empty($term)) $this->template->rows = dibi::fetchAll("SELECT proid, procode, proname, proprice".$this->curId."a AS propricea FROM products WHERE proismaster=0 AND proname LIKE '%$term%' OR  procode LIKE '$term%'");
  }

  public function renderEdit($id) {
    $form = $this['orderEditForm'];
    $formState = $this['orderChangeStateForm'];

    if (!$form->isSubmitted() && !$formState->isSubmitted()) {
      $orders = new \Model\OrdersModel();
      $dataRow = $orders->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }

      //naformatuju datum
      $form->setDefaults($dataRow);
      $formState->setDefaults($dataRow);

      $this->template->dataRow = $dataRow;

      //doplnim polozky objednavky
      $this->template->ordItems = dibi::query("SELECT * FROM orditems LEFT JOIN products ON (proid=oriproid) WHERE oriordid=%i", $dataRow->ordid)->fetchAssoc('oriid');

      //doplnim polozku se slevou
      $this->template->ordItemDisc = dibi::fetch("SELECT * from orditems where oriordid=%i AND oritypid=3", $dataRow->ordid);
      //načtu zpusb dopravy
      $this->template->ordPayType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $dataRow->orddelid);
      $this->template->ordDelType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $this->template->ordPayType->delmasid);

      if (!empty($dataRow->orddelspec)) {
        //zkontroluji platné odběrné místa ulozenky
        $dels = new Model\DeliveryModesModel();
        $delCode = $this->template->ordDelType->delcode;
        if ($delCode === "ULOZENKA") {
          $arr = $dels->getEnumUlozenkaPlaces();
          if (!array_key_exists($dataRow->orddelspec, $arr)) {
            $this->template->message = "Chybně zadáno odběrné místo!!! ($dataRow->orddelspec)";
            unset($dataRow->orddelspec);
          }
        } else if ($delCode === "CESKA_POSTA_BALIKOVNA") {
          $arr = $dels->getEnumBalikovnaPlaces();
          if (!array_key_exists($dataRow->orddelspec, $arr)) {
            $this->template->message = "Chybně zadáno odběrné místo!!! ($dataRow->orddelspec)";
            unset($dataRow->orddelspec);
          }
        }
      }


       //doplnim log zmen
      $this->template->statusLog = dibi::fetchAll("
      SELECT orldatec, orlstatus, admname AS orladmname, orlnote
      FROM orders_log
      LEFT JOIN admins ON (admid=orladmid)
      WHERE
      orlordid=%i", $dataRow->ordid, "ORDER BY orlid DESC");

      $this->template->enum_ordstatus = $orders->getEnumOrdStatus();

      //kontrola cerne listiny
      $this->template->bl = $orders->blAnalyse($dataRow);

      //eet data
      $this->template->eetRows = dibi::fetchAll('SELECT * FROM eet_log WHERE eet_log.logordid=%i', $dataRow->ordid);

    }
  }

  public function actionDelete($id) {
    if ($id > 0) {
      $orders = new \Model\OrdersModel();
      $orders->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }

  public function actionCsobReverse($ordid) {
    $ords = new \Model\OrdersModel();
    $ord = $ords->load($ordid);
    if (!empty($ord->ordpaymentid)) {
      $config = $this->neonParameters["csob"];
      $csob = new \CsobPayment($config);
      if ($csob->paymentReverse($ord->ordpaymentid)){
        $this->flashMessage("Platbu se podařilo zrušit");
      } else {
        $this->flashMessage("Platbu se NEpodařilo zrušit", 'err');
      }
    } else {
      $this->flashMessage("ID platby se nepodařilo zjistit", 'err');
    }
    $this->redirect("edit", $ordid);
  }

  public function actionMakeInvoice($id) {
    if ($id > 0) {
      $orders = new \Model\OrdersModel();
      try {
        $orders->makeInvoice($id);
        $this->flashMessage('Faktura byla vystavena');
      } catch (ModelException $e) {
        $this->flashMessage($e->getMessage(), "danger");
      }
    }
    $this->redirect('edit', $id);
  }

  public function actionPrint($id) {
    if ($id > 0) {
      $this->printOrder($id, 'D');
    }
    //$this->redirect('default');
  }

  public function actionExportOrderXml($id) {
    if ($id > 0) {
      $fileName = $this->exportOrderXml($id);
      $fullPath = WWW_DIR . '/../data/export/orders/';
      header('Content-type: text/xml');
      header('Content-Disposition: attachment; filename="'.$fileName.'"');
      echo file_get_contents($fullPath.$fileName);
    }
    $this->terminate();
  }

  public function actionPrintInvoice($id, $target='I') {
    if ($id > 0) {
      $this->printOrder($id, $target, 'Invoice.latte');
    }
    //$this->redirect('default');
  }

  public function actionBatchAction() {
    $ulozenka = $this->getParameter('export_ulozenka');
    if (isset($ulozenka)) {
      $ids = $this->getParameter('ordid');
      $this->redirect(':Front:Export:ulozenka', array('ids'=>$ids));
    }
    $changeStatus = $this->getParameter('change_status');
    if (isset($changeStatus)) {
      $status = $this->getParameter('ordstatus');
      $this->redirect('batchStatus', array('statuses'=>$status));
    }
  }

  public function actionBatchStatus(array $statuses) {
    foreach ($statuses as $key => $value) {
      $this->changeOrderStatus(array('ordid'=>$key, 'ordstatus'=>$value), TRUE,  $this->adminData->admid);
    }
    $this->redirect('default');
  }

  public function actionDeleteItem($id, $ordid) {
    if ($id > 0) {
      $orders = new \Model\OrdersModel();
      $ordItems = new \Model\OrdItemsModel();
      $ordItems->delete($id);
      $orders->recalcOrder($ordid);
      $this->flashMessage('Položka byla vymazána');
    }
    $this->redirect('edit', $ordid);
  }

  /********************* facilities *********************/
  protected function createComponentOrderChangeStateForm() {
    $order = new \Model\OrdersModel();
    $form = $this->createAppForm();

    $enum_ordstatus = $order->getEnumOrdStatus();
    /*
    //načtu zpusb dopravy
    $ordPayType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $ord->orddelid);
    $ordDelType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $ordPayType->delmasid);


    //podle typu dopravy vyhodím některé statusy
    if ($ordDelType->delcode === 'OSOBNE') {
      unset($enum_ordstatus[3]);
    }

    $id = (int)$this->getParameter('id');
    $ord = $order->load($id);
    */

    $form->addSelect("ordstatus", "Nový stav objednávky", $enum_ordstatus);
    $form->addSubmit('newstate', 'Zmenit stav');
    $form->onSuccess[] = array($this, 'orderChangeStateFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }


  protected function createComponentOrderEditForm() {
    $order = new \Model\OrdersModel();
    $dels = new \Model\DeliveryModesModel();
    $enums = new \Model\EnumcatsModel();
    $admins = new \Model\AdminsModel();
    $enumCountries = $enums->getEnumCountries(false);
    $id = $this->getParameter('id');
    $ord = $order->load($id);

    $payMode = $dels->load($ord->orddelid);
    $delMode = $dels->load($payMode->delmasid);

    $form = $this->createAppForm();

    $form->addGroup('');
    $form->addCheckbox("ordpaystatus", "Zaplaceno");
    $form->addText("ordpricevat", "Celková cena:", 15)
      ->setDisabled(True);
    $form->addText("orddisc", "Celková sleva:", 15)
      ->setDisabled(True);

    $form->addText("ordweight", "Celková hmotnost:", 15)
      ->setDisabled(True);

    $form->addSelect("ordadmid", "Obchodník:", $admins->getEnumAdmins())
      ->setPrompt('');

    $form->addText("ordparcode", "Číslo balíku:", 15);

    $form->addText("orddiscpercent", "Sleva v %:", 15)
      ->setRequired(FALSE)
      ->addRule(Nette\Forms\Form::NUMERIC, "Sleva v procentech musí být číslo;");

    $form->addText("ordcoucode", "Slevový kupón:", 15);

    $form->addselect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId(FALSE))
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    $payId = $ord->orddelid;


    if ($delMode->delcode == 'ULOZENKA') {
      $arr = $dels->getEnumUlozenkaPlaces();
      if (array_key_exists($ord->orddelspec, $arr) || empty($ord->orddelspec)) {
        $form->addselect("orddelspec", "Uloženka:", $dels->getEnumUlozenkaPlaces())
          ->setPrompt("Nutno zadat ...");
      } else {
        $form->addText("orddelspec", "Uloženka:", 20)
          ->setDefaultValue($ord->orddelspec)
          ->addRule(Nette\Forms\Form::FILLED, "Nutno zadat odběrné místo");
      }
    } else if ($delMode->delcode == 'CESKA_POSTA_BALIKOVNA') {
      $arr = $dels->getEnumBalikovnaPlaces();
      if (array_key_exists($ord->orddelspec, $arr) || empty($ord->orddelspec)) {
        $form->addselect("orddelspec", "Balíkovna:", $arr)
          ->setPrompt("Nutno zadat ...");
      }
    }

    $form->addText("ordmail", "Email:", 20);
    $form->addText("ordtel", "Telefon:", 10);

    $form->addGroup('Fakturační adresa');

    $form->addText("ordiname", "Jméno:", 60);
    $form->addText("ordilname", "Přijmení:", 60);
    $form->addText("ordifirname", "Název firmy:", 60);

    $form->addText("ordistreet", "Ulice:", 60);
    $form->addText("ordistreetno", "Číslo popisné:", 60);
    $form->addText("ordicity", "Město, obec:", 60);
    $form->addText("ordipostcode", "PSČ:", 6);
    //$form->addSelect("ordicouid", "Země:", $enumCountries);

    $form->addText("ordic", "IČ:", 10);
    $form->addText("orddic", "DIČ:", 10);
    $form->addCheckbox("ordusrvat", "Plátce DPH");

    $form->addGroup('Dodací adresa');

    $form->addText("ordstname", "Jméno:", 60);
    $form->addText("ordstlname", "Přijmení:", 60);
    $form->addText("ordstfirname", "Název firmy:", 60);

    $form->addText("ordststreet", "Ulice:", 60);
    $form->addText("ordststreetno", "Číslo popisné:", 60);
    $form->addText("ordstcity", "Město, obec:", 60);
    $form->addText("ordstpostcode", "PSČ:", 6);
    //$form->addSelect("ordstcouid", "Země:", $enumCountries);

    $form->addCheckbox("ordtransfered", "Přeneseno");

    $form->addGroup('Poznámka');

    $form->addTextArea("ordnote", "", 100, 3);
    /*
    $form->addGroup('Fakturační údaje');
    $form->addText("ordinvcode", "Číslo faktury:", 10);
    //$form->addText("ordinvdate", "Datum vystavení:", 10);
    */

    $form->addSubmit('makeorder', 'Uložit');
    $form->onSuccess[] = array($this, 'orderEditFormSubmitted');
    return $form;
  }

  protected function createComponentOrdItemsEditForm() {
    $form = $this->createAppForm();
    $ordid = (int)$this->getParameter("id");
    //nactu polozky objedmavky
    if ($ordid > 0) {
      $form->addContainer('items');
      $rows = dibi::fetchAll("SELECT * FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordid, " ORDER BY oriname");
      foreach ($rows as $row) {
        $key = 'item_'.$row->oriid;
        $form["items"]->addContainer($key);
        $form["items"][$key]->addHidden("oriid", $row->oriid);
        $form["items"][$key]->addText("oriproid", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ID zboží.')
          ->setDefaultValue($row->oriproid);
        $form["items"][$key]->addText("oriname", "", 50)
          ->setAttribute('class', 'autocomplete')
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název položky.')
          ->setDefaultValue($row->oriname);
        $form["items"][$key]->addText("oriprice", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte cenu položky.')
          ->setDefaultValue($row->oriprice);
        $form["items"][$key]->addText("sn", "", 20);
        $form["items"][$key]->addText("oriqty", "", 3)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte počet položek.')
          ->setDefaultValue($row->oriqty);
      }
      //postovne
      $deliv = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $ordid, " ORDER BY oriname");
      if ($deliv!==false) {
        $form['items']->addContainer('delivery');
        $form['items']['delivery']->addHidden("oriid", $deliv->oriid);
        $form['items']['delivery']->addHidden("oritypid", 1);
        $form['items']['delivery']->addHidden("oriqty", 1);
        $form['items']['delivery']->addText("oriname", "", 50)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte popis poštovného.')
          ->setDefaultValue($deliv->oriname);
        $form['items']['delivery']->addText("oripricemaster", "", 5)
          ->setDefaultValue($deliv->oripricemaster);
      }
      //nova polozka
      $form->addContainer('newitem');
      $form['newitem']->addHidden("oriordid", $ordid);
      $form['newitem']->addText("oriproid", "", 5);
      $form['newitem']->addText("oriname", "", 50)
        ->setAttribute('class', 'autocomplete');
      $form['newitem']->addText("oriprice", "", 5);
      $form['newitem']->addText("oriqty", "", 3)
        ->setDefaultValue(1);
    }
    $form->addSubmit('saveitems', 'Uložit');
    $form->onSuccess[] = array($this, 'ordItemsEditFormSubmitted');

    return $form;
  }

   public function ordItemsEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $ordid = (int)$this->getParameter("id");
      $formVals = $form->getValues(TRUE);

      $orders = new \Model\OrdersModel();
      $products = new \Model\ProductsModel();
      $ordItems = new \Model\OrdItemsModel();
      //nejdrive zjistim jestli nepipnul S/N
      $isSn = false;
      foreach ($formVals['items'] as $item) {
        if (!empty($item["sn"])) {
          $isSn = true;
          dibi::query("UPDATE orditems SET orisn=CONCAT(COALESCE(orisn, ''), IF(orisn IS NOT NULL, '|', ''), '".$item["sn"]."') WHERE oriid=%i", $item["oriid"]);
        }
      }
      if ($isSn) $this->redirect('edit#edititems', $ordid);

      foreach ($formVals['items'] as $item) {
        $id = $item["oriid"];
        unset($item["oriid"]);
        unset($item["sn"]);
        if (isset($item["oripricemaster"])) {
          if (trim($item["oripricemaster"]) == "") $item["oripricemaster"] = NULL;
        }
        if (!empty($item["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $item["oriproid"]);
          $item["oriprocode"] = $product->procode;
          $item["oriprocode2"] = $product->procode2;
          $item["orivatid"] = $product->provatid;
        }
        $ordItems->update($id, $item);

      }
      if (!empty($formVals['newitem']['oriname'])) {
        if (!empty($formVals['newitem']["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $formVals['newitem']["oriproid"]);
          $formVals['newitem']["oriprocode"] = $product->procode;
          $formVals['newitem']["oriprocode2"] = $product->procode2;
          $formVals['newitem']["orivatid"] = $product->provatid;
          $oriid = $ordItems->insert($formVals['newitem']);

          //pokud má dárek vložím dárek
          $gifts = $products->getGifts($product);
          if (!empty($gifts)) {
            foreach ($gifts as $grow) {
              if ($grow) {
                $gifori = array();
                $gifori['orioriid'] = $oriid;
                $gifori['oriordid'] = $ordid;
                $gifori['oriproid'] = $grow->proid;
                $gifori['oriprocode'] = $grow->procode;
                $gifori['oriprocode2'] = $grow->procode2;
                $gifori['oritypid'] = 0;
                $gifori['oriname'] = $grow->proname;
                $gifori['oriprice'] = 0;
                $gifori['oripriceoriginal'] = 0;
                $gifori['orivatid'] = $grow->provatid;
                $gifori['oricredit'] = $grow->procredit;
                $gifori['oriqty'] = $formVals['newitem']["oriqty"];
                $gifori['oriprobigsize'] = $grow->probigsize;
                $gifori['oriprooffer'] = $grow->prooffer;
                $ordItems->insert($gifori);
              }
            }
          }

        } else {
          $this->flashMessage("Nelze vložit položku, která není v databázi. Id není vyplněno.", "danger");
          $this->redirect('edit', $ordid);
        }
      }
      $orders->recalcOrder($ordid);

      $this->flashMessage("Položky objednávky byly aktualizovány, celková cena objednávky byla prepočítána.");
    }
    $this->redirect('edit#edititems', $ordid);
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sName = Null;
        $this->sAdmin = Null;
        $this->sStatus = Null;
        $this->sCoupon = Null;
        $this->sDateFrom = Null;
        $this->sDateTo = Null;
        $this->sNotClosed = Null;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sName = $vals["name"];
        $this->sAdmin = $vals["admin"];
        $this->sStatus = $vals["status"];
        $this->sCoupon = $vals["coupon"];
        $this->sDateFrom = $vals["datefrom"];
        $this->sDateTo = $vals["dateto"];
        $this->sNotClosed = $vals["notclosed"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("Order:default");
  }

  protected function createComponentSearchForm() {
    $orders = new \Model\OrdersModel();
    $catalogs = new \Model\CatalogsModel();
    $admins = new \Model\AdminsModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Kód objednávky", 10)
      ->setDefaultValue($this->sCode);
    $form->addText("name", "Příjmení nebo název firmy", 10)
      ->setDefaultValue($this->sName);
    $form->addSelect("admin", "Obchodník", $admins->getEnumAdmins())
      ->setPrompt('')
      ->setDefaultValue($this->sAdmin);
    $form->addSelect("status", "Stav", $orders->getEnumOrdStatus())
      ->setPrompt('');
    if (!empty($this->sStatus)) $form["status"]->setDefaultValue($this->sStatus);

    $form->addText("coupon", "Slevový kupón", 10)
      ->setDefaultValue($this->sCoupon);

    $form->addText("datefrom", "Datum od", 10)
      ->setDefaultValue($this->sDateFrom);

    $form->addText("dateto", "Datum do", 10)
      ->setDefaultValue($this->sDateTo);

    $form->addCheckbox("notclosed", "Neuzavřené")
      ->setDefaultValue($this->sNotClosed);

    $arr = array(
      'orddatec'=>'Data vytvoření',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  protected function createComponentOrderQuickForm() {
    $order = new \Model\OrdersModel();
    $enums = new \Model\EnumcatsModel();
    $admins = new \Model\AdminsModel();
    $form = $this->createAppForm();

    $form->addHidden("ordadmid", $this->adminData->admid);

    $form->addText("ordiname", "Jméno:", 60)
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");
    $form->addText("ordilname", "Přijmení:", 60)
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addselect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId())
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    if ($this->secondCurrency) {
      $form->addselect("ordcurid", "Měna:", $this->getEnumCurr())
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněná")
        ->setPrompt('Vyberte ...');
    } else {
      $form->addHidden("ordcurid", 1);
    }

    $form->addText("ordtel", "Telefon:", 30);
    $form->addText("ordmail", "Email:", 30)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addSubmit('makeorder', 'Vytvořit objednávku a upravit položky');
    $form->onSuccess[] = array($this, 'orderQuickFormSubmitted');
    return $form;
  }

  public function orderQuickFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $values = $form->getValues();
      $orders = new \Model\OrdersModel();
      $dels = new \Model\DeliveryModesModel();
      $dels->setCurrency($this->currencies, $values["ordcurid"]);
      $orders->setCurrency($this->currencies, $values["ordcurid"]);
      $id = $orders->insert($values);

      //vlozim dopravu
      //nactu si zpusob dopravy

      $paymode = $dels->load($values["orddelid"]);
      $delmode = $dels->load($paymode->delmasid);
      $delValues['oriordid'] = $id;
      $delValues['oritypid'] = 1;
      $delValues['oriproid'] = 0;
      $delValues['oriname'] = "Doprava: ".$delmode->delname." - ".$paymode->delname;
      $delValues['oriprice'] = $paymode->delprice;
      $delValues['orivatid'] = Null;
      $delValues['oricredit'] = 0;
      $delValues['oriqty'] = 1;
      $delValues['oriprobigsize'] = 0;
      $delValues['oriprooffer'] = 0;
      $orditems = new \Model\OrdItemsModel();
      $orditems->insert($delValues);
      $orders->recalcOrder($id);
      $this->flashMessage('Uloženo v pořádku');
      $this->redirect('edit#edititems', $id);
    }
  }

  protected function createComponentSearchStatsForm() {

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");

    $form->addText("datefrom", "Datum od", 10)
      ->setDefaultValue($this->sDateFrom);

    $form->addText("dateto", "Datum do", 10)
      ->setDefaultValue($this->sDateTo);

    $arr = [
      'all' => 'vše',
      'closed' => 'uzavřené',
      'storno' => 'stornované',
    ];
    $form->addSelect("statstat", "Stav", $arr);
    if (!empty($this->sStatStat)) {
      $form["statstat"]->setDefaultValue($this->sStatStat);
    }

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchStatsFormSubmitted');
    return $form;
  }

  public function searchStatsFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sStatStat = NULL;
        $this->sDateFrom = NULL;
        $this->sDateTo = NULL;
      } else {
        $vals = $form->getValues();
        $this->sStatStat = $vals["statstat"];
        $this->sDateFrom = $vals["datefrom"];
        $this->sDateTo = $vals["dateto"];
      }
    }
    $this->redirect("this");
  }
}
