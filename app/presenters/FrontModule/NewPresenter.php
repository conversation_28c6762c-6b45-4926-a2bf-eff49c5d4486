<?php
namespace FrontModule;
use dibi;
use Nette;

final class NewPresenter extends BasePresenter {

  public function renderDetail($id, $key) {
    $news = new \Model\NewsModel();
    $new = $news->load($id);
    if ($new === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    } else{
      //kontrola platnosti URL
      $urlkey = Nette\Utils\Strings::webalize($new->newtitle);

      //pokud se zmenil klic presmeruju na novy
      if ($key != $urlkey) $this->redirect(301, 'New:detail', array('id'=>$id, 'key'=>$urlkey));
      $this->template->new = $new;
    }
  }

  public function renderDefault() {
    $news = new \Model\NewsModel();
    $dataSource = $news->getDataSource("SELECT * FROM news ORDER BY newdate DESC");

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = 20;
    $paginator->itemCount = $dataSource->count();
    $this->template->rows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
  }
}
