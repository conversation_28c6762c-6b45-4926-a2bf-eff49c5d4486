Files changed
-------------
mpdf.php
classes/cssmgr.php
config.php  (added 'main' to $this->allowedCSStags and $this->outerblocktags)


Bug Fixes & Minor Additions
---------------------------
- Tables - cellSpacing and cellPadding taking preference over CSS stylesheet
- Tables - background images in table inside HTML Footer incorrectly positioned
- Tables - cell in a nested table with a specified width, should determine width of parent table cell
	(cf. http://www.mpdf1.com/forum/discussion/1648/nested-table-bug-)
- Tables - colspan (on a row after first row) exceeds number of columns in table
- Gradients in Imported documents (mPDFI) causing error in some browsers
- Fatal error after page-break-after:always on root level block element
- Support for 'https/SSL' if file_get_contents_by_socket required (e.g. getting images with allow_url_fopen turned off)
- Improved support for specified ports when getting external CSS stylesheets e.g. www.domain.com:80
- error accessing local .css files with dummy queries (cache-busting) e.g. mpdfstyleA4.css?v=2.0.18.9
- start of end tag in PRE incorrectly changed to &lt;
- error thrown when open.basedir restriction in effect (deleting temporary files)
- image which forces pagebreak incorrectly positioned at top of page
- [changes to avoid warning notices by checking if (isset(x)) before referencing it]
- text with letter-spacing set inside table which needs to be resixed (shrunk) - letter-spacing was not adjusted
- nested table incorrectly calculating width and unnecessarily wrapping text
- vertical-align:super|sub can be nested using <span> elements
- inline elements can be nested e.g. text <sup>text<sup>13</sup>text</sup> text
- CSS vertical-align:0.5em (or %) now supported
- underline and strikethrough now use the parent inline block baseline/fontsize/color for child inline elements *** change in behaviour
	(Adjusts line height to take account of superscript and subscript except in tables)
- nested table incorrectly calculating width and unnecessarily wrapping text
- tables - font size carrying over from one nested table to the next nested table
- tables - border set as attribute on <TABLE> overrides border set as CSS on <TD>
- tables - if table width set to 100% and one cell/column is empty with no padding/border, sizing incorrectly
	(http://www.mpdf1.com/forum/discussion/1886/td-fontsize-in-nested-table-bug-#Item_5)
- <main> added as recognised tag 
- CSS style transform supported on <img> element (only)
	All transform functions are supported except matrix() i.e. translate(), translateX(), translateY(), skew(), skewX(), skewY(),
	scale(), scaleX(), scaleY(), rotate()
	NB When using Columns or Keep-with-table (use_kwt), cannot use transform
- CSS background-color now supported on <img> element
- @page :first not recognised unless @page {} has styles set
- left/right margins not allowed on @page :first
