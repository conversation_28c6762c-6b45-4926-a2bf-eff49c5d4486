{*<!-- product menu start -->*}
<nav class="nav-product{if !$showMenuLeft} nav-product--hidden{/if}" role="navigation">

  {php
    $mans = array();
    if ($presenter->name == "Front:Manufacturer" && $presenter->action == 'detail') {
      $manid = (int)$presenter->getParam('id');
      if ($manid > 0) $mans[$manid] = $manid;
    }
  }

  <div class="nav-product--switch">

    <div class="nav-product__menu">

    {default $thisCatId=0}
    {default $rootCatId=0}

    {define #menuCatalog}
      {foreach $items as $item}
        {if $iterator->isFirst()}
          <ul>
        {/if}
        <li class="{if $iterator->isLast()}last{/if}{if $thisCatId == $item->catid || $rootCatId == $item->catid}is-active{/if}">
          {if $thisCatId == $item->catid}
            <a href="{plink Catalog:detail, $item->catid, ($item|getCatKey)}"><strong>{$item->catname}</strong></i></a>
          {else}
            <a href="{plink Catalog:detail, $item->catid, ($item|getCatKey)}">{$item->catname}</i></a>
          {/if}

          {if !empty($menuCatalogSubItems[$item->catid])}
            {include #menuCatalog 'items'=>$menuCatalogSubItems[$item->catid]}{/if}

        </li>
        {if $iterator->isLast()}
          </ul>
        {/if}
      {/foreach}
    {/define}

    {include #menuCatalog 'items'=>$menuCatalog}

    </div>

  </div>

  {ifset $menuShopInformation}
  <div class="nav-product__other">

      {foreach $menuShopInformation as $row}
        {if $iterator->isFirst()}
        <ul>
        {/if}
          <li><a href="{plink Page:detail, $row->pagid, ($row->pagurlkey|getUrlKey:$row->pagname)}">{$row->menname}</i></a></li>
        {if $iterator->isLast()}
        </ul>
        {/if}
      {/foreach}
  </div>
  {/ifset}

</nav>
{*<!-- product menu end -->*}
