<script>
    function noteSH(id) {
        if ($('#'+id).is(':visible')) {
            $('#'+id).hide();
        } else {
            $('#'+id).show();
        }
        return false;
    }
</script>
<form method="get" action="{plink Order:batchAction}">
 {*<input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše*}
<table class="table table-condensed table-hover table-bordered">
  <tr>
    {*<th> </th>*}
    <th>Č. obj.</th>
    <th>Zákazník</th>
    <th>Firma</th>
    <th>Datum</th>
    <th>Cena s DPH</th>
    <th>Doprava, platba</th>
    <th>Pozn.</th>
    <th>Status</th>
    <th colspan="3"></th>
  </tr>
  {php
    $colors[0] = "#FFFFFF";
    $colors[1] = "#FFFFC0";
    $colors[2] = "#FFBE7D";
    $colors[3] = "#C0C0FF";
    $colors[4] = "#C0FFC0";
    $colors[5] = "#FFC0C0";
    $colors[6] = "#800080";
    $colors[7] = "#C0C0C0";
    $colors[8] = "#BCDCB8";
    $colors[9] = "#C0C0FF";

    $sum = 0;
  }

  {foreach $dataRows as $row}

  {php
    $sum += $row->ordpricevat;
  }
  {var $style= (!$iterator->isOdd() ? 'bgcolor="#D0D0D0"' : '')}

  <tr {$style}>
    {*<td><input class="ordid_chk" type="checkbox" name="ordid[{$row->ordid}]" value="{$row->ordid}" ></td>*}
    <td>{$row->ordcode}</td>
    <td>{$row->ordiname} {$row->ordilname}|{$row->ordstname} {$row->ordstlname}</td>
    <td>{$row->ordifirname}|{$row->ordstfirname}</td>
    <td>{$row->orddatec|date:'d.m.Y H:i'}</td>
    <td style="text-align: right;white-space:nowrap">{$row->ordpricevat|formatPriceByCurId:$row->ordcurid}</td>
    <td>
      {$row->delnamemas}, {$row->delname}
      {if $row->delcode === 'OSOBNE' && $row->ordpaystatus == 1 && !empty($row->ordseccode)}
        <br>Kód: <strong style="color: #9f2b1e">{$row->ordseccode}</strong>
      {/if}
    </td>
    <td>{$row->ordnote|nl2br|noescape}</td>
    <td style="color: black; background-color: {$colors[$row->ordstatus]|noescape};">
    {$enum_ordstatus[$row->ordstatus]}
    </td>
    <td><a href="{plink Order:edit, $row->ordid}">{('edit'|glyph)|noescape}</a></td>
    <td>{if $row->ordusrid > 0}<a href="{plink User:edit, $row->ordusrid}">{('user'|glyph)|noescape}</a>{/if}</td>
  </tr>
  {/foreach}


  <tr>
    <td colspan="6" ><strong>Celkem:</strong></td>
    <td style="text-align: right;"><strong>{$sum|formatPrice}</strong></td>
    <td colspan="9" ></td>
  </tr>
  </table>
  {*<input type="submit" class="btn btn-primary btn-sm" name="change_status" value="Aktualizovat status">*}
  </form>
  <script type="">

  {*
  $("#checkAll").click(function(){
    $('.ordid_chk').not(this).prop('checked', this.checked);
  });
  *}
  </script>
