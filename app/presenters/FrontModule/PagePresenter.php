<?php
namespace FrontModule;
use dibi;
use Nette;

final class PagePresenter extends BasePresenter {
  /********************* view default *********************/

  public function renderDetail($id, $key) {
    $pages = new \Model\PagesModel();
    $articles = new \Model\ArticlesModel();
    $news = new \Model\NewsModel();
    $pageData = $pages->load($id);
    if ($pageData) {
      if ($pageData->pagid > 0) {
        $this->template->urlkey = $pageData->pagurlkey;
        $this->template->page = $pageData;
      }
    } else {
      throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    }

    if ($pageData->pagblock == 1) throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');

    //pokud nejaky specialni typ doplnim potrebna data nastavim sablonu
    switch ($pageData->pagtypid) {
      case 1:
        //kontaktni form
        $this->setView('contact');
        break;
      case 4:
        //prodejna
        $products = array();

        $product = new \Model\ProductsModel();
        $product->setPrcCat($this->userData->usrprccat);
        $product->setCurrency($this->currencies, $this->curId);
        $proList = array();
        $proList[] = $pageData->pagtext1;
        $proList[] = $pageData->pagtext2;
        $proList[] = $pageData->pagtext3;
        $proList[] = $pageData->pagtext4;
        $proList[] = $pageData->pagtext5;
        $proList[] = $pageData->pagtext6;
        $cnt = 0;
        foreach ($proList as $val) {
          $arr = explode("\n", $val);
          $proid = (int)trim($arr[0]);
          if (!empty($proid)) {
            $item = $product->load($proid);
            if ($item) {
              $item->storename = (string)trim($arr[1]);
              $item->storeprice = (double)trim($arr[2]);
              $item->storedesc = (string)trim($arr[3]);
              $products[] = $item;
              $cnt ++;
            }
          }
          if ($cnt >= 6) break;
        }



        $this->template->products = $products;
        $this->template->showMenuLeft = FALSE;
        $this->setView('store');
        break;
    }
    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $pageData->pagid);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype IN ('jpg', 'png', 'gif')", $pageData->pagid);
  }
}
