{var $title = 'Objednávka č. '.$dataRow->ordcode.' '.$dataRow->ordiname.' '.$dataRow->ordilname}

{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('oriname', 'oriproid');
          $("#"+newId).val(ui.item.id);
          newId = id.replace('oriname', 'oriprice');
          $("#"+newId).val(ui.item.price);
        }
        return false;
      }
    });
  });
  </script>

  {if !empty($dataRow->ordinvcode)}
  <p><b>Faktura č.: </b>{$dataRow->ordinvcode} <a target="invoicePrint" href="{plink Order:printInvoice, $dataRow->ordid}"><img src="{$baseUri}/admin/ico/pdf.png" width="16" height="16" /></a>  | <a href="{plink Order:printInvoice, $dataRow->ordid, 'D'}">{('export'|glyph)|noescape}</a></p>
  {else}
  <p>Není přiřazen kód faktury. <a href="{plink makeInvoice, $dataRow->ordid}">Vystavit fakturu</a></p>
  {/if}
  {if $ordPayType->delcode == 'creditcard'}
    <p>
    {if $dataRow->ordpaystatus == 1}
    {else}
      <a href="{plink //:Front:Payment:openGate $dataRow->ordid.substr(md5($dataRow->ordid.$dataRow->orddatec->getTimestamp()), 0, 8)}">Zaplatit GP Webpay</a>
    {/if}

    </p>
  {/if}
{*<p><a href="{plink exportOrderXml, $dataRow->ordid}"> Exportovat objednávku do XML </a></p>*}

  {if $ordDelType->delcode === 'OSOBNE' && $dataRow->ordpaystatus == 1 && !empty($dataRow->ordseccode)}
    <p>Ověřovací kód pro uhrazenou objednávku: <strong style="color: #9f2b1e">{$dataRow->ordseccode}</strong></p>
  {/if}
  {control orderChangeStateForm}

  <h4 id="edititems">Položky objednávky</h4>
  {form ordItemsEditForm}
    <table class="table table-condensed table-hover table-bordered">
      <tr>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        <th>cena/sleva</th>
        <th>počet</th>
        <th colspan="2"></th>
      </tr>
      {foreach  $form['items']->getComponents() as $cont}
      {var $oriid=$form['items'][$cont->name]['oriid']->value}
      {if $cont->name != 'delivery'}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>{php echo $form['items'][$cont->name]['oriproid']->control }</td>
        <td>{$ordItems[$oriid]->oriprocode}</td>
        <td>
        {php echo $form['items'][$cont->name]['oriname']->control->size(80) }<br />
        SN: {php echo $form['items'][$cont->name]['sn']->control->size(20) } {$ordItems[$oriid]->orisn}
        </td>
        <td style="text-align: center">{php echo $form['items'][$cont->name]['oriprice']->control }<br />{if $ordItems[$oriid]->oripriceoriginal != $ordItems[$oriid]->oriprice}Původní cena:<br>{$ordItems[$oriid]->oripriceoriginal|formatPrice}{/if}</td>
        <td>{php echo $form['items'][$cont->name]['oriqty']->control }</td>
        <td>
          <a href="{plink :Front:Product:detail, $ordItems[$oriid]->oriproid, ($ordItems[$oriid]|getProKey)}">{('front'|glyph)|noescape}</a>
          <a href="{plink Product:edit, $ordItems[$oriid]->oriproid}">{('edit'|glyph)|noescape}</a>
        </td>
        <td><a href="{plink Order:deleteItem, $form['items'][$cont->name]['oriid']->value, (int)$presenter->getParam('id')}" onclick="return DeleteConfirm('položku objednávky {$form['items'][$cont->name]['oriname']->value|noescape}');"> {('delete'|glyph)|noescape} SMAZAT </a></td>
      </tr>
      {else}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>poštovné</td>
        <td></td>
        <td>{php echo $form['items'][$cont->name]['oriname']->control->size(80) } </td>
        <td>
          Cena: {$ordItems[$oriid]->oriprice}<br />
          {php echo $form['items'][$cont->name]['oripricemaster']->control->size(3) }
        </td>
        <td>1</td>
        <td colspan="2"></td>
      </tr>
      {/if}
      {/foreach}
      {if $ordItemDisc}
      <tr>
        <th colspan="10">Sleva</th>
      </tr>
      <tr>
        <td></td>
        <td></td>
        <td>{$ordItemDisc->oriname}</td>
        <td>{$ordItemDisc->oriprice}</td>
        <td>1</td>
        <td></td>
        <td colspan="2"></td>
      </tr>
      {/if}
      <tr>
        <th colspan="7">nová položka</th>
      </tr>
      <tr>
        <td>{php echo $form['newitem']['oriproid']->control }</td>
        <td></td>
        <td>{php echo $form['newitem']['oriname']->control->size(80) } </td>
        <td>{php echo $form['newitem']['oriprice']->control }</td>
        <td>{php echo $form['newitem']['oriqty']->control }</td>
        <td colspan="2"></td>
      </tr>
      <tr>
        <td colspan="8">{input saveitems}</td>
      </tr>
    </table>
  {/form}

  {control orderEditForm}

  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie objednávky</h3>
    <table class="table table-condensed table-hover table-bordered">
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_ordstatus[$row->orlstatus]}</td>
    <td>{$row->orladmname}</td>
    <td>{$row->orlnote}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  {foreach $eetRows as $row}
    {if $iterator->isFirst()}
    <h3>EET log</h3>
    <table class="table table-condensed table-hover table-bordered">
      <th>Datum</th>
      <th>Provozovna</th>
      <th>Pokladna</th>
      <th>FIK</th>
      <th>BKP</th>
    {/if}
    <tr>
    <td>{$row->logdatec|date:'d.m.Y H:i:s'}</td>
    <td>{$row->logprovozid}</td>
    <td>{$row->logpoklid}</td>
    <td>{$row->logfik}</td>
    <td>{$row->logbkp}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  {/block}
