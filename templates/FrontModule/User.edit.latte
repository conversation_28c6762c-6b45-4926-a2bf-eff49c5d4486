{$pageTitle  = 'Upravit osobní údaje'}
{$pageRobots = "nofollow,noindex"}

{block #content}
<div class="user">

  <div class="user__menu">{include @userMenu.latte}</div>

  {form userEditForm}

    {* vypise chyby formulare *}
    {include ../@formErrors.latte form=>$form}
  <fieldset>

  <legend>{_'Věkový limit'}</legend>
  <p>{input usragelimit}</p>
  <div id="agelimit" class="visible:none">
    <h3>Zákonný zástupce u osoby mladší 16 let</h3>
    <p>{label usrreprename /} *{input usrreprename}</p>
    <p>{label usrrepreaddress /} *{input usrrepreaddress}</p>
    <p>{label usrrepremail /} *{input usrrepremail}</p>
    <p>{label usrrepretel /} *{input usrrepretel}</p>
  </div>
  </fieldset>

  <fieldset>

    <legend>{_'Přihlašovací údaje'}</legend>

    <p>
      {php echo $form['usrmail']->getLabel() } (<small>{_'slouž<PERSON> zároveň jako přihlašovací jméno'}</small>)<br>
      {php echo $form['usrmail']->control->cols(15) }
    </p>
    <p>
      {php echo $form['usrmaillist']->control }{php echo $form['usrmaillist']->getLabel() }
    </p>

  </fieldset>

  <fieldset>

    <legend>{_'Fakturační a současně doručovací adresa'}</legend>

    <p>
      {php echo $form['usriname']->getLabel() }<br>
      {php echo $form['usriname']->control->size(60) }
    </p>
    <p>
      {php echo $form['usrilname']->getLabel() }<br>
      {php echo $form['usrilname']->control->size(60) }
    </p>

    {if $lang=='cs'}
    <p>
      {php echo $form['usrifirname']->getLabel() }<br>
      {php echo $form['usrifirname']->control->size(60) }
    </p>
    {/if}

    <p>
      {php echo $form['usristreet']->getLabel() }<br>
      {php echo $form['usristreet']->control->size(60) }
    </p>
    <p>
      {php echo $form['usristreetno']->getLabel() }<br>
      {php echo $form['usristreetno']->control->size(16) }
    </p>
    <p>
      {php echo $form['usricity']->getLabel() }<br>
      {php echo $form['usricity']->control->size(60) }
    </p>
    <p>
      {php echo $form['usripostcode']->getLabel() }<br>
      {php echo $form['usripostcode']->control->size(16) }
    </p>
    <p>
      {php echo $form['usrtel']->getLabel() }<br>
      {php echo $form['usrtel']->control->cols(30) }
    </p>

    {if $lang=='cs'}
    <p>
      {php echo $form['usric']->getLabel() }<br>
      {php echo $form['usric']->control->size(30) }
    </p>
    <p>
      {php echo $form['usrdic']->getLabel() }<br>
      {php echo $form['usrdic']->control->size(30) }
    </p>
    {/if}

  </fieldset>

  <fieldset>

    <legend>{_'Adresa dodání'}</legend>

    <p>
      {php echo $form['stadr']->control }
      {php echo $form['stadr']->getLabel() }
    </p>
    <p>
      {php echo $form['usrstname']->getLabel() }<br>
      {php echo $form['usrstname']->control->size(60) }
    </p>
    <p>
      {php echo $form['usrstlname']->getLabel() }<br>
      {php echo $form['usrstlname']->control->size(60) }
    </p>

    {if $lang=='cs'}
    <p>
      {php echo $form['usrstfirname']->getLabel() }<br>
      {php echo $form['usrstfirname']->control->size(60) }
    </p>
    {/if}
    <p>
      {php echo $form['usrststreet']->getLabel() }<br>
      {php echo $form['usrststreet']->control->size(60) }
    </p>
    <p>
      {php echo $form['usrststreetno']->getLabel() }<br>
      {php echo $form['usrststreetno']->control->size(16) }
    </p>
    <p>
      {php echo $form['usrstcity']->getLabel() }<br>
      {php echo $form['usrstcity']->control->size(60) }
    </p>
    <p>
      {php echo $form['usrstpostcode']->getLabel() }<br>
      {php echo $form['usrstpostcode']->control->size(16) }
    </p>
  </fieldset>

  <fieldset>

    <legend>{_'Změna hesla'}</legend>

    <p>
      {php echo $form['usrpassw_old']->getLabel() } (<small>{_'vyplňte jen pokud chcete heslo změnit'}</small>)<br>
      {php echo $form['usrpassw_old']->control->size(30) }
    </p>

    <p>
      {php echo $form['usrpassw']->getLabel() } (<small>{_'minimálně 6 znaků'}</small>)<br>
      {php echo $form['usrpassw']->control->size(30) }
    </p>

    <p>
      {php echo $form['usrpassw2']->getLabel() } (<small>{_'minimálně 6 znaků, podruhé pro kontrolu'}</small>)<br>
      {php echo $form['usrpassw2']->control->size(30) }
    </p>

  </fieldset>

  <p>
    {php echo $form['save']->getControl()->value('Uložit všechny údaje') }
  </p>

  {/form}

  <h2>Zrušení registrace</h2>
  <p>Pro zrušení registrace vyplňte Vaše platné heslo a kliněte na tlačítko "Zrušit registraci". Vaše registrace bude vymazána.</p>
  {form userDeleteForm}

    {* vypise chyby formulare *}
    {include ../@formErrors.latte form=>$form}

    <p>
      {php echo $form['usrpassw']->getLabel() }<br>
      {php echo $form['usrpassw']->control->size(30) }
    </p>

    <p>
     {php echo $form['submit']->getControl()->value('Zrušit registraci') }
    </p>
  {/form}
</div>
{/block}
