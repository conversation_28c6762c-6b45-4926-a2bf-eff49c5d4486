<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  <PERSON>;

final class AdminPresenter extends BasePresenter {

  public function renderLogin($backlink) {

  }

  public function actionLogout() {
    $this->user->logout();
    $this->flashMessage("Odhlášení proběhl<PERSON>.");
    $this->redirect('Admin:login');
  }

  public function loginFormSubmitted($form) {
    try {
      $this->user->login($form['admmail']->getValue(), $form['admpassw']->getValue(), self::LOGIN_NAMESPACE);
      $this->restoreRequest($this->backlink);
      $this->redirect('Admin:default');

    } catch (Nette\Security\AuthenticationException $e) {
      $form->addError($e->getMessage());
    }
  }



  /********************* view default *********************/

  public function renderDefault() {
    //seznam aktualnich objednavek
    $orders = new \Model\OrdersModel();
    $dataRows = $orders->fetchAll("
    SELECT orders.*, d.delname AS delname, dm.delname AS delnamemas, dm.delcode AS delcode, admname
    FROM orders
    LEFT JOIN deliverymodes AS d ON (orddelid=d.delid)
    LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
    LEFT JOIN admins ON (ordadmid=admid)
    WHERE ordstatus NOT IN (4,5,7,10)
    ORDER BY orddatec ASC");
    $this->template->dataRows = $dataRows;

    //ciselnik statusu
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }

  /********************* view add, edit *********************/

  public function renderEditSelf() {
    $this->renderEdit($this->user->id);
  }

  public function renderEdit($id) {
    if ($this->adminData->admrole!='admin' && $this->adminData->admid != $id) {
      $this->flashMessage("Nemáte oprávnění editovat cizí účty a vytvářet nové.", 'danger');
      $this->redirect('list');
    }

    $form = $this['adminForm'];

    if (!$form->isSubmitted() && $id > 0) {
      $admin = new \Model\AdminsModel();
      $row = $admin->load($id);
      if (!$row) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($row);
    }
  }

  public function renderList($status=0) {
    $admin = new \Model\AdminsModel();
    $this->template->status = $status;
    $this->template->dataRows = dibi::fetchAll("SELECT * FROM admins where admstatus=$status ORDER BY admname");
    $this->template->enum_admstatus = $admin->getEnumAdmStatus();
  }

  public function adminFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVars = $form->getValues();

      $id = (int) $this->getParameter('id');
      $admin = new \Model\AdminsModel();

      if ($id > 0) {
        $admRow = $admin->load($id);
        //kontrola pokud chce zmenit heslo
        $passw_changed = false;
        if (!empty($formVars["admpassw"]) && $this->user->isAllowed('Admin:Admin', 'changeForeign')) {
          $formVars["admpassw"] = $this->passwordHash($formVars["admpassw"]);
          $passw_changed = true;
        } else if ($formVars["admpassw_old"] != "") {
          if (!$this->passwordVerify($formVars["admpassw_old"], $admRow["admpassw"])) {
            $this->flashMessage("Původní heslo jste nevyplnil/a správně. Heslo nebylo změněno", "danger");
            unset($formVars["admpassw"]);
          } else {
            $formVars["admpassw"] = $this->passwordHash($formVars["admpassw"]);
            $passw_changed = true;
          }
        } else {
          unset($formVars["admpassw"]);
        }
      } else {
        $formVars["admpassw"] = $this->passwordHash($formVars["admpassw"]);
      }

      unset($formVars["admpassw_old"]);
      unset($formVars["admpassw2"]);

      if ($id > 0) {
        $admin->update($id, $formVars);
        $this->flashMessage('Údaje byly aktualizovány.');
      } else {
        $id = $admin->insert($formVars);
        $this->flashMessage('Účet byl vytvořen.');
      }
    }
    $this->redirect('edit', $id);
  }

  /********************* facilities *********************/

  /**
   * Component factory.
   * @param  string  component name
   * @return void
   */
  protected function createComponentAdminLoginForm() {
    $form = $this->createAppForm();
    $form->addText('admmail', 'Přihlašovací jméno:')
      ->setAttribute('autofocus')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše přihlašovací jméno (email).');

    $form->addPassword('admpassw', 'Heslo:')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');

    $form->addSubmit('login', 'Přihlásit se');
    $form->onSuccess[] = array($this, 'loginFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  protected function createComponentAdminForm() {
    $id = $this->getParameter('id');

    $admin = new \Model\AdminsModel();
    $form = $this->createAppForm();
    $form->addText('admname', 'Jméno:', 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Jméno.');

    $form->addText('admmail', 'Login:', 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Login.');
    if (empty($id)) {
      $form->addPassword('admpassw', 'Heslo:')
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo.');
      $form->addPassword('admpassw2', 'Heslo podruhé:')
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["admpassw"]);
    } else if ($id == $this->adminData->admid || $this->user->isAllowed('Admin:Admin', 'changeForeign')) {
      $form->addPassword('admpassw_old', 'Původní heslo:');
      $form->addPassword('admpassw', 'Nové heslo:');
      $form->addPassword('admpassw2', 'Heslo podruhé:')
        ->addConditionOn($form["admpassw"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte heslo podruhé.')
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::EQUAL, 'Heslo podruhé musí být vyplněno stejně jako Heslo', $form["admpassw"]);
    }

    if ($this->user->isAllowed('Admin:Admin', 'changeForeign')) {
      $roles = $this->user->getAuthorizator()->getRoles();
      $selArr = array();
      foreach ($roles as $role) {
        $selArr[$role] = $role;
      }
      $form->addSelect('admrole', 'Role', $selArr);
    }

    if ($this->user->isAllowed('Admin:Admin', 'changeForeign')) {
      $form->addSelect('admstatus', 'Status', $admin->getEnumAdmStatus());
    }


    $form->addSubmit('save', 'Uložit');
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = [$this, 'adminFormSubmitted'];

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }
}
