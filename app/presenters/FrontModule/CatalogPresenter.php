<?php
namespace FrontModule;
use dibi;
use Nette;

final class CatalogPresenter extends BasePresenter {
  /** @persistent */
  public $t = array(); //typy

  /** @persistent */
  public $m = array(); //vyrobce

  /** @persistent */
  public $pF = Null; //cena od

  /** @persistent */
  public $pT = Null; //cena do

  /** @persistent */
  public $f = array(); //forma

  /** @persistent */
  public $o = ''; //řazeni

  public $productsRows = array();


  public function renderDetailOld($id, $key) {
    $catalog = new \Model\CatalogsModel();

    //aktualni polozka katalogu
    $catalogData = $catalog->load($id);
    if ($catalogData === false) {
      throw new Nette\Application\BadRequestException('Katalog nenalezen', '404');
    } else{
      //kontrola platnosti URL
      $urlkey = (!empty($catalogData->catkey) ? $catalogData->catkey : Nette\Utils\Strings::webalize($catalogData->catname));
      //presmeruju na novy
      $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key'=>$urlkey));
    }
  }

  public function renderDetail($id, $key) {
    $this->o = $this->getParameter('o');
    $this->t = $this->getParameter('t');
    $this->pF = $this->getParameter('pF');
    $this->pT = $this->getParameter('pT');
    $u = (int)$this->getParameter('ut');
    if ($u > 0) {
      unset($this->t[$u]);
      $this->redirect('this');
    }

    $this->m = $this->getParameter('m');
    $u = (int)$this->getParameter('um');
    if ($u > 0) {
      unset($this->m[$u]);
      $this->redirect('this');
    }

    $catalog = new \Model\CatalogsModel();

    //aktualni polozka katalogu
    $catalogData = $catalog->load($id);

    //zjistim jestli budu spojovat nazvy kategorii
    $this->template->catNameFull = $catalogData->catname;
    if ($catalogData->catlevel == 3) {
      //zjistim nadrizenou kategorii
      $catMasName = (string)dibi::fetchSingle("SELECT catname FROM catalogs WHERE catid=%i", $catalogData->catmasid);
      If (!empty($catMasName)) $this->template->catNameFull = $catMasName." - ".$catalogData->catname;
    }

    if ($catalogData === false) {
      throw new Nette\Application\BadRequestException('Katalog nenalezen', '404');
    } else{
      //kontrola platnosti URL
      $urlkey = (!empty($catalogData->catkey) ? $catalogData->catkey : Nette\Utils\Strings::webalize($catalogData->catname));
      //pokud se zmenil klic presmeruju na novy
      if ($key != $urlkey) $this->redirect(301, 'Catalog:detail', array('id'=>$id, 'key'=>$urlkey));

      $this->template->catalogData = $catalogData;
    }

    //id aktualni kategorie
    $this->template->thisCatId = $catalogData->catid;
    $idPath = explode('|', trim($catalogData->catpathids, '|'));
    $rootCatId = 0;
    if (isset($idPath[1])) $rootCatId = (int)$idPath[0];
    $this->template->masterCatId = $idPath[0];
    $catalogPath = array();
    $breadcrumbs = array();
    foreach ($idPath as $catid) {
      $row = dibi::fetch("SELECT * from catalogs WHERE catid=$catid");
      $breadcrumbs[] = array(
        "url" => $this->link("Catalog:detail", $row->catid, (!empty($row->catkey) ? $row->catkey : Nette\Utils\Strings::webalize($row->catname))),
        "text" => $row->catname
      );
      $catalogPath[$catid] = $row;
    }
    $this->template->breadcrumbs = $breadcrumbs;

    $this->template->catalogPath = $catalogPath;
    $this->template->rootCatId = $rootCatId;
    if (isset($catalogPath[$rootCatId])) $this->template->rootCatalog = $catalogPath[$rootCatId];

    //k aktualni kategorii načtu podkategorie
    $this->template->thisCatId = $catalogData->catid;
    //podrizene kategorie
    $catalogSubItems = dibi::query("SELECT * FROM catalogs WHERE catmasid=%i", $catalogData->catid, " AND catstatus=0 ORDER BY catorder")->fetchAssoc("catid");
    $this->template->catalogSubItems = $catalogSubItems;
    $menuCatalogSubItems = array();
    If ($catalogData->catlevel == 2) {
      $menuCatalogSubItems[$catalogData->catmasid] = dibi::fetchAll("SELECT * FROM catalogs WHERE catmasid=%i", $catalogData->catmasid, " AND catstatus=0 ORDER BY catorder");
    } elseIf ($catalogData->catlevel == 1) {
      $menuCatalogSubItems[$catalogData->catid] = $catalogSubItems;
    }
    $this->template->menuCatalogSubItems = $menuCatalogSubItems;
    //naplnim si do katalogu prislusne zbozi
    $product = new \Model\ProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);
    //sestavim WHERE
    switch ($this->o) {
       case '':
         $orderBy = "proorder";
         break;
       case 'os':
         $orderBy = " proaccess ASC";
         break;
      case 'na':
        $orderBy = " proname ASC";
        break;
       case 'nd':
         $orderBy = " proname DESC";
         break;
       case 'pa':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) ASC";
         break;
       case 'pd':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) DESC";
         break;
    }
    //nactu do filtru podminky
    $where = "prostatus=0";
    //vyrobce
    $manwhere = "";
    //vyrobci
    if (count($this->m) > 0) $manwhere = implode(',', $this->m);
    //nefiltrovat podle vyrobce pokud prislusenstvi
    if (!empty($manwhere)) $where = $where." AND promanid IN (".$manwhere.")";

    //typy
    if (count($this->t) > 0) {
      $tWhere = "";
      foreach ($this->t as $key => $value) {
        switch ($key) {
          case 'sk': //Skladem
            $tWhere = $tWhere."proaccess=0 OR ";
            break;
          case 'no': //Novinky
            $tWhere = $tWhere."protypid2=1 OR ";
            break;
          case 'ak': //Akce
            $tWhere = $tWhere."protypid=1 OR ";
            break;
          case 'dz': //Doprava zdarma
            $tWhere = $tWhere."prodelfree=1 OR ";
            break;
          case 'ti': //tip
            $tWhere = $tWhere."protypid3=1 OR ";
            break;
          case 'zd': //zlate dny
            $tWhere = $tWhere."protypid4=1 OR ";
            break;
        }
      }
      if (!empty($tWhere)) $tWhere = " AND (".substr($tWhere, 0, -4).")";
      $where = $where.$tWhere;
    }

    /*

    //nactu max a min cenu
    $proMinMax = dibi::fetch("
SELECT MAX(IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId.$this->userData->usrprccat.")) AS proprice_max,
MIN(IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId.$this->userData->usrprccat.")) AS proprice_min
FROM products
INNER JOIN catplaces ON (capproid=proid)
INNER JOIN catalogs ON (capcatid=catid)
WHERE catpathids LIKE '%|$id|%' AND
$where");

    if (empty($proMinMax->proprice_max)) $proMinMax->proprice_max = 0;
    if (empty($proMinMax->proprice_min)) $proMinMax->proprice_min = 0;
    if ((empty($this->pF) && empty($this->pT)) || ((int)$this->pF == (int)$proMinMax->proprice_min && (int)$this->pT == (int)$proMinMax->proprice_max)) {
      $this->pF = NULL;
      $this->pT = NULL;
    } else {
      if ((int)$this->pT > (int)$proMinMax->proprice_max || (int)$this->pF < (int)$proMinMax->proprice_min) {
        $this->pF = NULL;
        $this->pT = NULL;
      } else {
        //$wherePrice = " AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) BETWEEN ".(int)$this->pF." AND ".(int)$this->pT;
      }
    }
    */

    //cena od
    if (!empty($this->pF)) {
      $where = $where." AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a)>=".(int)$this->pF;
    }
    //cena do
    if (!empty($this->pT)) {
      $where = $where." AND IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a)<=".(int)$this->pT;
    }

    $dataSource = $product->getDataSource($product->getSqlCatalogList($id, $where, $orderBy));

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    //$this->template->proMinMax = $proMinMax;
    //$this->template->proPriceFrom = (empty($this->pF) ? $proMinMax->proprice_min : $this->pF);
    //$this->template->proPriceTo = (empty($this->pT) ? $proMinMax->proprice_max : $this->pT);

    //statistika prodejnosti
    $this->template->saleStatProducts = array();
    if (isset($this->template->rootCatalog)) {
      $cacheKey = 'catalogSaleStats_'.$this->template->rootCatalog->catid.$this->curId.$this->userData->usrprccat;
      $catalogSaleStats = $catalog->cacheGet($cacheKey);
      if ($catalogSaleStats === FALSE) {
        $catalogSaleStats = array();
        if (!empty($catalogData->catsalestat)) {
          $proCodesList = explode(',',trim($catalogData->catsalestat, ','));
          $cnt = 0;
          foreach ($proCodesList as $proId) {
            $cnt++;
            $proId = trim($proId);
            if (!empty($proId)) {
              $item = $product->load($proId);
              if ($item) $catalogSaleStats[] = $item;
            }
            if ($cnt>=3) break;
          }
        } /*else {
          //vezmu ze statistiky prodejnosti
          $rows = dibi::fetchAll("SELECT prsproid  FROM products_salestat WHERE prscatpathids LIKE '%|".$catalogData->catid."|%' ORDER BY prscnt DESC LIMIT 3");
          foreach ($rows as $key => $row) {
            $catalogSaleStats[] = $product->load($row->prsproid);
          }
        }*/
        $catalog->cacheSave($cacheKey, $catalogSaleStats);
      }
      $this->template->saleStatProducts = $catalogSaleStats;
    }

    //naplnim filtr vybranymi hodnotami
    if (!empty($this->m)) $arr["m"] = $this->m;
    if (!empty($this->t)) $arr["t"] = $this->t;
    if (!empty($this->o)) $arr["o"] = $this->o;
    if (!empty($this->pF)) $arr["pF"] = $this->pF;
    if (!empty($this->pT)) $arr["pT"] = $this->pT;
    if (!empty($arr)) {

      $form = $this->getComponent("catalogSearchForm");
      $form->setDefaults($arr);
    }
    //zakazu indexovani pokud filtruje, radi nebo strankuje
    if (!empty($arr) OR $paginator->page > 1) {
      $this->template->pageRobots = "noindex,follow";
    }

    //sestavim popisek co ma ve filtru
    //vyrobci
    $arr = array();
    If (!empty($manwhere)) {
      $arr["m"] = dibi::query("SELECT manid, manname FROM manufacturers WHERE manid IN (".$manwhere.")")->fetchPairs("manid", "manname");
    }
    //forma
    if (count($this->t) > 0) {
      $a = $this->getEnumTypes();
      $aar = array();
      foreach ($this->t as $key => $value) {
        $aar[$key] = $a[$key];
      }
      $arr["t"] = $aar;
    }
    If (!empty($this->pF)) $arr["pF"] = $this->pF;
    If (!empty($this->pT)) $arr["pT"] = $this->pT;
    $this->template->formVals = $arr;
  }

  protected function createComponentCatalogSearchForm() {
    $form = $this->createAppForm();

    $pros = new \Model\ProductsModel();
    $catid = $this->getParameter('id');
    //nactu vyrobce v katalogu
    $manufacts = dibi::query("
      SELECT manid, manname
      FROM products
      INNER JOIN catplaces ON (capproid=proid)
      INNER JOIN catalogs ON (capcatid=catid)
      INNER JOIN manufacturers ON (manid=promanid)
      WHERE catpathids LIKE '%|$catid|%'
      GROUP BY manid
      ORDER BY manname
    ")->fetchPairs("manid", "manname");
    $container = $form->addContainer('m');
    foreach ($manufacts as $key=>$name) {
      $container->addCheckbox($key, $name);
    }

    //$arr = $this->getEnumOrderBy();
    //$form->addSelect("o", "", $arr);
    //if (!empty($this->o)) $form["o"]->setDefaultValue($this->o);

    $arr = $this->getEnumTypes();
    $container = $form->addContainer('t');
    foreach ($arr as $key=>$name) {
      $container->addCheckbox($key, $name);
    }


    $form->addText("pF", 'Cena od', 5);
    $form->addText("pT", 'Cena do', 5);

    $form->addSubmit('search', 'Filtrovat')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'catalogSearchFormSubmitted');
    return $form;
  }

  public function catalogSearchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $this->m = array();
      foreach ($vals['m'] as $key => $val) {
        if ($val) $this->m[$key] = $key;
      }
      $this->t = array();
      foreach ($vals['t'] as $key => $val) {
        if ($val) $this->t[$key] = $key;
      }
      //$this->o = $vals->o;
      $this->pF = $this->formatNumberMySQL($vals->pF);
      $this->pT = $this->formatNumberMySQL($vals->pT);
      $this->redirect('this');
    }
  }

  /**
  * číselínk pro filtrování OrderBy
  *
  */
  private function getEnumOrderBy() {
    return array(
       '' => 'Výchozí řazení',
       'pa' => 'Řadit cenu od nejnižší',
       'pd' => 'Řadit cenu od nejvyšší',
       'na' => 'Řadit podle názvu A-Z',
       'nd' => 'Řadit podle názvu Z-A',
     );
  }

  /**
  * číselínk pro filtrování Types
  *
  */
  private function getEnumTypes() {
    return array(
      'sk' => 'Skladem',
      'ak' => 'Akce',
      'no' => 'Novinka',
      'ti' => 'Tip',
      'zd' => 'Zlaté dny',
      'dz' => 'Doprava zdarma',
    );
  }

}
