<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  <PERSON>;

final class MenuIndexPresenter extends BasePresenter {

  public function menuEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $formVals = $form->getValues();

      $menus = new \Model\MenuIndexsModel();
      //pred ulozenim vymazu z cache prislusny ciselnik
      $cache = $menus->cacheClean();

      $id = (int)$this->getParameter('id');

      //vezmu si obrazek pokud byl odeslan
      $image = $formVals["picture"];
      unset($formVals["picture"]);

      if ($id > 0) {
        $menus->update($id, $formVals);
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $id = $menus->insert($formVals);
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
      if ($image->isOk()) $image->move(WWW_DIR."/pic/menuindex/".$id.".jpg");

    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $menus = new \Model\MenuIndexsModel();
    //$dataRows = $menus->fetchAll("SELECT * FROM menus WHERE ORDER BY menorder");
    $this->template->items = dibi::query("SELECT * FROM menuindexs")->fetchAssoc("meitarget,meiid");

    //ciselniky
    $this->template->enum_menstatus = $menus->getEnumMenStatus();
    $this->template->enum_meitarget = $menus->getEnumMeiTarget();
  }

  public function renderEdit($id) {
    /** @var Nette\Forms\Form $form */
    $form = $this['menuIndexEditForm'];

    if (!$form->isSubmitted()) {
      $menus = new \Model\MenuIndexsModel();
      if ($id > 0) {
        $dataRow = $menus->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }

        $form->setDefaults($dataRow);

        $this->template->dataRow = $dataRow;

        //zjistim jestli existuje obrazek
        $fileName = WWW_DIR."/pic/menuindex/$id.jpg";
        if(file_exists($fileName)) $this->template->imagePath = "pic/menuindex/$id.jpg";

      } else {
        $defVals = array();
        $menmasid = $this->getParameter("meimasid");
        if ($menmasid > 0) $defVals["meimasid"] = $menmasid;

        $form->setDefaults($defVals);

      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $menus = new \Model\MenuIndexsModel();
      $menus->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentMenuIndexEditForm() {
    $menu = new \Model\MenuIndexsModel();
    $form = $this->createAppForm();
    $id = (int)$this->getParameter('id');
    $type = $this->getParameter('meisrctype');
    $form->addHidden('meibig', 1);

    $form->addSelect('meitarget', 'Cíl reklamy:', $menu->getEnumMeiTarget())
      ->setPrompt('')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte cíl reklamy.');

    $form->addText('meiname', 'Název:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název.');

    //pole pro seznam stranek
    $menpagarr = dibi::query("SELECT pagid, pagname FROM pages WHERE pagblock=0 ORDER BY pagname")
      ->fetchPairs('pagid', 'pagname');
    $form->addSelect('meipagid', 'Textová stránka:', $menpagarr)
      ->setPrompt('');
    $catalogs = new \Model\CatalogsModel();
    $arr = $catalogs->getEnumCatalogCombo();
    unset($arr[0]);

    $form->addSelect('meicatid', 'Katalog:', $arr)
      ->setAttribute("novalidate", "novalidate")
      ->setPrompt('');
    $form->addText('meiprocode', 'Kód zboží:', 15) ;
    $form->addText('meiurl', 'URL:', 100);

    //$form->addSelect('meimasid', 'Nadřízená úroveň:', $menu->getEnumMenuIndexCombo());
    $form->addHidden('meimasid', 0);

    $form->addTextArea('meidesc', 'Texty:', 80, 6) ;

    //obrazek
    $form->addUpload('picture', 'Obrázek:')
      ->setOption('description', 'Obrázek se nijak automaticky neupravuje. Musí mít rozměr '.$this->config["MEIPICSIZEBIG"].' pro slider na úvodní stránce a '.$this->config["MEIPICSIZEFOOTER"].' pro reklamu v patičce. Nahráním nového obrázku ten původní přepíšete.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIME_TYPE, 'Povolené typy souborů jsou pouze .png, .jpe, .jpeg, .jpg, .gif', 'image/png,image/jpeg,image/gif');

    $form->addText('meiorder', 'Pořadí:', 15)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte pořadí.');

    $form->addSelect('meistatus', 'Status:', $menu->getEnumMenStatus())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte status.');

    $form->addSubmit('save', 'Uložit');

    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'menuEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }
}
