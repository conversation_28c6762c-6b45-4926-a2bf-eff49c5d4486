// základní nastavení formul<PERSON>

// sk<PERSON>t<PERSON> formul<PERSON>ř<PERSON> a prvků v tiskové verzi
form, button, input[type="submit"] {

  @media print {
    display: none;
  }

}

// základní vzhled input polí
input, textarea, select {

  // ošetření na nižš<PERSON>, k<PERSON><PERSON> je zadán size
  width: 100%;
  max-width: 100%;

  margin-bottom: 5px;
  padding: 6px 10px;

  font-size: 16px;

  background-color: @color_gray_light;
  border: 1px solid @color_gray_light;

  &:focus {
    border: 1px solid @color_main_dark;
  }

  // pole v pořádku
  &.is--success {
    color: @color_success;
    border: 1px solid @color_success;
  }

  // chybně vyplněné pole
  &.is--danger {
    color: @color_danger;
    border: 1px solid @color_danger;
  }

}

// specifické definice pro input
.input--75 { width: 74%; }
.input--50 { width: 49%; }
.input--25 { width: 24%; max-width: 100px; }

// specifické definice pro textarea
.textarea--75 { width: 74%; }
.textarea--50 { width: 49%; }

// popisek pole
label {
  display: inline-block;
  padding-bottom: 5px;
}

// ošetření submit inputu, pokud je použit
button, input[type="submit"] {
  width: auto;
  cursor: pointer;
}

// ošetření chekboxu a radio buttonu
input[type="checkbox"], input[type="radio"] {
  width: auto;
  margin-right: 5px;
}

// formuláře jsou uzavřeny ve fieldsetu
fieldset {

  margin: 1em 0;
  padding: 0 1em;
  border: 1px solid @color_main;

  legend {
    padding: 6px 10px 7px 10px;
    color: @color_white;
    background-color: @color_main;
  }

}

// ošetření google reCaptcha
.g-recaptcha {

  @media (max-width: @mqxxs) {
    width: 1px;
    transform: scale(0.74);
  }

}
