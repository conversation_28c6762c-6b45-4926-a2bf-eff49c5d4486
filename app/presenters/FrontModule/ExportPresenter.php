<?php
namespace FrontModule;
use dibi;
use Nette;

final class ExportPresenter extends BasePresenter {

  const FORMAT_ZBOZICZ = 'zbozicz';
  const FORMAT_JYXOCZ = 'jyxocz';
  const FORMAT_HEUREKACZ = 'heurekacz';
  const FORMAT_HEUREKACZFULL = 'heurekaczfull';
  const FORMAT_GOOGLECOM = 'googlecom';

  public function actionProducts($id) {
    $products = new \Model\ProductsModel();

    if ($id != "heurekacz-acc") {
      $sql = "
SELECT pro.*, promas.proid AS proidmas, promas.procode AS procodemas, promas.propicname AS propicnamemas, manname, 
catid, catpath, catpathids, COALESCE(promas.procpcheureka, pro.procpcheureka) AS procpcheureka, COALESCE(promas.procpczbozi, pro.procpczbozi) AS procpczbozi, 
IF(pro.provatid=0, ".$this->config["VATTYPE_0"].", ".$this->config["VATTYPE_1"].") AS provat
FROM products AS pro
LEFT JOIN manufacturers ON (pro.promanid=manid)
LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
LEFT JOIN catplaces ON (capproid=coalesce(promas.proid,pro.proid))
LEFT JOIN catalogs ON (capcatid=catid)
WHERE pro.proaccess=0 AND pro.proprice".$this->curId."a>0 AND catstatus=0 AND pro.prostatus=0 AND ".($id == self::FORMAT_HEUREKACZ ? "pro.proismaster=0 AND " : "")." 
(promas.proaccess IS NULL OR promas.proaccess = 0) AND (promas.prostatus IS NULL OR promas.prostatus = 0) ".($id == self::FORMAT_GOOGLECOM ? " AND (pro.progoogleoff=0 AND COALESCE(promas.progoogleoff,0)=0)" : "")."
GROUP BY pro.proid ORDER BY promasid";

      //doplnim vycet doprav
      $delModes = dibi::query("
        SELECT delid, delname, delcode, COALESCE(disfrom, -1) AS delpricelimitfrom, delprice1a AS delprice
        FROM deliverymodes 
        LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat='a' AND discurid=1)
        WHERE delstatus=0 AND (delcurid=0 OR delcurid=1) AND delmasid=0 AND delcode IS NOT NULL AND delcode!='OSOBNE' ORDER BY delorder
      ")->fetchAssoc('delid');

      foreach ($delModes as $key => $row) {
        $payModes = dibi::query("
        SELECT delid, delmasid, delname, delcode, COALESCE(disfrom, -1) AS delpricelimitfrom, delprice1a AS delprice
        FROM deliverymodes 
        LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat='a' AND discurid=1)
        WHERE delstatus=0 AND (delcurid=0 OR delcurid=1) AND delmasid=%i", $key, " AND delcode IN ('dobirka', 'paybefore') ORDER BY delorder
        ")->fetchAssoc('delcode');
        if (count($payModes) > 0) {
          $delModes[$key]->paymodes = $payModes;
        } else {
          unset($delModes[$key]);
        }
      }

      $this->template->delModes = $delModes;

      //doplnim katalogove cesty pro heureku
      if ($id == "heurekacz") $this->template->heurekaCatalogs = dibi::query("SELECT catid, catname, catpathheureka FROM catalogs WHERE catpathheureka is not null AND catpathheureka!=''")->fetchAssoc("catid");

      //doplnim katalogove cesty pro google
      if ($id == "googlecom") $this->template->googleCatalogs = dibi::query("SELECT catid, catname, catpathgoogle FROM catalogs WHERE catpathgoogle is not null AND catpathgoogle!=''")->fetchAssoc("catid");

      //doplnim katalogove cesty pro zbozi
      if ($id == "zbozicz") $this->template->zboziCatalogs = dibi::query("SELECT catid, catname, catpathzbozi FROM catalogs WHERE catpathzbozi is not null AND catpathzbozi!=''")->fetchAssoc("catid");

    } else {
      $row = dibi::fetch("SELECT CURTIME(), IF(CURTIME() > TIME('11:00:00'),1,0) AS delIsAfter, IF(CURTIME() > TIME('17:30:00'),1,0) AS perIsAfter");
      $delDaysAdd = (int)$row->delIsAfter;
      $perDaysAdd = (int)$row->perIsAfter;
      $sql = "SELECT pro.proid, pro.procode, pro.proaccess,
CONCAT(DATE(CURDATE() + INTERVAL (pro.proaccess + $delDaysAdd + 1) DAY), ' 17:00') AS deldate,
CONCAT(DATE(CURDATE() + INTERVAL (pro.proaccess + $perDaysAdd) DAY), ' 18:00') AS perdate,
CONCAT(DATE(CURDATE() + INTERVAL $delDaysAdd DAY), ' 11:00') AS deldatedeadline,
CONCAT(DATE(CURDATE() + INTERVAL $perDaysAdd DAY), ' 17:30') AS perdatedeadline
FROM products AS pro
LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
WHERE pro.proaccess=0 AND pro.prostatus=0 AND 
(promas.proaccess IS NULL OR promas.proaccess = 0) AND (promas.prostatus IS NULL OR promas.prostatus = 0)
GROUP BY pro.proid";
      $id = "heurekaczAccess";
    }
    $this->template->rows=dibi::fetchAll($sql);
    $this->template->view=$id;
    if ($id == 'heurekaczfull') $id = 'heurekacz';
    $this->setView($id);
  }



  public function actionSitemap() {
    $this->template->products=dibi::fetchAll("
SELECT pro.*, coalesce(pro.prodateu, pro.prodatec) AS moddate 
FROM products AS pro
INNER JOIN catplaces ON (capproid=pro.proid)
INNER JOIN catalogs ON (capcatid=catid)
LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
WHERE pro.proprice".$this->curId."a > 0 AND catstatus=0 AND pro.prostatus=0 AND 
(promas.prostatus IS NULL OR promas.prostatus = 0)
GROUP BY pro.proid");
    $catalogs = dibi::query("
    SELECT *, coalesce(catdateu, catdatec) AS moddate
    FROM catalogs
    WHERE catstatus=0 AND catmasid=0
    ORDER BY catorder")->fetchAssoc('catid');
    foreach ($catalogs as $key => $row) {
      $catalogs[$key]->subCats = dibi::fetchAll("
    SELECT catalogs.*, coalesce(catdateu, catdatec) AS moddate FROM catalogs
    WHERE catstatus=0 AND catpathids LIKE '|$row->catid|%' AND catlevel=2
    GROUP BY catid");
    }
    $this->template->catalogs = $catalogs;

    //vyrobci
    $this->template->manufacturers = dibi::fetchAll("
      SELECT *, coalesce(mandateu, mandatec) AS manmoddate 
      FROM manufacturers 
      INNER JOIN products AS pro ON (pro.promanid=manid)  
      INNER JOIN catplaces ON (capproid=pro.proid)
      INNER JOIN catalogs ON (capcatid=catid)
      LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
      WHERE pro.proprice".$this->curId."a > 0 AND catstatus=0 AND pro.prostatus=0 AND 
      (promas.prostatus IS NULL OR promas.prostatus = 0)
      GROUP BY manid");

    //clanky
    $this->template->articles = dibi::fetchAll("
    SELECT *, coalesce(artdateu, artdatec) AS moddate FROM articles WHERE artstatus=0
    ");

    $this->template->menuTopExport = dibi::fetchAll("SELECT *, coalesce(pagdateu, pagdatec) AS moddate FROM menus INNER JOIN pages ON (pagid=mensrcid AND mensrctype='page') WHERE menmasid=1 AND menstatus=0 ORDER BY menorder");
    $this->template->menuIndexs = dibi::fetchAll("
    SELECT *, coalesce(pagdateu, pagdatec) AS moddate FROM menuindexs
    INNER JOIN pages ON (pagid=meipagid)
    WHERE meimasid=0 AND meistatus=0 AND meipagid > 0 ORDER BY meiorder");

  }

  public function actionUlozenka(array $ids) {
    header('Content-type: application/octetstream');
    header('Content-Disposition: attachment; filename="ulozenka.csv"');
    header('Cache-Control: max-age=0');
    $this->setLayout('empty');
    $this->setView('ulozenka');
    $ordids = implode(',', $ids);
    $ordids = trim($ordids, ',');
    $this->template->rows=dibi::fetchAll("
    SELECT orders.*, paymode.delcode AS paycode FROM orders
    INNER JOIN deliverymodes AS paymode ON (orddelid=paymode.delid)
    INNER JOIN deliverymodes AS delmode ON (paymode.delmasid=delmode.delid)
    WHERE
    delmode.delcode='HEUREKAPOINT' AND
    ordid IN ($ordids)");
  }

}
