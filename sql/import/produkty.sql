TRUNCATE `products`;
INSERT INTO products (
SELECT
proid AS proid,
NULL AS prokey,
procode AS procode,
NULL AS procode2,
NULL AS procodep,
propriid AS progifts,
0 AS promasid,
NULL AS procatrootid,
0 AS proismaster,
promanid AS promanid,
0 AS protypid,
0 AS protypid2,
0 AS protypid3,
0 AS protypid4,
0 AS protypid5,
NULL AS proorigin,
proname AS proname,
NULL AS proname2,
pronames AS pronames,
NULL AS proforid,
NULL AS prodescs,
CONCAT(prodesc, prosdesc) AS prodesc,
NULL AS pronutrition,
NULL AS provideo,
NULL AS proico,
NULL AS prokeywords,
prometa AS protitle,
NULL AS prodescription,
100 AS proaccess,
proaccess AS proaccesstext,
0 AS proqty,
0 AS proqty_store,
0 AS proqty_shop,
prowarranty AS prowarranty,
propricecom AS proprice1com,
propricea AS proprice1a,
propriceb AS proprice1b,
propricec AS proprice1c,
propriced AS proprice1d,
NULL AS proprice2com,
NULL AS proprice2a,
NULL AS proprice2b,
NULL AS proprice2c,
NULL AS proprice2d,
0 AS provatid,
0 AS procredit,
0 AS procpcheureka,
0 AS procpczbozi,
0 AS prorecycle,
0 AS proweight,
1 AS prosalestat,
pronotdisc AS pronotdisc,
prodelfree AS prodelfree,
0 AS progoogleoff,
prorating AS prorating,
propicname AS propicname,
NULL AS progift,
NULL AS protrarestr,
NULL AS proaccessories,
NULL AS prooptions,
protrarestr AS prooptionskeywords,    -- souvisejici zbozi klicove slova
0 AS prooffer,
0 AS probigsize,
procnt AS procounter,
proorder AS proorder,
prodisable AS prostatus,
NULL AS pronoteint,
proc AS prodatec,
prou AS prodateu
FROM cz_product);


-- nastavit název obrázku
UPDATE products SET propicname=procode WHERE COALESCE(propicname,'')='';

-- SPUSTIT /administrace/import/recalc-pro-is-master

-- SPUSTIT /administrace/import/import-products-param

-- vymazu po naimportování parametrů vymazat ty které už nejsou třeba
DELETE FROM proparams WHERE NOT EXISTS (SELECT proid FROM products WHERE prpproid=proid);