{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}

{block #content}

  <div class="article article--contact">

	<h2>{_'Kontaktní adresa a kamenný obchod'}</h2>

	<div class="article__into">

	    {$page->pagbody|noescape}

	</div>

    {form contactForm}
    {* vypise chyby formulare *}
    {include ../@formErrors.latte form=>$form}

	<fieldset>

		<legend>Napište nám</legend>

		<p>
			{php echo $form['conname']->getLabel() }:<br>
			{php echo $form['conname']->getControl()->size(60) }
		</p>

		<p>
			{php echo $form['conmail']->getLabel()->class('required') }:<br>
			{php echo $form['conmail']->getControl()->size(60) }
		</p>

		<p>
			{php echo $form['congsm']->getLabel() }:<br>
			{php echo $form['congsm']->getControl()->size(30) }
		</p>

		<p class="antispam">
			{php echo $form['antispam']->getLabel() }:<br>
			{php echo $form['antispam']->getControl() }
		</p>

		<p>
			{php echo $form['connote']->getLabel() }<br>
			{php echo $form['connote']->getControl() }
		</p>

		{php echo $form['save']->getControl()->value('Odeslat formulář') }

	</fieldset>

	{/form}

	<script type="text/javascript">
		$('#antispam').val('{$presenter->config["ANTISPAM_NO"]|noescape}');
		$('.antispam').hide();
	</script>

  </div>

{/block}
