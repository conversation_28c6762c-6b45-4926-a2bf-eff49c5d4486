
=================
Open Source Fonts
=================
mPDF should be able to read most TrueType Unicode font files with a .ttf extension
Truetype fonts with .ttf or .otf extension and .ttc TrueType collections should also work OK.

You will probably therefore have access to any number of fonts, but here are some useful sources for 
open-source or free-use fonts. Many of these were included with earlier versions of mPDF:

General collections
-------------------
- DejaVu font collection from: http://dejavu-fonts.org/wiki/Main_Page (included in the release)
- GNU FreeFont from: http://www.gnu.org/software/freefont/
- Linux Libertine from: http://www.linuxlibertine.org (use the .ttf files - not the .otf)
- Liberation Fonts from: https://fedorahosted.org/liberation-fonts/ or directly https://fedorahosted.org/releases/l/i/liberation-fonts/

Thai fonts
----------
- Collection of Thai fonts from: ftp://linux.thai.net/pub/thailinux/software/thai-ttf/


OCR-B
-----
OCRB (ocrb10) font: http://ansuz.sooke.bc.ca/software/ocrb.php
	Useful for barcodes (included in the release)

Pan-Unicode fonts (fonts containing most characters defined in Unicode)
-----------------------------------------------------------------------
- BitStream Cyberbit from: http://aol-4.vo.llnwd.net/pub/communicator/extras/fonts/windows/
	Cyberbit.ZIP (or just Google it)
	(NB if run with $debug_fonts gives warning of invalid table checksum - just turn it off!)
- Code2000 (NB shareware) from: http://www.code2000.net/code2000_page.htm
- Quivira from: http://www.quivira-font.com/ (combining diacritics are not placed correctly, but has a very full set of glyphs)


CJK (Chinese-Japanese-Korean)
-----------------------------
HanNomA and B from: http://vietunicode.sourceforge.net/fonts/fonts_hannom.html
unBatang_0613 from: http://people.ktug.or.kr/~nomos/mine/UnBatang_0613.ttf
Sun-ExtA and Sun-ExtB from: http://www.alanwood.net/unicode/fonts-east-asian.html
	(http://www.alanwood.net/downloads/index.html)
Fonts which contain both Unicode BMP and SIP (Supplementary Ideographic Plane) 
	characters in one file e.g. "AR PL UMing" and "AR PL UKai" 
	from: http://www.freedesktop.org  
	http://www.freedesktop.org/wiki/Software/CJKUnifonts/Download
[MS Windows fonts such as MS Mincho and MingLiU also work] 
Japanese IPA fonts: http://ossipedia.ipa.go.jp/ipafont/index.html

Arabic/Eastern fonts
--------------------
- XW Zar fonts can be downloaded from: http://www.redlers.com/downloadfont.html
	(2 of these fonts are included in the release)
	(original site in Farsi: http://wiki.irmug.org/index.php/XWZar)
- See below for more details and font names of the fonts used in mPDF 4.x


Other sources
-------------
http://www.unifont.org/fontguide/
http://www.alanwood.net/unicode/fonts.html
http://cooltext.com (e.g. http://cooltext.com/Fonts-Unicode+Arabic)
ApparatusSIL, DoulosSIL, Padauk from SIL: http://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&cat_id=FontDownloads
DaiBanna (Tai Le) 

Mediaeval & Ancient Scripts
---------------------------
Junicode: http://junicode.sourceforge.net/
Cardo: http://scholarsfonts.net/cardofnt.html

Syriac fonts
------------
http://www.bethmardutho.org/meltho/




============
ARABIC FONTS
============
You can use any appropriate Unicode-encoded .ttf or .ttc font file for arabic text, but
the font file must include the Arabic Presentation Forms-B (U+FE70�U+FEFF).
2 of the XW Zar fonts are included in the release.



===========
INDIC FONTS
===========
Indic scripts e.g. Hindi, Bengali etc. use conjuct forms which have no Unicode assignment. You 
cannot therefore use any standard .ttf font files with mPDF.

A set of fonts has been adapted to use specifically with mPDF. They are based on a set of fonts from:

http://www.cdacmumbai.in/projects/indix/e_introduction.shtml

NB These fonts are released under GNU GPL Version 2 license

You need these adapted fonts (ind_bn_1_001.ttf etc.) to work with mPDF, as the glyphs have been 
assigned to Unicode values in the Private Use area (U+E000 ->). These are included in the 
mPDF download.



Users of mPDF v4.x
------------------
The original indic font files do not contain a full set of even basic ASCII
characters. The fonts supplied have been "fixed" to include those extra
characters. This now works whether the font is subset or not.



=========
CJK Fonts
=========


Supplemental Ideographic Plane
==============================
SIP = Supplemental Ideographic Plane ( = Plane 2) = U+20000 - U+2FFFF  (131072 - 196607)
These characters are required for the HKSCS Hong Kong Supplementary Character Set. Fonts with 
SIP characters are commonly divided into a second file e.g. MingLiU and MingLiU-ExtB

mPDF requires 2 fonts and they should be cross-referenced in config_fonts.php e.g.
	"sun-exta" => array(
		'R' => "Sun-ExtA.ttf",
		'sip-ext' => 'sun-extb',
		),
	"sun-extb" => array(
		'R' => "Sun-ExtB.ttf",
		),

--OR--
(NB these ones also define the font number within the TTC Collection as TTCfontID)
	'mingliu' => array (
		'R' => 'mingliu.ttc',
		'TTCfontID' => array (
			'R' => 1,
		),
		'sip-ext' => 'mingliu-extb',
	),
	'mingliu-extb' => array (
		'R' => 'mingliub.ttc',
		'TTCfontID' => array (
			'R' => 1,
		),
	),
	'mingliu_hkscs' => array (
		'R' => 'mingliu.ttc',
		'TTCfontID' => array (
			'R' => 3,
		),
		'sip-ext' => 'mingliu_hkscs-extb',
	),
	'mingliu_hkscs-extb' => array (
		'R' => 'mingliub.ttc',
		'TTCfontID' => array (
			'R' => 3,
		),
	),




