// ob<PERSON><PERSON><PERSON><PERSON><PERSON> proces

.order {

  // základ<PERSON><PERSON> pod<PERSON> nad<PERSON>ů
  h1,
  h2,
  h3,
  h4 {
    color: @color_main_light;
    font-weight: 300;
    text-transform: uppercase;
  }

  h2 {
    font-size: 20px;
    @media (min-width: @mqsm) { font-size: 30px; }
  }

  h3 {
    font-size: 18fpx;
    @media (min-width: @mqsm) { font-size: 25px; }
  }

  h4 {
    font-size: 16px;
    @media (min-width: @mqsm) { font-size: 18px; }
  }

  p {

    @media (max-width: @mqsm) {
      margin: 5px 0;
    }

  }

  a {

    color: @color_main_light;

    &:hover, &:active, &:focus {
      color: @color_main_dark;
    }

  }

  // ošetření barev odkazů
  a.btn {
    color: @color_white;
  }
  a.control .icon {
    color: @color_white;
  }

}

  // tabulka v objednávce
  .order__table {

    width: 100%;

    // r<PERSON><PERSON><PERSON><PERSON> tabulky
    border: 0;
    th, td {
      border: 0;
    }

    // více prostoru v tabulce
    th, td {

      padding: 5px;

      @media (min-width: @mqsm) {
        padding: 10px 20px;
      }

    }

    // ošetření velikosti inputu
    input {
      width: 65px;
    }

    // rozlámání tabulky na malých rozlišeních
    @media only screen and (max-width: @mqxs) {

      display: block;

      th, td, tr, thead, tbody {
        display: block;
      }

      th + th {
        border-top: 1px solid @color_main_dark;
      }

      tr td:last-child {
        border-bottom: 2px solid @color_main_light;
      }

    }

  }

  // produkt + obrázek
  .order__product {

    // zmenšení a pozice obrázku
    img {
      float: left;
      width: 100px;
      height: auto;
      margin-right: 0.5em;
    }

    // odkazy
    a {

      color: @color_main_light;
      font-size: 17px;
      text-transform: uppercase;
      text-decoration: none;

      &:hover, &:active, &:focus {
        color: @color_main_dark;
      }

      img:hover {
        opacity: 0.8;
      }

    }

  }

  // počet kusů
  .order__count {
    white-space: nowrap;
  }

  // cena
  .order__price {

    max-width: 65px;
    text-align: right;
    font-weight: 700;
    white-space: nowrap;

    @media only screen and (max-width: @mqxs) {
      width: 100%;
      max-width: 100%;
      text-align: center;
      font-size: 18px;
    }

  }

  // cena za produkty celkem
  .order__price-sum {

    td {

      color: @color_main;

      @media only screen and (max-width: @mqxs) {
        display: none;
      }

    }

  }

  // sleva
  .order__discount {

    td {
      color: @color_success;
    }

  }

  // celková cena vč slevy, atd.
  .order__price-final {

    padding-top: 20px;
    padding-bottom: 20px;
    color: @color_white;
    font-size: 16px;
    font-weight: 300;
    background-color: @color_main_light;

    strong {
      float: right;
      font-weight: 700;
      white-space: nowrap;
    }

    @media (min-width: @mqsm) {
      font-size: 25px;
    }

  }

  // prázdný košík
  .order__empty {

  }

  // slevový kupón
  .order__coupon {
    margin-top: 1em;
  }

  // platební možnosti
  .order__payment, .order__delivery {

    position: relative;
    margin-bottom: 25px;
    padding-left: 30px;
    font-size: 16px;

    input {
      position: absolute;
      margin-left: -20px;
    }

    a {

      color: @color_main_light;

      &:hover, &:active, &:focus {
        color: @color_main_dark;
      }

    }

  }
  .order__help {
    font-size: 14px;
  }
