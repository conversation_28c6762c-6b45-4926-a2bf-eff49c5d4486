<?php


$html = "
<h1>mPDF</h1>
<h2>RTL Languages</h2>

<h4>English</h4>
<p>Please note that I do not understand any of the scripts below. The texts are borrowed from News websites, and I have used words and bits of phrases just to demonstrate the program.</p>

<h4>Hebrew (pangram)</h4>
<p lang=\"he\">\xd7\x93\xd7\x92 \xd7\xa1\xd7\xa7\xd7\xa8\xd7\x9f \xd7\xa9\xd7\x98 \xd7\x91\xd7\x99\xd7\x9d \xd7\x9e\xd7\x90\xd7\x95\xd7\x9b\xd7\x96\xd7\x91 \xd7\x95\xd7\x9c\xd7\xa4\xd7\xaa\xd7\xa2 \xd7\x9e\xd7\xa6\xd7\x90 \xd7\x97\xd7\x91\xd7\xa8\xd7\x94 </p>

<p lang=\"he\">\xd7\x90\xd7\x95 \xd7\x94\xd7\xa0\xd7\xa1\xd7\x94 \xd7\x90\xd7\x9c\xd7\x94\xd7\x99\xd7\x9d, \xd7\x9c\xd7\x91\xd7\x95\xd7\x90 \xd7\x9c\xd7\xa7\xd7\x97\xd7\xaa \xd7\x9c\xd7\x95 \xd7\x92\xd7\x95\xd7\x99 \xd7\x9e\xd7\xa7\xd7\xa8\xd7\x91 \xd7\x92\xd7\x95\xd7\x99, \xd7\x91\xd7\x9e\xd7\xa1\xd7\xaa \xd7\x91\xd7\x90\xd7\xaa\xd7\xaa \xd7\x95\xd7\x91\xd7\x9e\xd7\x95\xd7\xa4\xd7\xaa\xd7\x99\xd7\x9d \xd7\x95\xd7\x91\xd7\x9e\xd7\x9c\xd7\x97\xd7\x9e\xd7\x94 \xd7\x95\xd7\x91\xd7\x99\xd7\x93 \xd7\x97\xd7\x96\xd7\xa7\xd7\x94 \xd7\x95\xd7\x91\xd7\x96\xd7\xa8\xd7\x95\xd7\xa2 \xd7\xa0\xd7\x98\xd7\x95\xd7\x99\xd7\x94, \xd7\x95\xd7\x91\xd7\x9e\xd7\x95\xd7\xa8\xd7\x90\xd7\x99\xd7\x9d \xd7\x92\xd7\x93\xd7\x9c\xd7\x99\xd7\x9d: \xd7\x9b\xd7\x9b\xd7\x9c \xd7\x90\xd7\xa9\xd7\xa8-\xd7\xa2\xd7\xa9\xd7\x94 \xd7\x9c\xd7\x9b\xd7\x9d \xd7\x99\xd7\x94\xd7\x95\xd7\x94 \xd7\x90\xd7\x9c\xd7\x94\xd7\x99\xd7\x9b\xd7\x9d, \xd7\x91\xd7\x9e\xd7\xa6\xd7\xa8\xd7\x99\xd7\x9d--\xd7\x9c\xd7\xa2\xd7\x99\xd7\xa0\xd7\x99\xd7\x9a </p>

<p lang=\"he\">\xd7\x9c\xd7\x9b\xd7\x9f \xd7\x97\xd7\x9b\xd7\x95 \xd7\x9c\xd7\x99 \xd7\xa0\xd7\x90\xd7\x9d \xd7\x99\xd7\x94\xd7\x95\xd7\x94 \xd7\x9c\xd7\x99\xd7\x95\xd7\x9d \xd7\xa7\xd7\x95\xd7\x9e\xd7\x99 \xd7\x9c\xd7\xa2\xd7\x93, \xd7\x9b\xd7\x99 \xd7\x9e\xd7\xa9\xd7\xa4\xd7\x98\xd7\x99 \xd7\x9c\xd7\x90\xd7\xa1\xd7\xa3 \xd7\x92\xd7\x95\xd7\x99\xd7\x9d \xd7\x9c\xd7\xa7\xd7\x91\xd7\xa6\xd7\x99 \xd7\x9e\xd7\x9e\xd7\x9c\xd7\x9b\xd7\x95\xd7\xaa, \xd7\x9c\xd7\xa9\xd7\xa4\xd7\x9a \xd7\xa2\xd7\x9c\xd7\x99\xd7\x94\xd7\x9d \xd7\x96\xd7\xa2\xd7\x9e\xd7\x99 \xd7\x9b\xd7\x9c \xd7\x97\xd7\xa8\xd7\x95\xd7\x9f \xd7\x90\xd7\xa4\xd7\x99, \xd7\x9b\xd7\x99 \xd7\x91\xd7\x90\xd7\xa9 \xd7\xa7\xd7\xa0\xd7\x90\xd7\xaa\xd7\x99 \xd7\xaa\xd7\x90\xd7\x9b\xd7\x9c \xd7\x9b\xd7\x9c \xd7\x94\xd7\x90\xd7\xa8\xd7\xa5 </p>

<p lang=\"he\">\xd7\xa9\xd7\xa4\xd7\x9f \xd7\x90\xd7\x9b\xd7\x9c \xd7\xa7\xd7\xa6\xd7\xaa \xd7\x92\xd7\x96\xd7\xa8 \xd7\x91\xd7\x98\xd7\xa2\xd7\x9d \xd7\x97\xd7\xa1\xd7\x94, \xd7\x95\xd7\x93\xd7\x99. </p>





<h4>Arabic</h4>
<p>\xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xad\xd8\xaf\xd9\x8a\xd8\xab \xd9\x85\xd8\xaa\xd9\x84\xd9\x81\xd8\xb2<annotation content=\"\xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac\" subject=\"\xd8\xac\xd9\x88\xd8\xb1\xd8\xac\" icon=\"Comment\" color=\"#FE88EF\" author=\"\xd8\xac\xd9\x88\xd8\xb1\xd8\xac\" />
 \xd9\x81\xd9\x8a \xd8\xa7\xd9\x84\xd8\xb0\xd9\x83\xd8\xb1\xd9\x89 \xd8\xa7\xd9\x84\xd8\xb1\xd8\xa7\xd8\xa8\xd8\xb9\xd8\xa9 \xd9\x84\xd9\x84\xd8\xba\xd8\xb2\xd9\x88 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a \xd9\x84\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82 \xd8\xa7\xd9\x86 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x88\xd9\x84\xd9\x88\xd9\x8a\xd8\xa9 \xd8\xad\xd8\xa7\xd9\x84\xd9\x8a\xd8\xa7 \xd9\x84\xd8\xa7\xd8\xb9\xd8\xa7\xd8\xaf\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd9\x86 \xd9\x84\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82.</p>

<p>\xd9\x87\xd9\x84 \xd8\xb3\xd8\xaa\xd8\xb3\xd9\x81\xd8\xb1 \xd8\xa7\xd9\x84\xd8\xac\xd9\x87\xd9\x88\xd8\xaf \xd8\xa7\xd9\x84\xd8\xaf\xd8\xa8\xd9\x84\xd9\x88\xd9\x85\xd8\xa7\xd8\xb3\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xac\xd8\xa7\xd8\xb1\xd9\x8a\xd8\xa9 \xd8\xb9\xd9\x86 \xd8\xad\xd9\x84\xd9\x88\xd9\x84\xd8\x9f \xd9\x88\xd9\x83\xd9\x8a\xd9\x81 \xd8\xaa\xd9\x86\xd8\xb8\xd8\xb1 \xd9\x84\xd9\x84\xd8\xa7\xd8\xaa\xd9\x87\xd8\xa7\xd9\x85\xd8\xa7\xd8\xaa \xd9\x84\xd8\xa8\xd8\xb9\xd8\xb6 \xd9\x87\xd8\xb0\xd9\x87 \xd8\xa7\xd9\x84\xd8\xaf\xd9\x88\xd9\x84 \xd8\xa8\xd8\xa7\xd9\x84\xd8\xaa\xd8\xaf\xd8\xae\xd9\x84 \xd9\x81\xd9\x8a \xd8\xa7\xd9\x84\xd8\xb4\xd8\xa3\xd9\x86 \xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82\xd9\x8a\xd8\x8c \xd9\x88\xd8\xa7\xd9\x84\xd8\xaa\xd9\x88\xd8\xb1\xd8\xb7 \xd9\x81\xd9\x8a \xd8\xaf\xd8\xb9\xd9\x85 \xd8\xb9\xd9\x85\xd9\x84\xd9\x8a\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd8\xb9\xd9\x86\xd9\x81\xd8\x9f \xd9\x88\xd8\xa7\xd9\x84\xd9\x89 \xd8\xa7\xd9\x8a \xd9\x85\xd8\xaf\xd9\x89 \xd9\x8a\xd8\xa8\xd8\xaf\xd9\x88 \xd8\xa7\xd9\x84\xd9\x88\xd8\xb6\xd8\xb9 \xd9\x81\xd9\x8a \xd8\xa7\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82 \xd8\xa7\xd9\x86\xd8\xb9\xd9\x83\xd8\xa7\xd8\xb3\xd8\xa7 \xd9\x84\xd9\x84\xd8\xb5\xd8\xb1\xd8\xa7\xd8\xb9\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd8\xa5\xd9\x82\xd9\x84\xd9\x8a\xd9\x85\xd9\x8a\xd8\xa9 \xd9\x81\xd9\x8a \xd8\xa7\xd9\x84\xd9\x85\xd9\x86\xd8\xb7\xd9\x82\xd8\xa9\xd8\x9f</p>

<p>\xd9\x88\xd8\xa7\xd8\xaf\xd8\xa7\xd9\x86 \xd8\xa7\xd9\x84\xd8\xa8\xd9\x8a\xd8\xaa \xd8\xa7\xd9\x84\xd8\xa7\xd8\xa8\xd9\x8a\xd8\xb6 &quot;\xd8\xa8\xd8\xb4\xd8\xaf\xd8\xa9&quot; \xd8\xaa\xd9\x81\xd8\xac\xd9\x8a\xd8\xb1 \xd8\xa7\xd9\x8a\xd9\x84\xd8\xa7\xd8\xaa \xd9\x81\xd9\x8a\xd9\x85\xd8\xa7 \xd8\xa7\xd8\xb9\xd8\xb1\xd8\xa8\xd8\xaa \xd9\x88\xd8\xb2\xd8\xa7\xd8\xb1\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xae\xd8\xa7\xd8\xb1\xd8\xac\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xb1\xd9\x88\xd8\xb3\xd9\x8a\xd8\xa9 \xd8\xb9\xd9\x86 &quot;\xd8\xa7\xd8\xaf\xd8\xa7\xd9\x86\xd8\xaa\xd9\x87\xd8\xa7 \xd8\xa7\xd9\x84\xd8\xb4\xd8\xaf\xd9\x8a\xd8\xaf\xd8\xa9&quot; \xd9\x84\xd9\x84\xd8\xad\xd8\xa7\xd8\xaf\xd8\xab&quot; \xd9\x88\xd8\xa7\xd8\xb5\xd9\x81\xd8\xa9 \xd8\xa7\xd9\x8a\xd8\xa7\xd9\x87 \xd8\xa8\xd9\x80&quot;\xd8\xa7\xd9\x84\xd9\x85\xd8\xaa\xd8\xb7\xd8\xb1\xd9\x81&quot; \xd8\xa7\xd9\x84\xd8\xb0\xd9\x8a \xd8\xa7\xd8\xb3\xd8\xaa\xd9\x87\xd8\xaf\xd9\x81 &quot;\xd9\x85\xd8\xaf\xd9\x86\xd9\x8a\xd9\x8a\xd9\x86 \xd9\x85\xd8\xb3\xd8\xa7\xd9\x84\xd9\x85\xd9\x8a\xd9\x86&quot;.</p> 

<p>\xd9\x88\xd8\xa7\xd8\xb6\xd8\xa7\xd9\x81\xd8\xaa \xd9\x88\xd8\xb2\xd8\xa7\xd8\xb1\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xae\xd8\xa7\xd8\xb1\xd8\xac\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xb1\xd9\x88\xd8\xb3\xd9\x8a\xd8\xa9 \xd9\x81\xd9\x8a \xd8\xa8\xd9\x8a\xd8\xa7\xd9\x86\xd9\x87\xd8\xa7: &quot;\xd9\x85\xd9\x86 \xd8\xa7\xd9\x84\xd9\x85\xd8\xa4\xd8\xb3\xd9\x81 \xd8\xa7\xd9\x86 \xd9\x8a\xd8\xa3\xd8\xaa\xd9\x8a \xd9\x87\xd8\xb0\xd8\xa7 \xd8\xa7\xd9\x84\xd8\xad\xd8\xa7\xd8\xaf\xd8\xab \xd8\xa8\xd9\x8a\xd9\x86\xd9\x85\xd8\xa7 \xd8\xaa\xd8\xa8\xd8\xb0\xd9\x84 \xd8\xa7\xd9\x84\xd8\xac\xd9\x87\xd9\x88\xd8\xaf \xd9\x84\xd8\xaa\xd8\xae\xd8\xb7\xd9\x8a \xd8\xa7\xd9\x84\xd8\xa7\xd8\xb2\xd9\x85\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x81\xd9\x84\xd8\xb3\xd8\xb7\xd9\x8a\xd9\x86\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xaf\xd8\xa7\xd8\xae\xd9\x84\xd9\x8a\xd8\xa9&quot;.</p> 

<p>\xd9\x88\xd8\xaf\xd8\xb9\xd8\xaa \xd9\x85\xd9\x88\xd8\xb3\xd9\x83\xd9\x88 \xd8\xa7\xd9\x84\xd8\xb3\xd9\x84\xd8\xb7\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd9\x81\xd9\x84\xd8\xb3\xd8\xb7\xd9\x8a\xd9\x86\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x89 &quot;\xd8\xa8\xd8\xb0\xd9\x84 \xd9\x83\xd9\x84 \xd9\x85\xd8\xa7 \xd9\x8a\xd9\x85\xd9\x83\xd9\x86 \xd9\x85\xd9\x86 \xd8\xa7\xd8\xac\xd9\x84 \xd8\xa7\xd8\xad\xd8\xaa\xd9\x88\xd8\xa7\xd8\xa1 \xd9\x85\xd8\xb8\xd8\xa7\xd9\x87\xd8\xb1 \xd8\xa7\xd9\x84\xd8\xaa\xd8\xb7\xd8\xb1\xd9\x81 \xd8\xa7\xd9\x84\xd8\xaa\xd9\x8a \xd9\x84\xd8\xa7 \xd9\x85\xd8\xa8\xd8\xb1\xd8\xb1 \xd9\x84\xd9\x87\xd8\xa7 \xd9\x88\xd8\xa7\xd9\x84\xd8\xaa\xd9\x8a \xd9\x84\xd8\xa7 \xd8\xaa\xd9\x81\xd9\x8a\xd8\xaf \xd9\x85\xd8\xb5\xd8\xa7\xd9\x84\xd8\xad \xd8\xa7\xd9\x84\xd8\xb4\xd8\xb9\xd8\xa8 \xd8\xa7\xd9\x84\xd9\x81\xd9\x84\xd8\xb3\xd8\xb7\xd9\x8a\xd9\x86\xd9\x8a \xd8\xb9\xd9\x84\xd9\x89 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xaf \xd8\xa7\xd9\x84\xd8\xb7\xd9\x88\xd9\x8a\xd9\x84&quot;.</p> 

<p>\xd9\x88\xd8\xaf\xd8\xb9\xd8\xaa \xd9\x85\xd9\x88\xd8\xb3\xd9\x83\xd9\x88 \xd8\xa7\xd9\x84\xd8\xb3\xd9\x84\xd8\xb7\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd9\x81\xd9\x84\xd8\xb3\xd8\xb7\xd9\x8a\xd9\x86\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x89 &quot;\xd8\xa8\xd8\xb0\xd9\x84 \xd9\x83\xd9\x84 \xd9\x85\xd8\xa7  \xd9\x88\xd8\xaf\xd8\xb9\xd8\xaa \xd9\x85\xd9\x88\xd8\xb3\xd9\x83\xd9\x88 \xd8\xa7\xd9\x84\xd8\xb3\xd9\x84\xd8\xb7\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd9\x81\xd9\x84\xd8\xb3\xd8\xb7\xd9\x8a\xd9\x86\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x89 &quot;\xd8\xa8\xd8\xb0\xd9\x84 \xd9\x83\xd9\x84 </p> 
\xd9\x88\xd8\xaf\xd8\xb9\xd8\xaa \xd9\x85\xd9\x88\xd8\xb3\xd9\x83\xd9\x88 \xd8\xa7\xd9\x84\xd8\xb3\xd9\x84\xd8\xb7\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd9\x81\xd9\x84\xd8\xb3\xd8\xb7\xd9\x8a\xd9\x86\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x89 \xd9\x88\xd8\xaf\xd8\xb9\xd8\xaa \xd9\x85\xd9\x88\xd8\xb3\xd9\x83\xd9\x88 \xd8\xa7\xd9\x84\xd8\xb3\xd9\x84\xd8\xb7\xd8\xa7\xd8\xaa \xd8\xa7\xd9\x84\xd9\x81\xd9\x84\xd8\xb3\xd8\xb7\xd9\x8a\xd9\x86\xd9\x8a\xd8\xa9 \xd8\xa7\xd9\x84\xd9\x89 &quot;\xd8\xa8\xd8\xb0\xd9\x84 \xd9\x83\xd9\x84 \xd9\x85\xd8\xa7 \xd9\x8a\xd9\x85\xd9\x83\xd9\x86 \xd9\x85\xd9\x86 \xd8\xa7\xd8\xac\xd9\x84</p>  


<p>\xd9\x83\xd9\x85\xd8\xa7 \xd8\xa7\xd8\xaf\xd8\xa7\xd9\x86 \xd8\xa7\xd9\x84\xd9\x81\xd8\xa7\xd8\xb1\xd9\x88 \xd8\xaf\xd9\x8a \xd8\xb3\xd9\x88\xd8\xaa\xd9\x88 \xd9\x85\xd8\xa8\xd8\xb9\xd9\x88\xd8\xab \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd9\x85 \xd8\xa7\xd9\x84\xd9\x85\xd8\xaa\xd8\xad\xd8\xaf\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xae\xd8\xa7\xd8\xb5 \xd8\xa7\xd9\x84\xd9\x89 \xd8\xa7\xd9\x84\xd8\xb4\xd8\xb1\xd9\x82 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x88\xd8\xb3\xd8\xb7 \xd8\xa7\xd9\x84\xd8\xb9\xd9\x85\xd9\x84\xd9\x8a\xd8\xa9 \xd9\x88\xd9\x82\xd8\xa7\xd9\x84 &quot;\xd8\xa7\xd9\x86\xd9\x87 \xd9\x83\xd8\xa7\xd9\x86 \xd9\x87\xd8\xac\xd9\x88\xd9\x85\xd8\xa7 \xd8\xb9\xd9\x84\xd9\x89 \xd8\xa7\xd8\xb4\xd8\xae\xd8\xa7\xd8\xb5 \xd8\xb9\xd8\xa7\xd8\xaf\xd9\x8a\xd9\x8a\xd9\x86 \xd9\x83\xd8\xa7\xd9\x86\xd9\x88\xd8\xa7 \xd9\x8a\xd9\x82\xd9\x88\xd9\x85\xd9\x88\xd9\x86 \xd8\xa8\xd9\x86\xd8\xb4\xd8\xa7\xd8\xb7\xd9\x87\xd9\x85 \xd8\xa7\xd9\x84\xd9\x8a\xd9\x88\xd9\x85\xd9\x8a \xd9\x88\xd9\x87\xd8\xb0\xd8\xa7 \xd8\xa7\xd9\x85\xd8\xb1 \xd9\x84\xd8\xa7 \xd9\x8a\xd9\x85\xd9\x83\xd9\x86 \xd8\xaa\xd8\xa8\xd8\xb1\xd9\x8a\xd8\xb1\xd9\x87&quot;.</p>

<h4>Farsi / Persian (fa)</h4>
<p>\xd9\x85\xd8\xad\xd9\x85\xd8\xaf \xd8\xa7\xd9\x84\xd8\xa8\xd8\xb1\xd8\xa7\xd8\xaf\xd8\xb9\xdb\x8c \xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3 \xd8\xa2\xda\x98\xd8\xa7\xd9\x86\xd8\xb3 \xd8\xa8\xd9\x8a\xd9\x86 \xd8\xa7\xd9\x84\xd9\x85\xd9\x84\xd9\x84\xdb\x8c \xd8\xa7\xd9\x86\xd8\xb1\xda\x98\xdb\x8c \xd8\xa7\xd8\xaa\xd9\x85\xdb\x8c \xd9\xbe\xd9\x8a\xd8\xb4\xd9\x86\xd9\x87\xd8\xa7\xd8\xaf \xda\xa9\xd8\xb1\xd8\xaf\xd9\x87 \xd8\xa7\xd8\xb3\xd8\xaa \xd8\xaa\xd9\x87\xd8\xb1\xd8\xa7\xd9\x86 \xd8\xa8\xd8\xb1\xd9\x86\xd8\xa7\xd9\x85\xd9\x87 \xd8\xac\xd9\x86\xd8\xac\xd8\xa7\xd9\x84\xdb\x8c \xd8\xba\xd9\x86\xdb\x8c \xd8\xb3\xd8\xa7\xd8\xb2\xdb\x8c \xd8\xa7\xd9\x88\xd8\xb1\xd8\xa7\xd9\x86\xd9\x8a\xd9\x88\xd9\x85 \xd8\xb1\xd8\xa7 \xd9\x85\xd8\xaa\xd9\x88\xd9\x82\xd9\x81 \xda\xa9\xd9\x86\xd8\xaf \xd9\x88 \xd8\xba\xd8\xb1\xd8\xa8 \xd9\x86\xd9\x8a\xd8\xb2 \xd8\xa7\xd8\xac\xd8\xb1\xd8\xa7\xdb\x8c \xd8\xaa\xd8\xad\xd8\xb1\xd9\x8a\xd9\x85 \xd9\x87\xd8\xa7\xdb\x8c \xd8\xaa\xd9\x86\xd8\xa8\xd9\x8a\xd9\x87\xdb\x8c \xd9\x85\xd9\x88\xd8\xb1\xd8\xaf \xd8\xaa\xd8\xa7\xd8\xa6\xd9\x8a\xd8\xaf \xd8\xb3\xd8\xa7\xd8\xb2\xd9\x85\xd8\xa7\xd9\x86 \xd9\x85\xd9\x84\xd9\x84 \xd9\x85\xd8\xaa\xd8\xad\xd8\xaf \xd8\xb1\xd8\xa7 \xd8\xa8\xd9\x87 \xd8\xaa\xd8\xb9\xd9\x88\xd9\x8a\xd9\x82 \xd8\xa8\xd9\x8a\xd8\xa7\xd9\x86\xd8\xaf\xd8\xa7\xd8\xb2\xd8\xaf.</p> 

<p>\xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4\xd8\x8c \xd8\xaf\xd8\xb1 \xda\x86\xd9\x87\xd8\xa7\xd8\xb1\xd9\x85\xdb\x8c\xd9\x86 \xd8\xb3\xd8\xa7\xd9\x84\xda\xaf\xd8\xb1\xd8\xaf \xd8\xa7\xd8\xb4\xd8\xba\xd8\xa7\xd9\x84 \xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82 \xd9\x85\xdb\x8c \xda\xaf\xd9\x88\xdb\x8c\xd8\xaf \xd8\xa7\xd8\xb3\xd8\xaa\xd8\xb1\xd8\xa7\xd8\xaa\xda\x98\xdb\x8c \xd8\xa7\xd8\xb3\xd8\xaa\xd9\x82\xd8\xb1\xd8\xa7\xd8\xb1 \xd9\x86\xdb\x8c\xd8\xb1\xd9\x88\xd9\x87\xd8\xa7\xdb\x8c \xd8\xa2\xd9\x85\xd8\xb1\xdb\x8c\xda\xa9\xd8\xa7\xdb\x8c\xdb\x8c \xd8\xa8\xdb\x8c\xd8\xb4\xd8\xaa\xd8\xb1\xdb\x8c \xd8\xaf\xd8\xb1 \xd8\xa8\xd8\xba\xd8\xaf\xd8\xa7\xd8\xaf\xd8\x8c \xd9\x85\xd8\xaf\xd8\xaa\xdb\x8c \xd8\xb7\xd9\x88\xd9\x84 \xd8\xae\xd9\x88\xd8\xa7\xd9\x87\xd8\xaf \xda\xa9\xd8\xb4\xdb\x8c\xd8\xaf.</p>

<p>\xd8\xa2\xd9\x85\xd8\xb1\xdb\x8c\xda\xa9\xd8\xa7 \xd9\x88\xdb\x8c\xd8\xb2\xd8\xa7\xdb\x8c \xd8\xb1\xd8\xa6\xdb\x8c\xd8\xb3 \xd8\xac\xd9\x85\xd9\x87\xd9\x88\xd8\xb1 \xd8\xa7\xdb\x8c\xd8\xb1\xd8\xa7\xd9\x86 \xd8\xb1\xd8\xa7 \xd8\xa8\xd9\x87 \xd9\x85\xd9\x86\xd8\xb8\xd9\x88\xd8\xb1 \xd8\xad\xd8\xb6\xd9\x88\xd8\xb1 \xd9\x88\xdb\x8c \xd8\xaf\xd8\xb1 \xd8\xac\xd9\x84\xd8\xb3\xd9\x87 \xd8\xb1\xd8\xa7\xdb\x8c \xda\xaf\xdb\x8c\xd8\xb1\xdb\x8c \xd8\xb4\xd9\x88\xd8\xb1\xd8\xa7\xdb\x8c \xd8\xa7\xd9\x85\xd9\x86\xdb\x8c\xd8\xaa \xd8\xa8\xd8\xb1\xd8\xa7\xdb\x8c \xd9\x82\xd8\xb7\xd8\xb9\xd9\x86\xd8\xa7\xd9\x85\xd9\x87 \xd8\xaa\xd8\xa7\xd8\xb2\xd9\x87 \xd8\xb9\xd9\x84\xdb\x8c\xd9\x87 \xd8\xa7\xdb\x8c\xd9\x86 \xda\xa9\xd8\xb4\xd9\x88\xd8\xb1 \xd8\xb5\xd8\xa7\xd8\xaf\xd8\xb1 \xda\xa9\xd8\xb1\xd8\xaf.</p>


<h4>Urdu</h4>
<p lang=\"ur\">\xd8\xac\xd8\xb3\xd9\xb9\xd8\xb3 \xd8\xa7\xd9\x81\xd8\xaa\xd8\xae\xd8\xa7\xd8\xb1 \xda\xa9\xdb\x8c \xd8\xac\xd8\xa8\xd8\xb1\xdb\x8c \xd8\xb1\xd8\xae\xd8\xb5\xd8\xaa \xd9\xbe\xd8\xb1 \xd9\x84\xd8\xa7\xdb\x81\xd9\x88\xd8\xb1\xdb\x81\xd8\xa7\xd8\xa6\xdb\x8c \xda\xa9\xd9\x88\xd8\xb1\xd9\xb9 \xda\xa9\xdb\x92 \xd8\xa7\xdb\x8c\xda\xa9 \xd8\xa7\xd9\x88\xd8\xb1 \xd8\xb3\xd9\x86\xd8\xaf\xda\xbe \xd9\x85\xdb\x8c\xda\xba \xda\xa9\xd8\xa6\xdb\x8c \xd8\xb3\xd9\x88\xd9\x84 \xd8\xac\xd8\xac \xd9\x85\xd8\xb3\xd8\xaa\xd8\xb9\xd9\x81\xdb\x8c \xdb\x81\xd9\x88\xda\xaf\xd8\xa6\xdb\x92 \xdb\x81\xdb\x8c\xda\xba\xdb\x94</p>

<p lang=\"ur\">\xda\x86\xdb\x8c\xd9\x81 \xd8\xac\xd8\xb3\xd9\xb9\xd8\xb3 \xda\xa9\xdb\x8c \xd8\xb3\xd8\xb1\xda\xaf\xd8\xb1\xd9\x85\xdb\x8c\xd8\xa7\xda\xba \xd9\x85\xd8\xad\xd8\xaf\xd9\x88\xd8\xaf \xda\xa9\xd8\xb1\xd9\x86\xdb\x92 \xd8\xa7\xd9\x88\xd8\xb1 \xd9\xbe\xd9\x88\xd9\x84\xdb\x8c\xd8\xb3 \xd8\xaa\xd8\xb9\xdb\x8c\xd9\x86\xd8\xa7\xd8\xaa\xdb\x8c \xda\xa9\xdb\x92 \xd8\xad\xda\xa9\xd9\x85 \xd9\x86\xd8\xa7\xd9\x85\xdb\x92 \xd9\xbe\xd8\xb1 \xd8\xaf\xd8\xb3\xd8\xaa\xd8\xae\xd8\xb7 \xda\xa9\xd8\xb1\xda\xa9\xdb\x92 \xd8\xba\xd9\x84\xd8\xb7 \xda\xa9\xdb\x8c\xd8\xa7: \xd8\xac\xd9\x86\xd8\xb1\xd9\x84 \xd9\x85\xd8\xb4\xd8\xb1\xd9\x81</p>

<h4>Pashto (ps)</h4>
<p lang=\"ps\">\xd9\xbe\xd9\x87 \xda\xa9\xd8\xa7\xd8\xa8\xd9\x84 \xd8\xa7\xd9\x88 \xda\xa9\xd9\x86\xd8\xaf\xd9\x87\xd8\xa7\xd8\xb1 \xda\xa9\xdb\x90 \xd8\xaf\xd9\x88\xd9\x88 \xda\x81\xd8\xa7\xd9\x86\xd9\x85\xd8\xb1\xda\xaf\xd9\x88 \xd8\xa8\xd8\xb1\xd9\x8a\xd8\xaf\xd9\x88\xd9\x86\xd9\x88 \xd9\x84\xda\x96 \xd8\xaa\xd8\xb1 \xd9\x84\xda\x96\xd9\x87 \xd9\x8a\xd9\x88 \xd9\x85\xd8\xa7\xd8\xb4\xd9\x88\xd9\x85 \xd9\x88\xda\x98\xd9\x84\xd9\x89 \xd8\xa7\xd9\x88 \xd8\xa7\xd8\xaa\xd9\x87 \xd8\xaa\xd9\x86\xd9\x87 \xd9\x86\xd9\x88\xd8\xb1 \xd9\x89\xdb\x90 \xd9\xbc\xd9\xbe\xd9\x8a\xd8\xa7\xd9\x86 \xda\xa9\xda\x93\xd9\x8a.</p>

<p lang=\"ps\">\xd9\x87 \xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82 \xda\xa9\xdb\x90 \xd9\x8a\xd9\x88\xd9\x87 \xd8\xaa\xd8\xa7\xd8\xb2\xd9\x87 \xd9\x86\xd8\xb8\xd8\xb1 \xd8\xb4\xd9\x85\xdb\x90\xd8\xb1\xd9\x86\xd9\x87 \xda\x9a\xd9\x8a\xd9\x8a \xda\x86\xdb\x90 \xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82\xd9\x8a\xd8\xa7\xd9\x86 \xd9\xbe\xd9\x87 \xd8\xb2\xd9\x8a\xd8\xa7\xd8\xaa\xdb\x90\xd8\xaf\xd9\x88\xd9\x86\xda\xa9\xd9\x8a \xd8\xaa\xd9\x88\xda\xaf\xd9\x87 \xd8\xaf \xd8\xad\xd8\xa7\xd9\x84\xd8\xa7\xd8\xaa\xd9\x88 \xd9\xbe\xd9\x87 \xd8\xa7\xda\x93\xd9\x87 \xd8\xa8\xd8\xaf\xd8\xa8\xd9\x8a\xd9\x86\xd9\x87 \xd8\xaf\xd9\x8a \xd8\xa7\xd9\x88 \xd8\xaf \xd8\xa8\xdb\x90 \xd8\xa8\xd8\xa7\xd9\x88\xd8\xb1\xd9\x8a \xd8\xa7\xd8\xad\xd8\xb3\xd8\xa7\xd8\xb3 \xda\xa9\xd9\x88\xd9\x8a\xd8\x8c \xd8\xae\xd9\x88 \xd8\xae\xd9\xbe\xd9\x84 \xd9\x87\xd9\x8a\xd9\x88\xd8\xa7\xd8\xaf \xd9\x8a\xd9\x88\xd9\x85\xd9\x88\xd9\xbc\xd9\x89 \xd8\xba\xd9\x88\xd8\xa7\xda\x93\xd9\x8a.</p>
 

<h4>Symbols</h4>
<p>\xc2\xa9\xc2\xae\xe2\x84\xa2\xc2\xb5\xe2\x80\xa2\xe2\x80\xa6\xe2\x80\xb2\xe2\x80\xb3\xc2\xa7&lt;&gt;\xe2\x89\xa4\xe2\x89\xa5\xc2\xb0\xe2\x88\x92\xc2\xb1\xc3\xb7\xe2\x81\x84\xc3\x97\xc6\x92\xe2\x88\xab\xe2\x88\x91\xe2\x88\x9e\xe2\x88\x9a\xe2\x89\x88\xe2\x89\xa0\xe2\x89\xa1\xe2\x88\x8f\xc2\xac\xe2\x88\xa9\xe2\x88\x82</p>
<p>\xce\x92\xce\x93\xce\x94\xce\x95\xce\x96\xce\x97\xce\x98\xce\x99\xce\x9a\xce\x9b\xce\x9c\xce\x9d\xce\x9e\xce\x9f\xce\xa0\xce\xa1\xce\xa3\xce\xa4\xce\xa5\xce\xa6\xce\xa7\xce\xa8\xce\xa9</p>
<p>\xce\xb1\xce\xb2\xce\xb3\xce\xb4\xce\xb5\xce\xb6\xce\xb7\xce\xb8\xce\xb9\xce\xba\xce\xbb\xce\xbc\xce\xbd\xce\xbe\xce\xbf\xcf\x80\xcf\x81\xcf\x82\xcf\x83\xcf\x84\xcf\x85\xcf\x86\xcf\x87\xcf\x88\xcf\x89</p>
<p>\xe2\x86\x90\xe2\x86\x91\xe2\x86\x92\xe2\x86\x93\xe2\x86\x94\xe2\x97\x8a\xe2\x99\xa3\xe2\x99\xa5\xe2\x99\xa6</p>

<h4>Dingbats</h4>
<p>\xc2\xa7&lt;&gt;\xe2\x86\x92\xe2\x86\x94\xe2\x99\xa3\xe2\x99\xa5\xe2\x99\xa6</p>

<h4>win-1252 </h4>
<p>\xc2\xa2\xe2\x82\xac\xc2\xa9\xc2\xae\xe2\x84\xa2\xe2\x80\xb0\xc2\xb5\xc2\xb7\xe2\x80\xa2\xe2\x80\xa6\xc2\xa7\xc3\x9f\xe2\x80\xb9\xe2\x80\xba\xc2\xab\xc2\xbb\xe2\x80\x98\xe2\x80\x99\xe2\x80\x9c\xe2\x80\x9d\xe2\x80\x9a\xe2\x80\x9e&lt;&gt;\xe2\x80\x93\xe2\x80\x94\xcb\x86\xcb\x9c\xc2\xb0\xc2\xb1\xc3\xb7\xc3\x97\xc2\xbc\xc2\xbd\xc2\xbe\xc6\x92\xc2\xac\xe2\x80\xa0\xe2\x80\xa1</p>
<p>\xc3\x80\xc3\x81\xc3\x82\xc3\x83\xc3\x84\xc3\x85\xc3\x86\xc3\x87\xc3\x88\xc3\x89\xc3\x8a\xc3\x8b\xc3\x8c\xc3\x8d\xc3\x8e\xc3\x8f\xc3\x90\xc3\x91\xc3\x92\xc3\x93\xc3\x94\xc3\x95\xc3\x96\xc3\x98\xc5\x92\xc5\xa0\xc3\x99\xc3\x9a\xc3\x9b\xc3\x9c\xc3\x9d\xc5\xb8</p>
<p>\xc3\xa0\xc3\xa1\xc3\xa2\xc3\xa3\xc3\xa4\xc3\xa5\xc3\xa6\xc3\xa7\xc3\xa8\xc3\xa9\xc3\xaa\xc3\xab\xc3\xac\xc3\xad\xc3\xae\xc3\xaf\xc3\xb0\xc3\xb1\xc3\xb2\xc3\xb4\xc3\xb5\xc3\xb6\xc3\xb8\xc5\x93\xc5\xa1\xc3\xb9\xc3\xba\xc3\xbb\xc3\xbc\xc3\xbd\xc3\xbe\xc3\xbf</p>

<h3>Bidirectional text</h3>
<p>Text alignment, unless specified, is neutral and therefore dictated by the 'direction' of the paragraph.</p>

<p>All text is analysed chunk by chunk (between tags) and at the end of every block (div, p, td). If the text contains RTL characters, those characters and words are reversed.</p>

<p>\xd9\x88\xd8\xa7\xd8\xaf\xd8\xa7\xd9\x86 \xd8\xa7\xd9\x84\xd8\xa8\xd9\x8a\xd8\xaa \xd8\xa7\xd9\x84\xd8\xa7\xd8\xa8\xd9\x8a\xd8\xb6 &quot;\xd8\xa8\xd8\xb4\xd8\xaf\xd8\xa9&quot; \xd8\xaa\xd9\x81\xd8\xac\xd9\x8a\xd8\xb1 with some english in the middle \xd8\xa7\xd9\x8a\xd9\x84\xd8\xa7\xd8\xaa \xd9\x81\xd9\x8a\xd9\x85\xd8\xa7 \xd8\xa7\xd8\xb9\xd8\xb1\xd8\xa8\xd8\xaa \xd9\x88\xd8\xb2\xd8\xa7\xd8\xb1\xd8\xa9</p>

<p>To set the 'directionality' of the whole document e.g. to reverse default alignment, tables, lists etc. you can set the dir attribute or the direction CSS property on the HTML or BODY tag to 'rtl' e.g.</p>
<p>&lt;body style=\"direction: rtl\"&gt;</p>
<p>&lt;body dir=\"rtl\"&gt;</p>
<p style=\"text-align: right\">or you can use \$mpdf->SetDirectionality('rtl');</p>

<pre style=\"direction: ltr; background-color: #DDFFFF; page-break-inside: avoid;\">
- the document now has a baseline direction; this determines the 
	- behaviour of blocks for which text-align has not been specifically set
	- layout of mirrored page-margins, columns, ToC and Indexes, headers and footers
	- base direction can be set by any of:
		- \$mpdf-&gt;SetDirectionality('rtl');
		- &lt;html dir=\"rtl\" or style=\"direction: rtl;\"&gt;
		- &lt;body dir=\"rtl\" or style=\"direction: rtl;\"&gt;
	- base direction is an inherited CSS property, so will affect all content, unless...
- direction can be set for all HTML block elements e.g. &lt;DIV&gt;&lt;P&gt;&lt;TABLE&gt;&lt;UL&gt; etc using
	- CSS property &lt; style=\"direction: rtl;\"&gt; 
	- direction can only be set on the top-level element of nested lists
	- direction can only be set on &lt;TABLE&gt;, NOT on THEAD, TBODY, TD etc.
	- nested tables CAN have different directions
- NOTE that block/table margins/paddings are NOT reversed by direction
	NB mPDF &lt;5.1 reversed the margins/paddings for blocks when RTL set.
- language (either CSS \"lang\", using Autofont, or through initial set-up e.g. \$mpdf = new mPDF('ar') ) 
	no longer affects direction in any way.
	NB config_cp.php has been changed as a result; any values of \"dir\" set here are now ineffective
- default text-align is now as per CSS spec: \"a nameless value which is dependent on direction\" 
	NB default text-align removed in default stylesheet in config.php 
- once text-align is specified, it is respected and inherited
	NB mPDF &lt;5.1 reversed the text-align property for all blocks when RTL set.
- the configurable value $rtlcss is depracated, as it is no longer required
- improved algorithm for dtermining text direction
	- english word blocks are handled in text reversal as one block i.e. dir=\"rtl\"
	[arabic text] this will not be reversed [arabic text]
	- arabic numerals 0-9 handled correctly

Although the control of direction for block elements is now more configurable, the control of 
text direction (RTL arabic characters) remains fully automatic and unconfigurable. 
&lt;BDO&gt; etc has no effect. Enclosing text in silent tags can sometimes help e.g.
	content&lt;span&gt;[arabic text]&lt;/span&gt;content

</pre>

<pagebreak />
<h3>Tables</h3>
<p>Tables are automatically transposed when the direction is rtl:</p>
<table class=\"bpmTopicC\"><thead>
<tr class=\"headerrow\"><th>\xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a</th>
<td>
<p>\xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a</p>
</td>
<td>\xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a</td>
</tr>
</thead><tbody>
<tr class=\"oddrow\"><th>\xd9\x82\xd8\xa7\xd9\x84</th>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
</tr>
<tr class=\"evenrow\"><th>\xd9\x82\xd8\xa7\xd9\x84</th>
<td>
<p>\xd9\x82\xd8\xa7\xd9\x84 \xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xad\xd8\xaf\xd9\x8a\xd8\xab \xd9\x85\xd8\xaa\xd9\x84\xd9\x81\xd8\xb2</p>
</td>
<td>
<p>\xd9\x82\xd8\xa7\xd9\x84 \xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xad\xd8\xaf\xd9\x8a\xd8\xab \xd9\x85\xd8\xaa\xd9\x84\xd9\x81\xd8\xb2</p>
</td>
</tr>
<tr class=\"oddrow\"><th>
<p>\xd9\x82\xd8\xa7\xd9\x84</p>
</th>
<td>
<p>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</p>
</td>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
</tr>
<tr class=\"evenrow\"><th>
<p>\xd9\x82\xd8\xa7\xd9\x84</p>
<p>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</p>
</th>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
<td>
<p>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</p>
</td>
</tr>
<tr class=\"oddrow\"><th>\xd9\x82\xd8\xa7\xd9\x84</th>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
</tr>
<tr class=\"evenrow\"><th>\xd9\x82\xd8\xa7\xd9\x84</th>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
</tr>
<tr class=\"oddrow\"><th>\xd9\x82\xd8\xa7\xd9\x84</th>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
</tr>
<tr class=\"evenrow\"><th>\xd9\x82\xd8\xa7\xd9\x84</th>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
<td>\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</td>
</tr>
</tbody></table>
<p>&nbsp;</p>
<h3>Lists</h3>
<p>Lists will automatically reverse as well (note the use of list-style to set numbering):</p>
<div style=\"background-color:#ddccff; padding:5pt;\">
<ol style=\"list-style-type: arabic-indic;\">
<li>\xd9\x82\xd8\xa7\xd9\x84 \xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3</li>
<li>\xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a
	<ul><li>\xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac \xd8\xa8\xd9\x88\xd8\xb4 \xd9\x81\xd9\x8a \xd8\xac\xd9\x88\xd8\xb1\xd8\xac </li>
	<li>\xd8\xad\xd8\xaf\xd9\x8a\xd8\xab \xd9\x85\xd8\xaa\xd9\x84\xd9\x81\xd8\xb2
	<ul>
	<li>\xd9\x81\xd9\x8a \xd8\xa7\xd9\x84\xd8\xb0\xd9\x83\xd8\xb1\xd9\x89 \xd8\xa7\xd9\x84\xd8\xb1\xd8\xa7\xd8\xa8\xd8\xb9\xd8\xa9</li>
	<li>\xd9\x84\xd9\x84\xd8\xba\xd8\xb2\xd9\x88 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd8\xb1\xd9\x8a\xd9\x83\xd9\x8a</li>
	</ul>
	</li>
</ul></li>
<li>\xd9\x84\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82 \xd8\xa7\xd9\x86 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x88\xd9\x84\xd9\x88\xd9\x8a\xd8\xa9 \xd8\xad\xd8\xa7\xd9\x84\xd9\x8a\xd8\xa7</li>
<li>\xd9\x84\xd8\xa7\xd8\xb9\xd8\xa7\xd8\xaf\xd8\xa9 \xd8\xa7\xd9\x84\xd8\xa7\xd9\x85\xd9\x86 \xd9\x84\xd9\x84\xd8\xb9\xd8\xb1\xd8\xa7\xd9\x82</li>
</ol>
</div>
";

//==============================================================
	// Set Header and Footer
	$h = array (
  'odd' => 
  array (
    'R' => 
    array (
      'content' => '{PAGENO}',
      'font-size' => 8,
      'font-style' => 'B',
    ),
    'L' => 
    array (
      'content' => "\xd9\x82\xd8\xa7\xd9\x84 \xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3",
      'font-size' => 8,
      'font-style' => 'B',
    ),
    'line' => 1,
  ),
  'even' => 
  array (
    'L' => 
    array (
      'content' => '{PAGENO}',
      'font-size' => 8,
      'font-style' => 'B',
    ),
    'R' => 
    array (
      'content' => "\xd9\x82\xd8\xa7\xd9\x84 \xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3",
      'font-size' => 8,
      'font-style' => 'B',
    ),
    'line' => 1,
  ),
);

	$f = array (
  'odd' => 
  array (
    'L' => 
    array (
      'content' => '{DATE Y-m-d}',
      'font-size' => 8,
      'font-style' => 'BI',
    ),
    'C' => 
    array (
      'content' => '- {PAGENO} -',
      'font-size' => 8,
    ),
    'R' => 
    array (
      'content' => "\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3",
      'font-size' => 8,
    ),
    'line' => 1,
  ),
  'even' => 
  array (
    'L' => 
    array (
      'content' => "\xd8\xa7\xd9\x84\xd8\xb1\xd8\xa6\xd9\x8a\xd8\xb3",
      'font-size' => 8,
      'font-style' => 'B',
    ),
    'C' => 
    array (
      'content' => '- {PAGENO} -',
      'font-size' => 8,
    ),
    'R' => 
    array (
      'content' => '{DATE Y-m-d}',
      'font-size' => 8,
      'font-style' => 'BI',
    ),
    'line' => 1,
  ),
);

//==============================================================
//==============================================================
//==============================================================
include("../mpdf.php");


$mpdf=new mPDF('ar','A4','','',32,25,27,25,16,13); 

// From mPDF 5.1 onwards you must set:
$mpdf->SetDirectionality('rtl');
$mpdf->mirrorMargins = true;
$mpdf->SetDisplayMode('fullpage','two');


$mpdf->setHeader($h);
$mpdf->setFooter($f);


$stylesheet = file_get_contents('mpdfstyletables.css');
$mpdf->WriteHTML($stylesheet,1);	// The parameter 1 tells that this is css/style only and no body/html/text

$mpdf->WriteHTML($html);
$mpdf->AddPage();

$mpdf->SetColumns(2,'J');
$mpdf->WriteHTML($html);

$mpdf->Output();
exit;
//==============================================================
//==============================================================
//==============================================================


?>