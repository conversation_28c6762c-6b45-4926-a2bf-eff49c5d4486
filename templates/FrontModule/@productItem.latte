<div class="col-xs-6 col-sm-6 col-md-4 col-lg-4 product__wrapper">

  <article class="product" role="article">

    <h2 class="product__header">
      <a href="{plink Product:detail, $product->proid, ($product|getProKey)}" title="{$product->proname}">{$product->proname}</a>
    </h2>

    <div class="product__image">

      {* <!-- štítky --> *}
      {include @productLabel.latte, product => $product}
      {* <!-- štítky --> *}

      <a href="{plink Product:detail, $product->proid, ($product|getProKey)}">
        <img src="{$baseUri}/img/no.jpg" data-src="{$baseUri}/{($product|getProductPicName:'list')|noescape}" alt="{$product->proname}">
      </a>

    </div>

    <div class="product__info">
      {$product->prodescs|truncate:120}
    </div>

    <div class="product__price">

      {if $product->proismaster!=1}<strong>{$product->proprice|formatPrice}</strong> {_'včetně DPH'}{/if}

      {if $product->proaccess == 0}
        <br><span class="stock stock--available">Skladem</span>
      {elseif !empty($product->proaccesstext)}
        <br><span class="stock stock--available">{$product->proaccesstext}</span>
      {else}
        <br><span class="stock stock--unavailable">Pouze na objednání</span>
      {/if}

    </div>

    <div class="product__controls">
      <a href="{plink Product:detail, $product->proid, ($product|getProKey)}" class="btn btn--small">{_'Detail'}</a>
      <a href="{plink Product:detail, $product->proid, ($product|getProKey)}" class="btn btn--small btn--buy">{_'Koupit'}</a>
    </div>

  </article>

</div>
