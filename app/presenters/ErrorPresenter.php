<?php

/**
 * My Application
 *
 * @copyright  Copyright (c) 2010 <PERSON>
 * @package    MyApplication
 */



/**
 * Error presenter.
 *
 * <AUTHOR>
 * @package    MyApplication
 */
class ErrorPresenter extends BasePresenter
{

	/**
	 * @param  Exception
	 * @return void
	 */
	public function renderDefault($exception) {
		if ($this->isAjax()) { // AJAX request? Just note this error in payload.
			$this->payload->error = TRUE;
			$this->terminate();

		} elseif ($exception instanceof Nette\Application\BadRequestException) {
			$this->setView('404'); // load template 404.latte

		} else {
			$this->setView('500'); // load template 500.latte
			Nette\Diagnostics\Debugger::log($exception, Nette\Diagnostics\Debugger::ERROR); // and handle error by Nette\Debug
		}
	}

  /*
  protected function shutdown($response) {
    $exception = $this->params['exception'];
    NDebug::processException($exception); // pozor, volá exit()
  }
  */
}
