{var $title = $mailing->mamsubject.' - statistika'}

{block #content}
 <h4>Základní statistika</h4>
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Datum</th>
    <td>{$mailing->mamdate|date:'d.m.Y'}</td>
  </tr><tr>
    <th><PERSON><PERSON></th>
    <td>{$mailing->mamdatestart|date:'d.m.Y H:m:i'}</td>
  </tr><tr>
    <th><PERSON><PERSON> dokon<PERSON></th>
    <td>{$mailing->mamdateend|date:'d.m.Y H:m:i'}</td>
  </tr><tr>
    <th>Zobrazení</th>
    <td>{$mailing->mamviews}</td>
  </tr><tr>
    <th>Kliky</th>
    <td>{$mailing->mamclicks}</td>
  </tr><tr>
    <th><PERSON><PERSON><PERSON> emailů</th>
    <td>{$mailing->mamcntall}</td>
  </tr><tr>
    <th><PERSON>býv<PERSON> odeslat</th>
    <td>{$mailing->mamleft}</td>
  </tr><tr>
    <th>Status</th>
    <td>{$enum_mamstatus[$mailing->mamstatus]}</td>
  </tr><tr>
    <th></th>
    <td>
    <a href="{plink Mailing:edit, $mailing->mamid}">{('edit'|glyph)|noescape}</a>
    <a href="{plink :Front:Mailing:detail, $mailing->mamid, 1, 'b929b889afd416e8ae24532402f8d159'}">{('info'|glyph)|noescape}</a>
    </td>
  </tr>
  </table>
 {php $sumAllSale = 0 }
 <h4>Statistika kliků na zboží a prodej tohoto zboží (do 30-ti dnů od kliknutí)</h4>
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Kód zboží</th>
    <th>Název zboží</th>
    <th>Kliky</th>
    <th>Prodej cena</th>
    <th>Prodej kusy</th>
    <th>Objednávka | Přikoupil za</th>
  </tr>
  {php
      $oripriceSum = 0;
      $ordpriceSumOtherSale = 0;
      $oriqtySum = 0;
      $clicksSum = 0;
  }
  {foreach $statsProducts as $row}
    {php
      $oriprice = 0;
      $oriqty = 0;
      if (isset($statsProductsSale[$row->proid])) {
        $oriprice = $statsProductsSale[$row->proid]["oriprice"];
        $oriqty = $statsProductsSale[$row->proid]["oriqty"];
      }
      $oripriceSum += $oriprice;
      $oriqtySum += $oriqty;
      $clicksSum += $row->clicks;
    }
    <tr>
      <td><a href="{plink Product:edit, $row->proid}">{$row->procode}</a></td>
      <td>{$row->proname}</td>
      <td>{$row->clicks}</td>
      <td style="text-align: right;">{$oriprice|formatPrice}</td>
      <td style="text-align: right;">{$oriqty}</td>
      <td style="text-align: right;">{if isset($statsProductsSale[$row->proid]["orders"])}
        {foreach $statsProductsSale[$row->proid]["orders"] as $oi}
          {php $ordpriceSumOtherSale += $oi["ordpricevat_other"]; }
          {if $iterator->isFirst()}
          <table width="100%" border="0">
          {/if}
          <tr>
            <td width="65"><a href="{plink Order:edit, $oi["ordid"]}">{$oi["ordcode"]}</a></td>
            <td style="text-align: right;">{$oi["ordpricevat_other"]|formatPrice}</td>
          </tr>
          {if $iterator->isLast()}
          </table>
          {/if}
        {/foreach}
      {/if}</td>
    </tr>
  {/foreach}
    <tr>
      <th></th>
      <th>Celkem</th>
      <th>{$clicksSum}</th>
      <th style="text-align: right;">{$oripriceSum|formatPrice}</th>
      <th style="text-align: right;">{$oriqtySum}</th>
      <th style="text-align: right;">{$ordpriceSumOtherSale|formatPrice}</th>
    </tr>
  </table>
  {php $sumAllSale = $sumAllSale + $oripriceSum + $ordpriceSumOtherSale ;}
  {php $ordpriceSumOtherSale = 0 }
  {foreach $ordersOtherSale as $row}
    {php $ordpriceSumOtherSale += $row->ordpricevat }
    {if $iterator->isFirst()}
    <h4>Ostatní prodej (na něco kliknul a něco koupil do 30-ti dnů)</h4>
    <table class="table table-condensed table-hover table-bordered">
      <tr>
      <th>Kód obj.</th>
      <th>Cena</th>
      </tr>
    {/if}
    <tr>
    <td><a href="{plink Order:edit, $row->ordid}">{$row->ordcode}</a></td>
    <td style="text-align: right;">{$row->ordpricevat|formatPrice}</td>

    </tr>
    {if $iterator->isLast()}
      <tr>
      <th>Celkem</th>
      <th style="text-align: right;">{$ordpriceSumOtherSale|formatPrice}</th>
      </tr>
    </table>
    {/if}
  {/foreach}
  {php $sumAllSale = $sumAllSale + $ordpriceSumOtherSale ;}

  <h3>Celkový obrat: {$sumAllSale|formatPrice}</h3>

  <h4>Statistika kliků podle typu</h4>
  <table class="table table-condensed table-hover table-bordered">
    <tr>
      <th>Typ kliku</th>
      <th>Počet</th>
    </tr>
    {foreach $statsClickTypes as $row}
    <tr>
      <td>{$enum_MasTypId[$row->mastypid]}</td>
      <td style="text-align: right;">{$row->clicks}</td>
    </tr>
    {/foreach}
  </table>

{/block}
