<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class CatalogsModel extends BaseModel {

  protected $tableName = "catalogs";
  protected $fieldPrefix = "cat";

  public function __construct() {
    parent::__construct();
    //upresneni formatu datovych poli, pokud je treba

  }

  /**
  * pregeneruje cesty ukladane v katalogu
  *
  */

  public function rebuildPaths() {
    $catalogs = new CatalogsModel();
    $rows = dibi::query("SELECT catid, catmasid, catname, catlevel,catpath,catpathids FROM catalogs")->fetchAssoc('catid');
    $cnt1 = 0;
    $cnt2 = 0;
    foreach ($rows as $row) {
      $pathstr = "";
      $pathidsstr = "";
      $path = array();
      $pathids = array();
      $pathlevel = 0;
      if ($row->catmasid > 0) {
        $cnt1++;
        $lastmasid = $row->catmasid;
        do {
          $pathlevel ++;
          $catalog = $rows[$lastmasid]; //dibi::fetch("SELECT catid, catmasid, catname FROM catalogs WHERE catid=%i", $lastmasid);
          $lastmasid = $catalog->catmasid;
          $path[$catalog->catid] = $catalog->catname;
          $pathids[$catalog->catid] = $catalog->catid;
          $cnt2++;
        } while ($lastmasid > 0);
        $path = array_reverse($path, True);
        $pathids = array_reverse($pathids, True);
        $pathstr = implode("|", $path);
        $pathidsstr = "|".implode("|", $pathids);
      }
      $pathlevel ++;
      $pathstr .= ($pathstr != "" ? "|" : "")."$row->catname";
      $pathidsstr .= "|$row->catid|";

      //updatnu prislusny catalog pokud se neco zmenilo
      if ($row->catlevel != $pathlevel || $row->catpath != $pathstr || $row->catpathids != $pathidsstr) {
        $values = array();
        $values["catlevel"] = $pathlevel;
        $values["catpath"] = $pathstr;
        $values["catpathids"] = $pathidsstr;
        $catalogs->update($row->catid, $values, false, false);
      }
    }
  }

  public function insert($data, $rebuildPaths=true) {
    $catid = parent::insert($data);
    //pregeneruju cesty
    if ($catid > 0 && $rebuildPaths) $this->rebuildPaths();
    return ($catid);
  }

  public function update($id, $data, $setDateU = Null, $rebuildPaths=true) {
    if (!empty($data["catkey"])) $data["catkey"] = Nette\Utils\Strings::webalize($data["catkey"]);
    $ret = parent::update($id, $data, $setDateU);
    //pregeneruju cesty
    if ($rebuildPaths) $this->rebuildPaths();
    return ($ret);
  }

  public function delete($id) {
    $ret = parent::delete($id);
    //vymazu prislusne zarazeni v katalogu
    dibi::query("DELETE FROM catplaces WHERE capcatid=%i", $id);
    //pregeneruju cesty
    if ($ret) $this->rebuildPaths();
    return ($ret);
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik catstatus
  * @return array
  */
  public function getEnumCatStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  /**
  * Vraci strom katalogu (vhodne pro combo box)
  * CACHED
  * @return array
  */
  public function getEnumCatalogCombo($masid=0, $onlyActive=false) {
    $cacheName = 'CatalogCombo'.(int)$onlyActive;
    $cache = $this->cacheGet($cacheName);
    if (!$cache) {
      unset($cache);
      $arr = array();
      $arr[0] = "Kořenová úroveň";
      $this->addEnumCatalogComboLevel($masid, $arr, $onlyActive);
      $this->cacheSave($cacheName, $arr);
      $cache = $arr;
    }
    return $cache;
  }

  /**
  * vraci jednu vetev katalogu
  *
  * @param integer $catmasid
  * @param array $arr
  */
  private function addEnumCatalogComboLevel($catmasid, &$arr, $onlyActive=false) {
    $res = dibi::query("SELECT * FROM catalogs WHERE catmasid=$catmasid ".($onlyActive ? ' AND catstatus=0' : '')." ORDER BY catorder,catname");

    while ($row = $res->fetch()) {
      //zjistim v jake urovni zanoreni jsem
      $lev = $row->catlevel + 1;
      $arr[$row->catid] = str_repeat('-', $lev * 2).' '.$row->catname;
      $this->addEnumCatalogComboLevel($row->catid, $arr);
    }
  }

  public function addEnumCatalogRootLevel() {
    $ret = dibi::query("SELECT catid, catname FROM catalogs WHERE catmasid=0 ORDER BY catname")->fetchPairs('catid', 'catname');
    return ($ret);
  }

  /**
  * Vraci strom katalogu
  * CACHED
  * @return array
  */
  public function getEnumCatalogTree($catmasid=0, $onlyActive=false) {
    $cacheKey = 'CatalogMenu_'.(int)($onlyActive).'_'.$catmasid;
    $data = $this->cacheGet($cacheKey);
    if ($data === false) {
      $data = $this->addEnumCatalogTreeLevel($catmasid, $onlyActive);
      $this->cacheSave($cacheKey, $data);
    }
    return $data;
  }

  /**
  * vraci jednu vetev katalogu
  *
  * @param integer $catmasid
  * @param array $arr
  */
  private function addEnumCatalogTreeLevel($catmasid, $onlyActive=false) {
    $items = dibi::query("SELECT * FROM catalogs WHERE catmasid=$catmasid ".($onlyActive ? ' AND catstatus=0' : '')." ORDER BY catorder")
      ->fetchAssoc('catid');

    $arr = array();
    foreach ($items as $key => $row) {
      $arr[$key]["data"] = $row;
      $arr[$key]["subitems"] = $this->addEnumCatalogTreeLevel($key, $onlyActive);
    }
    return $arr;
  }


  /**
  * vraci vypis stromu katalogu pro menu, jen prvni dve zanoreni
  * CACHED
  * @return array
  */
  public function getEnumCatalogMenu() {
    $cache = $this->cacheGet('CatalogMenu');
    if (!$cache) {
      unset($cache);
      $sql = "SELECT cat.catid as masid, cat.catkey as maskey, cat.catname AS masname,
cat_l12.catid AS subid, cat_l12.catkey AS subkey, cat_l12.catname AS subname
FROM catalogs AS cat
LEFT JOIN catalogs AS cat_l12 ON (cat.catid=cat_l12.catmasid)
WHERE cat.catmasid=0 AND cat.catstatus=0 AND COALESCE(cat_l12.catstatus, 0)=0
ORDER BY cat.catorder, cat_l12.catorder";
      $result = dibi::query($sql);
      $cache = $result->fetchAssoc('masid,=,subid');
      $this->cacheSave('CatalogMenu', $cache);
    }
    return $cache;
  }

  public function getEnumRootCatId() {
    return dibi::query("SELECT catid, catname FROM catalogs WHERE catmasid=0 AND catid!=50")
      ->fetchPairs('catid', 'catname');
  }

  public function getEnumUseCatId() {
    return dibi::query("SELECT catid, catname FROM catalogs WHERE catmasid=50")
      ->fetchPairs('catid', 'catname');
  }

  /**
  * vraci katalogovou cestu pro dany katalog
  *
  *
  * @param dibiRow $row
  */
  public function getCatalogPath($row) {
    $idPath = explode('|', trim($row->catpathids, '|'));
    $catalogPath = array();
    foreach ($idPath as $catid) {
      $catalogPath[$catid] = dibi::fetch("SELECT catid, catkey, catname, catparams from catalogs WHERE catid=%i", $catid);
    }
    return($catalogPath);
  }

}
