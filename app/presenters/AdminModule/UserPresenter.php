<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  <PERSON>;

final class UserPresenter extends BasePresenter {

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sMail = '';

  /** @persistent */
  public $sIc = '';

  /** @persistent */
  public $sFirName = '';

  /** @persistent */
  public $sPrcCat;

  /** @persistent */
  public $sOrderBy = 'usrilname';

  /** @persistent */
  public $sOrderByType = 'ASC';

  public function userEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $users = new \Model\UsersModel();
      $id = $this->getParameter('id');
      if ($id > 0) {
        $users->update($id, $form->getValues());
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        $users->insert($form->getValues());
        $this->flashMessage('Nový záznam ul<PERSON>ž<PERSON> v pořádku');
      }
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $users = new \Model\UsersModel();

    $where = "";
    if (!empty($this->sIc)) $where .= " usric like '$this->sIc%'";
    if (!empty($this->sName)) $where .= " (usrilname LIKE '%$this->sName%' OR  usrstlname LIKE '%$this->sName%')";
    if (!empty($this->sFirName)) $where .= " (usrifirname LIKE '%$this->sFirName%' OR  usrstfirname LIKE '%$this->sFirName%')";
    if (!empty($this->sMail)) $where .= " usrmail LIKE '%$this->sMail%'";
    if (!empty($this->sPrcCat)) $where .= " usrprccat='$this->sPrcCat'";
    if (!empty($where)) $where = " WHERE $where";


    if (empty($this->sOrderBy)) $this->sOrderBy = "usrilname";
    $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;


    $dataSource = $users->getDataSource("
      SELECT *, IF(uslid IS NULL,0,1) AS aprove
      FROM users
      LEFT JOIN users_log ON (uslusrid=usrid AND uslevtid=2)
      $where
      GROUP BY usrid
      $orderBy
    ");
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    //ciselnik statusu
    $this->template->enum_usrstatus = $users->getEnumUsrStatus();
    $this->template->enum_usrprccat = $this->getEnumPrcCat();
  }

  public function renderEdit($id) {
    $form = $this['userEditForm'];

    if (!$form->isSubmitted()) {
      $users = new \Model\UsersModel();
      $dataRow = $users->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $form->setDefaults($dataRow);

      $this->template->dataRow = $dataRow;

      //dotáhneme users_log
      $this->template->usersLog = dibi::fetchAll('SELECT * FROM users_log WHERE uslusrid=%i', $dataRow->usrid, ' ORDER BY usldatec DESC');
      $this->template->enum_uslevtid = $users->getEnumUslEvtId();
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $users = new \Model\UsersModel();
      //zjistím jestli je v nějaké obj
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(ordid) FROM orders WHERE ordusrid=%i", $id);
      if ($cnt === 0) {
        $users->delete($id);
        $this->flashMessage('Záznam byl vymazán');
      } else {
        $users->clearPersonalData($id);
        $this->flashMessage('Účet je součástí několika objednávek. Byly proto jen vymazány osobní data.');
      }
    }
    $this->redirect('default');
  }


  /********************* facilities *********************/

  protected function createComponentUserEditForm() {
    $user = new \Model\UsersModel();
    $form = $this->createAppForm();

    $form->addGroup('Změna emailu');

    $form->addText('usrmail', 'Email:', 30)
      ->setOption('description', ' slouží klientovi zároveň jako přihlašovací jméno')
      ->setEmptyValue('@')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addCheckbox("usrmaillist", "Mailovat novinky");

    $form->addText('usrdiscount', 'Sleva v %:', 30)
      ->addRule(Nette\Forms\Form::FILLED, "Sleva v procentech musí být vyplněna")
      ->addRule(Nette\Forms\Form::NUMERIC, "Sleva v procentech musí být číslo")
      ->setDefaultValue(0);

    $form->addSelect('usrprccat', 'Cenová hladina', $this->getEnumPrcCat());

    $form->addText('usrtel', 'Telefon:', 30);

    $form->addCheckbox("usrvat", "Plátce DPH");
    $form->addText('usric', 'IČ:', 30);
    $form->addText('usrdic', 'DIČ:', 30)
      ->addConditionOn($form["usrvat"], Nette\Forms\Form::EQUAL, TRUE)
         ->addRule(Nette\Forms\Form::FILLED, 'DIČ musí být vyplněno, pokud je plátce DPH');

    $form->addGroup('Adresa fakturační:');
    $form->addText('usriname', 'Jméno:', 30);
    $form->addText('usrilname', 'Příjmení:', 30);
    $form->addText('usrifirname', 'Firma:', 30);
    $form->addText('usristreet', 'Ulice:', 30);
    $form->addText('usristreetno', 'Číslo popisné:', 30);
    $form->addText('usricity', 'Město:', 30);
    $form->addText('usripostcode', 'PSČ:', 30);

    $form->addGroup('Adresa dodací:');
    $form->addText('usrstname', 'Jméno:', 30);
    $form->addText('usrstlname', 'Příjmení:', 30);
    $form->addText('usrstfirname', 'Firma:', 30);
    $form->addText('usrststreet', 'Ulice:', 30);
    $form->addText('usrststreetno', 'Číslo popisné:', 30);
    $form->addText('usrstcity', 'Město:', 30);
    $form->addText('usrstpostcode', 'PSČ:', 30);

    $form->addTextArea('usrnote', 'Poznámka', 100, 5);

    $form->addSelect('usrstatus', 'Status', $user->getEnumUsrStatus());

    $form->addSubmit('save', 'Uložit');
    //$form->addSubmit('cancel', 'Zrušit')->setValidationScope(NULL);
    $form->onSuccess[] = array($this, 'userEditFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }

  protected function createComponentSearchForm() {
    $usrs = new \Model\UsersModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("ic", "IČ", 10)
      ->setDefaultValue($this->sIc);
    $form->addText("stname", "Příjmení", 10)
      ->setDefaultValue($this->sName);
    $form->addText("firname", "Firma", 10)
      ->setDefaultValue($this->sFirName);
    $form->addText("email", "Email", 10)
      ->setDefaultValue($this->sMail);
    $form->addSelect("prccat", "Cenová kategorie", $usrs->getEnumUsrPrcCat())
      ->setPrompt("")
      ->setDefaultValue($this->sPrcCat);


    $arr = array(
      'usrilname'=>'Příjmení',
      'usrmail'=>'Email',
      'usrdatec'=>'Datum registrace',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

    public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sIc= Null;
        $this->sName = Null;
        $this->sFirName = Null;
        $this->sMail = Null;
        $this->sPrcCat = Null;
        $this->sOrderBy = "usrilname";
        $this->sOrderByType = "ASC";
      } else {
        $vals = $form->getValues();
        $this->sIc= $vals["ic"];
        $this->sName = $vals["stname"];
        $this->sFirName = $vals["firname"];
        $this->sMail = $vals["email"];
        $this->sPrcCat = $vals["prccat"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("default");
  }

  public function actionClientCardPdf($id) {
    if ($id > 0) {
      $this->ClientCardPdf($id, 'D');
    }
    $this->terminate();
  }

  public function ClientCardPdf($id, $dest="D") {
    $template = $this->createTemplate();
    $users = new \Model\UsersModel();
    $template->dataRow = $users->load($id);
    $template->setFile(WWW_DIR.'/../templates/pdf/clientCardPdf.latte');
    $fname = ('klient-'.$template->dataRow->usrid.'-'.Nette\Utils\Strings::webalize($template->dataRow->usriname.'-'.$template->dataRow->usrilname));
    // mPDF
    require(LIBS_DIR."/mpdf/mpdf.php");
    $mpdf = new mPDF('utf-8','A4', 12,'',10,10,10,10,9,9,'P');
    $mpdf->useOnlyCoreFonts = true;
    $mpdf->SetDisplayMode('real');
    $mpdf->SetAutoFont(0);
    $template->headers = (object) NULL;

    $pdfHtml = (string) $template; // vyrenderujeme šablonu už nyní
    $mpdf->AddPage('P');
    $mpdf->WriteHTML($pdfHtml, 2);
    if ($dest=="I") {
      $name = TEMP_DIR."/".$fname.".pdf";
    } else {
      $name = $fname.".pdf";
    }
    $mpdf->Output($name, $dest);

  }
}
