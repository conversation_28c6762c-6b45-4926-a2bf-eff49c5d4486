// tabulka

.table {

  width: 100%;

  // r<PERSON><PERSON><PERSON>í tabulky
  border-top: 1px solid @color_main_light;
  border-bottom: 0;
  border-left: 1px solid @color_main_light;
  border-right: 0;
  th, td {
    border-top: 0;
    border-bottom: 1px solid @color_main_light;
    border-left: 0;
    border-right: 1px solid @color_main_light;
  }

  // rozlámání tabulky na malých rozlišeních
  @media only screen and (max-width: @mqxs) {

    display: block;

    th, td, tr, thead, tbody {
      display: block;
    }

    th + th {
      border-top: 1px solid @color_main_dark;
    }

    tr td:last-child {
      border-bottom: 2px solid @color_main_light;
    }

  }

  // více prostoru v tabulce
  th, td {

    padding: 5px;

    @media (min-width: @mqsm) {
      padding: 10px 20px;
    }

  }

  // hlavi<PERSON>ka tabulky
  th {

    color: @color_white;
    background-color: @color_main_light;
    border-right: 1px solid lighten(@color_main, 5%);
    font-weight: normal;

    &:last-child {
      border-right: 1px solid @color_main;
    }

  }

  // zvýraznění řádku po najetí myší
  tr:hover td {
    background-color: lighten(@color_main, 60%);
  }

  // zebra stripping
  tr:nth-child(odd) td {
    background-color: @color_gray_light;
  }

}

  // kompaktnější tabulka
  .table--small {

    font-size: 14px;

    th, td {
      padding: 7px 10px;
    }

  }

  // vertikální tabulka
  .table--vertical {

    th {
      text-align: left;
      border-bottom: 1px dotted @color_main_light;
    }

  }

  // tabulka s parametry
  .table--parameters {

    border: 0;
    th, td { border: 0; }

    th {
      color: @color_black;
      background-color: @color_gray_light;
    }

    // zebra stripping
    tr:nth-child(even) th {
      background-color: @color_white;
    }

  }

  // tabulka s variantami
  .table--variants {

    border: 0;
    th, th:last-child, td { border: 0; }

    // cena, skladem
    td:nth-child(2), td:nth-child(3), td:nth-child(4) {
      white-space: nowrap;
      text-align: center;
    }

    // zebra stripping
    tr:nth-child(odd) td {
      background-color: lighten(@color_main, 60%);
    }

    img {
      float: left;
      margin-right: 15px;
    }

    a {
      color: @color_main;
      line-height: 20px;
      text-decoration: none;
    }

  }

  // tabulka provonání
  .table--compare {

    td {
      vertical-align: top;
      text-align: center;
    }

    img {
      display: inline-block;
    }

    // nebudeme zdvojovat poslední linku
    tr td:last-child {
      border-bottom: 1px solid @color_main;
    }

    // zrušení rozlámání na malých rozlišeních
    @media only screen and (max-width: @mqxs) {

      display: table;

      th, td {
        display: table-cell;
      }

      tr, thead, tbody {
        display: table-row;
      }

    }

    // zvětšení boxů od většího rozlišení
    @media (min-width: @mqsm) {

      th { width: 5%; }
      td { width: 20%; }

    }

  }

  // zarovnání tabulky
  .table--aligned {

    @media (min-width: @mqxs) {

      th { width: 40%; }
      td { width: 60%; }

    }

  }
