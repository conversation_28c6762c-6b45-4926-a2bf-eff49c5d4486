		body { font-family: DejaVuSansCondensed, sans-serif; font-size: 11pt;  }
		p { 	text-align: justify; margin-bottom: 4pt;  margin-top:0pt; }

		hr {	width: 70%; height: 1px; 
			text-align: center; color: #999999; 
			margin-top: 8pt; margin-bottom: 8pt; }

		a {	color: #000066; font-style: normal; text-decoration: underline; 
			font-weight: normal; }

		ul {	text-indent: 5mm; margin-bottom: 9pt; }
		ol {	text-indent: 5mm; margin-bottom: 9pt; }

		pre { font-family: DejaVuSansMono, monospaced; font-size: 9pt; margin-top: 5pt; margin-bottom: 5pt; }

		h1 {	font-weight: normal; font-size: 26pt; color: #000066; 
			font-family: DejaVuSansCondensed, sans-serif; margin-top: 18pt; margin-bottom: 6pt; 
			border-top: 0.075cm solid #000000; border-bottom: 0.075cm solid #000000; 
			text-align: ; page-break-after:avoid; }
		h2 {	font-weight: bold; font-size: 12pt; color: #000066; 
			font-family: DejaVuSansCondensed, sans-serif; margin-top: 6pt; margin-bottom: 6pt; 
			border-top: 0.07cm solid #000000; border-bottom: 0.07cm solid #000000; 
			text-align: ;  text-transform: uppercase; page-break-after:avoid; }
		h3 {	font-weight: normal; font-size: 26pt; color: #000000; 
			font-family: DejaVuSansCondensed, sans-serif; margin-top: 0pt; margin-bottom: 6pt; 
			border-top: 0; border-bottom: 0; 
			text-align: ; page-break-after:avoid; }
		h4 {	font-weight: ; font-size: 13pt; color: #9f2b1e; 
			font-family: DejaVuSansCondensed, sans-serif; margin-top: 10pt; margin-bottom: 7pt; 
			font-variant: small-caps;
			text-align: ;  margin-collapse:collapse; page-break-after:avoid; }
		h5 {	font-weight: bold; font-style:italic; ; font-size: 11pt; color: #000044; 
			font-family: DejaVuSansCondensed, sans-serif; margin-top: 8pt; margin-bottom: 4pt; 
			text-align: ;  page-break-after:avoid; }
		h6 {	font-weight: bold; font-size: 9.5pt; color: #333333; 
			font-family: DejaVuSansCondensed, sans-serif; margin-top: 6pt; margin-bottom: ; 
			text-align: ;  page-break-after:avoid; }


		.breadcrumb {
			text-align: right; font-size: 8pt; font-family: DejaVuSerifCondensed, serif; color: #666666;
			font-weight: bold; font-style: normal; margin-bottom: 6pt; }

		.infobox { margin-top:10pt; background-color:#DDDDBB; text-align:center; border:1px solid #880000; }

		.big { font-size: 1.5em; }
		.red { color: #880000; }
		.slanted { font-style: italic; }

/* For Table of Contents */
div.mpdf_toc {
	font-family: sans-serif;
	font-size: 11pt;
}
a.mpdf_toc_a  {
	text-decoration: none;
	color: black;
}
div.mpdf_toc_level_0 {		/* Whole line level 0 */
	line-height: 1.5;
	margin-left: 0;
	padding-right: 2em;	/* should match e.g <dottab outdent="2em" /> 0 is default */
}
span.mpdf_toc_t_level_0 {	/* Title level 0 - may be inside <a> */
	font-weight: bold;
}
span.mpdf_toc_p_level_0 {	/* Page no. level 0 - may be inside <a> */
}
div.mpdf_toc_level_1 {		/* Whole line level 1 */
	margin-left: 2em;
	text-indent: -2em;
	padding-right: 2em;	/* should match <dottab outdent="2em" /> 2em is default */
}
span.mpdf_toc_t_level_1 {	/* Title level 1 */
	font-style: italic;
	font-weight: bold;
}
span.mpdf_toc_p_level_1  {	/* Page no. level 1 - may be inside <a> */
}
div.mpdf_toc_level_2 {		/* Whole line level 2 */
	margin-left: 4em;
	text-indent: -2em;
	padding-right: 2em;	/* should match <dottab outdent="2em" /> 2em is default */
}
span.mpdf_toc_t_level_2 {	/* Title level 2 */
}
span.mpdf_toc_p_level_2 {	/* Page no. level 2 - may be inside <a> */
}

