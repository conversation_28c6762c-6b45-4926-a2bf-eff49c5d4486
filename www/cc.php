<?php
/**
 * Created by PhpStorm.
 * User: koblihcz
 * Date: 14.09.2017
 * Time: 14:34
 */

function rrmdir($dir) {
  if (is_dir($dir)) {
    $objects = scandir($dir);
    foreach ($objects as $object) {
      if ($object != "." && $object != "..") {
        if (filetype($dir . "/" . $object) == "dir") {
          rrmdir($dir . "/" . $object);
        } else {
          unlink($dir . "/" . $object);
        }
      }
    }
    reset($objects);
    rmdir($dir);
  }
}

if ($_GET["k"] == 'lZwJIL') {
  rrmdir(dirname(__FILE__) . "/../temp/cache/");
  echo "Cache vymazana ...";
}
