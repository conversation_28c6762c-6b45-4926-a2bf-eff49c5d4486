<?php

namespace FrontModule;
use dibi;
use Model\CatalogsModel;
use Model\CatPlacesModel;
use Model\OrdersModel;
use Model\PaymentTransactionsModel;
use Model\ProductsModel;
use Nette;
use Pixidos\GPWebPay\Data\Operation;
use Pixidos\GPWebPay\Param\Amount;
use Pixidos\GPWebPay\Param\Currency;
use Pixidos\GPWebPay\Param\MerOrderNum;
use Pixidos\GPWebPay\Param\OrderNumber;
use Pixidos\GPWebPay\Param\ResponseUrl;
use Tracy\Debugger;
use Pixidos\GPWebPay\Enum\Currency as CurrencyEnum;

final class BcPresenter extends BasePresenter {

  private function getApiConfig() {
    $neon = $this->neonParameters["bc"];
    return [
      'clientId' => $this->config["BC_API_CLIENT_ID"],
      'clientSecret' => $this->config["BC_API_SECRET"],
      'locationCode' => $this->config["BC_API_LOC_CODE"],
      'apiUrl' => $neon["apiUrl"],
    ];
  }

  public function actionGetProducts() {
    $bcApi = new \BcApi($this->getApiConfig());
    $items = $bcApi->getProducts();

    if (empty($items["value"])) {
      echo "<br>Nastala chyba nebo API nevrací žádné data";
      $this->terminate();
    }

    echo '<table border>';
    $fields = ['no', 'description', 'inventory', 'eshopGroup', 'eshopSubgroup', 'vatProdPostingGroup', 'unitPriceWeb', 'unitPriceWithVat'];
    foreach ($items["value"] as $item) {
      echo '<tr>';
      foreach ($item as $key => $value) {
        if (!in_array($key, $fields)) {
          continue;
        }

        echo "<td title=\"$key\">$value</td>";
      }
      echo '</tr>';
    }
    echo '</table>';
    $this->terminate();
  }

  public function actionUpdateProducts() {
    $bcApi = new \BcApi($this->getApiConfig());
    $items = $bcApi->getProducts();

    $cats = new CatalogsModel();
    $pros = new ProductsModel();
    $cpts = new CatPlacesModel();

    $data = [];
    $vats = [];
    $rows = dibi::fetchAll("select * from config where cfgcode like 'VATTYPE%'");
    foreach ($rows as $key => $row) {
      $vats[(int)$row->cfgvalue] = (int)substr($row->cfgcode, -1);
    }

    $start = dibi::fetchSingle("SELECT NOW()");

    if (empty($items["value"])) {
      echo "<br>Nastala chyba nebo API nevrací žádné data";

      $this->terminate();
    }

    $skipped = [];

    foreach ($items["value"] as $item) {
      //filtrace dle prefixu katalogu
      if (!$this->isValidCatalog($item["eshopGroup"])) {

        if (!isset($skipped[$item["eshopGroup"]])) {
          $skipped[$item["eshopGroup"]] = $item["eshopGroup"];
          echo("Catalog " . $item["eshopGroup"] . " přeskočen<br>");
        }

        continue;
      }

      if (empty($item["eshopGroup"])) {
        continue;
      }

      //odstraním případné prefixy v názvu katalogu, podkatalogu
      $item["eshopGroup"] = $this->clearCatalogName($item["eshopGroup"]);
      $item["eshopSubgroup"] = $this->clearCatalogName($item["eshopSubgroup"]);

      $vatText = $item["vatProdPostingGroup"];
      $vat = (int)substr($vatText, 3, 2);

      if (isset($vats[$vat])) {
        $vatId = $vats[$vat];
      } else {
        $vatId = 0;
        echo "neznámá sazba DPH: kód: " . $item["no"] . "|" . $vat . "<br>";
      }

      $proPrice = round((float)$item["unitPriceWeb"] * (1 + ($vat /100)));

      $catId = NULL;

      $proName = $item["description"];

      $eshopGroups = dibi::query("SELECT catid, catmasid, catname FROM catalogs WHERE COALESCE(catmasid,0)=0")->fetchAssoc("catname");
      $eshopSubGroups = dibi::query("SELECT catid, catmasid, catname FROM catalogs WHERE catmasid > 0")->fetchAssoc("catmasid|catname");

      $masId = NULL;

      if (!isset($eshopGroups[$item["eshopGroup"]])) {
        //nemám kategorii, vytvořím
        $masId = $cats->insert([
          'catmasid' => 0,
          'catname' => $item["eshopGroup"],
          'catstatus' => 0,
        ]);
        $eshopGroups[$item["eshopGroup"]] = dibi::fetch("SELECT catid, catmasid, catname FROM catalogs WHERE catid=?", $masId);
      } else {
        $masId = $eshopGroups[$item["eshopGroup"]]->catid;
      }

      if (!empty($item["eshopSubgroup"])) {
        if (!isset($eshopSubGroups[$masId][$item["eshopSubgroup"]])) {
          //nemám podkategorii, vytvořím
          $catId = $cats->insert([
            'catmasid' => $masId,
            'catname' => $item["eshopSubgroup"],
            'catstatus' => 0,
          ]);
          $eshopSubGroups[$masId][$item["eshopSubgroup"]] = dibi::fetch("SELECT catid, catmasid, catname FROM catalogs WHERE catid=?", $catId);
        } else {
          $catId = $eshopSubGroups[$masId][$item["eshopSubgroup"]]->catid;
        }
      } else {
        $catId = $masId;
      }

      $data[$item["no"]] = [
        "prostatus" => 0,
        "provatid" => $vatId,
        "proprice1a" => $proPrice,
        "catid" => $catId,
        "procatrootid" => $masId,
        //"eshopGroup" => $item["eshopGroup"],
        //"eshopSubgroup" => $item["eshopSubgroup"],
      ];

      if (!empty($proName)) {
        $data[$item["no"]]["proname"] = $proName;
      }
    }

    $products = dibi::query("SELECT proid, procode, proqty, proname FROM products")->fetchAssoc("procode");

    $xCnt = 0;
    $okCnt = 0;

    foreach ($data as $proCode =>  $item) {

      if (isset($products[$proCode])) {

        $catId = $item["catid"];
        unset($item["catid"]);

        //aktualizuji
        if ($pros->update($products[$proCode]->proid, $item)) {
          $okCnt ++;

          if ($catId > 0) {
            $cpts->updatePlace($products[$proCode]->proid, $catId);
          }
        }

      } else {
        echo "Chybí: $proCode\t";
        echo "<br>";
        $xCnt++;
      }

    }

    //přegeneruji katalogové cesty
    $cats->rebuildPaths();

    //vymažu zařazení do katalogu co se neaktualizovaly
    dibi::query("DELETE FROM catplaces WHERE coalesce(capdateu, capdatec) < '$start'");

    dibi::query("UPDATE products SET prostatus=1 WHERE coalesce(prodateu, prodatec) < '$start'");
    echo "<br>Hotovo. Aktualizováno: $okCnt, chybí: $xCnt";

    $this->terminate();
  }

  public function actionUpdateInventory() {
    $bcApi = new \BcApi($this->getApiConfig());
    $items = $bcApi->getInventory();

    if (empty($items["value"])) {
      echo "<br>Nastala chyba nebo API nevrací žádné data";

      $this->terminate();
    }

    $data = [];

    foreach ($items["value"] as $item) {
      $data[$item["itemNo"]] = [
        "proqty" => $item["inventory"],
        "proaccess" => $item["inventory"] > 0 ? 0 : 100,
      ];
    }

    $start = dibi::fetchSingle("SELECT NOW()");

    $pros = new ProductsModel();

    $products = dibi::query("SELECT proid, procode, proqty, proname FROM products")->fetchAssoc("procode");

    $okCnt = 0;

    foreach ($data as $proCode =>  $item) {

      if (isset($products[$proCode])) {

        if ($pros->update($products[$proCode]->proid, $item)) {
          $okCnt ++;
        }

      }

    }

    dibi::query("UPDATE products SET proqty=0, proaccess=100 WHERE coalesce(prodateu, prodatec) < '$start'");
    echo "<br>Hotovo. Aktualizováno: $okCnt";

    $this->terminate();
  }

  private function isValidCatalog(string $catName): bool {
    //zjistím na kterém eshopu jsem
    $appKey = $this->neonParameters["app"]["serverCode"];

    $prefix = substr($catName, 0, 3);

    if ($prefix !== "SS_" && $prefix !== "WS_" && $prefix !== "WW_") {
      return false;
    }

    $prefixKey = "";

    switch ($appKey) {
      case "widex":
        $prefixKey = "W";
        break;
      case "sivantos":
        $prefixKey = "S";
        break;
      default:
    }

    if ($prefixKey === ""){
      return false;
    }

    return str_contains($prefix, $prefixKey);
  }

  private function clearCatalogName(string $catName): string {
    return trim(str_replace(["SS_", "WS_", "WW_"], ["", "", ""], trim($catName)));
  }

}
