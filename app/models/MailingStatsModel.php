<?php
namespace Model;
use <PERSON><PERSON>, dibi;
  
class MailingStatsModel extends BaseModel {
  
  protected $tableName = "mailingstats";
  protected $fieldPrefix = "mas";
  
  Public function runCounterClick($mamid) {
    return dibi::query("UPDATE mailings SET mamclicks=mamclicks+1 WHERE mamid=%i", $mamid);
  }
  
  Public function runCounterView($mamid) {
    return dibi::query("UPDATE mailings SET mamviews=mamviews+1 WHERE mamid=%i", $mamid);
  }
  
  public function getEnumMasTypId() {
    return array(
      0 => 'Homepage klik',
      1 => 'Produkt klik',
      2 => 'Odhlášení klik',
      3 => 'Zobrazení emailu na WWW',
      4 => 'Zobrazení emailu',
    );
  }
}
?>