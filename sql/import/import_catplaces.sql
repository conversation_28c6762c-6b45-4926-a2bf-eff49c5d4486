TRUNCATE `catplaces`;
INSERT INTO catplaces (
SELECT
NULL AS capid,
capproid AS capproid,
capcatid AS capcatid,
capc AS capdatec,
capu AS capdateu
FROM cz_catplace
);

-- vyma<PERSON><PERSON> zařazení co nejsou nikde použiti
DELETE FROM catplaces WHERE NOT EXISTS (SELECT proid FROM products WHERE capproid=proid);
DELETE FROM catplaces WHERE NOT EXISTS (SELECT catid FROM catalogs WHERE capcatid=catid);

-- spustit /administrace/import/fill-pro-cat-root-id
