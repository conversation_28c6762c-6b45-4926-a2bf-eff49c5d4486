{
"orders": [
{foreach $orders as $order}
  {
  "number": "{$order->ordcode}",
  "date": "{$order->orddatec|date:'Y-m-d'}",
  "customer": {
  "company": "{$order->ordifirname}",
  "name": "{$order->ordilname} {$order->ordiname}",
  "street": "{$order->ordistreet} {$order->ordistreetno}",
  "city": "{$order->ordicity}",
  "psc": "{$order->ordipostcode}",
  "ico": "{$order->ordic}",
  "dic": "{$order->orddic}",
  "tel": "{$order->ordtel}",
  "email": "{$order->ordmail}",
  "note": "{$order->ordnote}"
  {if !empty($order->ordstlname) OR !empty($order->ordstfirname)}
    ,"consignee": {
    "company": "{$order->ordstfirname}",
    "name": "{$order->ordstlname} {$order->ordstname}",
    "street": "{$order->ordststreet} {$order->ordststreetno}",
    "city": "{$order->ordstcity}",
    "psc": "{$order->ordstpostcode}"
    }
  {/if}
  },
  "orderItems": [
  {foreach $order->ordItems as $row}
    {php
      $rateVAT = 'high';
      if ((int)$row->orivatid === 1) $rateVAT = 'low';
      if ((int)$row->orivatid === 2) $rateVAT = 'third';
      if ((int)$row->orivatid === 3) $rateVAT = 'none';

      if ((int)$row->oritypid === 1) {
      $proCode = 'DOPRAVNE';
      } else if ((int)$row->oritypid === 3) {
      $proCode = 'SLEVA';
      } else {
      $proCode = (string)$row->procode;
      }
    }
    {
    "code": "{$proCode}",
    {if $row->oritypid === 1}
      "delCode": "{$order->delMode->delcode|lower}",
      "payCode": "{$order->payMode->delcode|lower}",
      {if $order->delMode->delcode=='CESKA_POSTA_BALIKOVNA'} "balikovnaId": "{$order->orddelspec}",{/if}
    {/if}
    "quantity": "{$row->oriqty}",
    "unit": "ks",
    "rateVAT": "{$rateVAT}",
    "priceVAT": "{$row->oriprice}",
    "name": "{$row->oriname}"
    }{if !$iterator->isLast()}, {/if}
  {/foreach}
  ]
  }{if !$iterator->isLast()}, {/if}
{/foreach}
]
}
