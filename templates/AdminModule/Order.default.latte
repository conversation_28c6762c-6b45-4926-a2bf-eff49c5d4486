{var $title = '<PERSON><PERSON><PERSON> Objednávek'}

{block #content}

  <script>
  function noteSH(id) {
     if ($('#'+id).is(':visible')) {
      $('#'+id).hide();
     } else {
       $('#'+id).show();
     }
     return false;
  }
  </script>



  <div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

    {form searchForm class=>'form-inline'}

      <div class="input-group">
        <span class="input-group-addon">{label code /}</span>
        {input code class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label name /}</span>
        {input name class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label admin /}</span>
        {input admin class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label status /}</span>
        {input status class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label coupon /}</span>
        {input coupon class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label datefrom /}</span>
        {input datefrom class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label dateto /}</span>
        {input dateto class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">Řadit podle</span>
        {input orderby class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">Řadit</span>
        {input orderbytype class=>'form-control'}
      </div>

      {label notclosed}{input notclosed}{/label}

      {input search class=>"btn btn-success"}
      {input clear class=>"btn btn-default"}

    {/form}

    </div>

  </div>

  {include @ordersList.latte}
  {control paginator}

  {php $token = \FrontModule\ApiPresenter::getToken()}

  <p><a href="{plink //:Front:Api:ordersExport, token=>$token}">Json export link</a></p>
{/block}
