<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  <PERSON>;

final class ImportPresenter extends BasePresenter {

  public function exportSubmitted (Nette\Application\UI\Form $form) {
    dibi::getConnection()->onEvent = NULL;
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $manName = "";

      if ($vals["manid"] > 0) {
        $mans = dibi::fetch("SELECT * FROM manufacturers WHERE manid=%i", $vals["manid"]);
        $manName .= $mans->manname;
      }

      if (empty($vals["catid"]) && empty($vals["manid"])) {
        $manName = 'vse';
      }

      //nastaveni popisku cen
      $labelCom = 'Cena běžná';
      if (isset($this->neonParameters["labels"]["com"])) $labelCom = $this->neonParameters["labels"]["com"];
      $labelA = 'Cena A';
      if (isset($this->neonParameters["labels"]["a"])) $labelA = $this->neonParameters["labels"]["a"];
      $labelB = 'Cena B';
      if (isset($this->neonParameters["labels"]["b"])) $labelB = $this->neonParameters["labels"]["b"];
      $labelC = 'Cena C';
      if (isset($this->neonParameters["labels"]["c"])) $labelC = $this->neonParameters["labels"]["c"];
      $labelD = 'Cena D';
      if (isset($this->neonParameters["labels"]["d"])) $labelD = $this->neonParameters["labels"]["d"];

      //nastaveni popiskutypu produktu
      $labelTypId = 'Akce';
      if (isset($this->neonParameters["labels"]["protypid"])) $labelTypId = $this->neonParameters["labels"]["protypid"];
      $labelTypId2 = 'Novinka';
      if (isset($this->neonParameters["labels"]["protypid2"])) $labelTypId2 = $this->neonParameters["labels"]["protypid2"];
      $labelTypId3 = 'Tip';
      if (isset($this->neonParameters["labels"]["protypid3"])) $labelTypId3 = $this->neonParameters["labels"]["protypid3"];
      $labelTypId4 = 'Nejprodávanější';
      if (isset($this->neonParameters["labels"]["protypid4"])) $labelTypId4 = $this->neonParameters["labels"]["protypid4"];
      $labelTypId5 = 'Připravujeme';
      if (isset($this->neonParameters["labels"]["protypid5"])) $labelTypId5 = $this->neonParameters["labels"]["protypid5"];

      $cols["full"] = array(
        "proid" => "ID",
        "procode" => "Kód",
        "codemas" => "Kód nadřízené položky",
        "procode2" => "EAN",
        "proname" => "Název",
        "prodesc" => "Popis",
        "prodescs" => "Krátký popis",
        "proprice1com" => $labelCom." v ".$this->curCodes[1],
        "proprice1a" => $labelA." v ".$this->curCodes[1],
        "proprice1b" => $labelB." v ".$this->curCodes[1],
        "proprice1c" => $labelC." v ".$this->curCodes[1],
        "proprice1d" => $labelD." v ".$this->curCodes[1],
        "provatid" => "Sazba DPH (0-zakladní, 1-snížená)",
        "proaccess" => "Počet dní do dodání zboží, 0 - skladem",
        "proaccesstext" => "Dostupnost textově",
        "proqty" => "Kusů skladem",
        "manname" => "Název výrobce",
        "prowarranty" => "Záruka (volný text)",
        "protypid" => $labelTypId." (1-ANO, 0-NE)",
        "protypid2" => $labelTypId2." (1-ANO, 0-NE)",
        "protypid3" => $labelTypId3." (1-ANO, 0-NE)",
        "protypid4" => $labelTypId4." (1-ANO, 0-NE)",
        "protypid5" => $labelTypId5." (1-ANO, 0-NE)",
        "proweight" => "Hmotnost v Kg",
        "progifts" => "Dárky (kódy zboží oddělené čárkou)",
        "pronotdisc" => "Nezapočítávat do slevy (1-ANO, 0-NE)",
        "prodelfree" => "Doprava zdarma (1-ANO, 0-NE)",
        "progoogleoff" => "Neposílat do Google nákupy (1-ANO, 0-NE)",
        "prostatus" => "Status zboží  (0-aktivní, 1-blokované)",
      );

      $cols["pricelist"] = array(
        "proid" => "ID",
        "procode" => "Kód",
        "codemas" => "Kód nadřízené položky",
        "proname" => "Název",
        "proprice1com" => $labelCom." v ".$this->curCodes[1],
        "proprice1a" => $labelA." v ".$this->curCodes[1],
        "proprice1b" => $labelB." v ".$this->curCodes[1],
        "proprice1c" => $labelC." v ".$this->curCodes[1],
        "proprice1d" => $labelD." v ".$this->curCodes[1],
        "provatid" => "Sazba DPH (0-zakladní, 1-snížená)",
        "proaccess" => "Počet dní do dodání zboží, 0 - skladem",
        "proaccesstext" => "Dostupnost textově",
        "proqty" => "Kusů skladem",
        "protypid" => $labelTypId." (1-ANO, 0-NE)",
        "protypid2" => $labelTypId2." (1-ANO, 0-NE)",
        "protypid3" => $labelTypId3." (1-ANO, 0-NE)",
        "protypid4" => $labelTypId4." (1-ANO, 0-NE)",
        "protypid5" => $labelTypId5." (1-ANO, 0-NE)",
        "prostatus" => "Status zboží  (0-aktivní, 1-blokované)",
      );

      $arrRows = array();
      //nadpisy sloupců, popis a klíč datového pole
      $arrRows[0] = array();
      $arrRows[1] = array();
      foreach ($cols[$vals->type] as $key => $value) {
        $arrRows[0][] = $this->iconv2win1250($value);
        $arrRows[1][] = $key;
      }

      //nactu data
      $products = new \Model\ProductsModel();
      $sql = "SELECT pro.*, (SELECT p.procode FROM products AS p WHERE pro.promasid=p.proid) AS codemas, manname
FROM products AS pro
INNER JOIN manufacturers ON (manid=pro.promanid)
LEFT JOIN products AS promas ON (pro.promasid=promas.proid)
WHERE 
pro.proismaster=0 AND pro.prostatus=0 AND (promas.prostatus IS NULL OR promas.prostatus=0)";

      if ($vals["manid"] > 0) {
        $sql .= " AND manid=".$vals["manid"];
      }
      $sql .= "
GROUP BY pro.proid
ORDER BY pro.proname";
      $rows = dibi::fetchAll($sql);
      $i = 2;
      foreach ($rows as $key=>$row) {
        foreach ($cols[$vals->type] as $field => $col) {
          $arrRows[$i][] = $this->formatValueExport($field, $row[$field]);
        }
        $i++;
      }

      $fName = Nette\Utils\Strings::webalize($manName);

      header("Content-type: text/csv");
      header("Content-Disposition: attachment; filename={$fName}.csv");
      header("Pragma: no-cache");
      header("Expires: 0");

      $outputBuffer = fopen("php://output", 'w');
      foreach($arrRows as $val) {
        fputcsv($outputBuffer, $val, ";");
      }
      fclose($outputBuffer);

      $this->terminate();
    }
  }

  public function renderDefault() {

  }

  public function renderImportProductsParam() {
    dibi::getConnection()->onEvent = NULL;
    dibi::query("TRUNCATE proparams");
    $rows = dibi::fetchAll("SELECT * FROM cz_product");
    $prps = new \Model\ProParamsModel();
    $pros = new \Model\ProductsModel();
    foreach ($rows as $key => $row) {
      //zapisu parametry
      for ($i=1; $i<=20; $i++) {
        $par = trim($row["propar".$i]);
        if (!empty($par)) {
          $arr = explode(':', $par);
          $parname = (!empty($arr[0]) ? trim($arr[0]) : "");
          $parvalue = (!empty($arr[1]) ? trim($arr[1]) : "");
          if (!empty($parname) && !empty($parvalue)) {
            $prps->insert(array(
              'prpproid' => $row->proid,
              'prpname' => $parname,
              'prpvalue' => $parvalue,
            ));
          }
        }
      }
    }
    die("hotovo");
  }


  public function renderImportProductsCsv() {
    $cols = array(
      'procode',
      'proname',
      'manname',
      'catname',
      'prowarranty',
      'protypid',
      'protypid2',
      'protypid3',
      'protypid4',
      'prodescs',
      'prodesc',
      'proaccess',
      'propicname',
      'picnames',
      'picnamesdesc',
      'files',
      'filesdesc',
      'proaccessories',
      'prooptions',
      'prostatus',
    );

    //vymazu produkty a katalog
    /*
    dibi::query("TRUNCATE products");
    dibi::query("TRUNCATE catalogs");
    dibi::query("TRUNCATE catplaces");
    dibi::query("TRUNCATE manufacturers");
    */

    $cnt = 0;
    $products = new \Model\ProductsModel();
    $mans = new \Model\ManufacturersModel();
    $cats = new \Model\CatalogsModel();
    $catps = new \Model\CatPlacesModel();
    if (($handle = fopen(WWW_DIR."/data/produkty3.csv", "r")) !== FALSE) {
      while (($data = fgetcsv($handle, Null, ";")) !== FALSE) {
        $cnt++;
        $catid = 0;
        $manid = 0;
        $proid = 0;
        $vals = array();
        if ($cnt == 1) {
          continue;
        }
        $vals = array_combine($cols, $data);

        if ($vals["procode"] == 'Gkm570st') {
          echo "";
        }
        //prevedu pokud treba
        $vals['protypid'] = ($vals['protypid'] == 'True' ? 1 : 0);   //AKCE
        $vals['protypid2'] = ($vals['protypid2'] == 'True' ? 1 : 0); //NOVINKA
        $vals['protypid3'] = ($vals['protypid3'] == 'True' ? 1 : 0); //DOPORUCUJEME
        $vals['protypid4'] = ($vals['protypid4'] == 'True' ? 1 : 0); //NEJPROD
        $vals['prostatus'] = ($vals['prostatus'] == 'True' ? 1 : 0);

        //propicname bez pripony
        if (!empty($vals['propicname'])) {
          $vals['propicname'] = substr($vals['propicname'], 0, -4);
        }

        //zjistim vyrobce pokud neni vytvorim
        $manid = (int)dibi::fetchSingle("SELECT manid FROM manufacturers WHERE manname=%s", $vals['manname']);
        //neni zalozim
        if ($manid == 0) $manid = $mans->insert(array('manname'=>$vals['manname']));
        $vals['promanid'] = $manid;
        //zjistim katalog pokud neni vytvorim
        $vals['catname'] = str_replace('/', '|', $vals['catname']);
        $catid = (int)dibi::fetchSingle("SELECT catid FROM catalogs WHERE catpath=%s", $vals['catname']);
        //neni zalozim
        if ($catid == 0) {
          //rozparsruju cestu
          $arr = explode('|', $vals['catname']);
          $masid = 0;
          foreach ($arr as $val) {
            $catid = (int)dibi::fetchSingle("SELECT catid FROM catalogs WHERE catname=%s AND catmasid=$masid", $val);
            if ($catid == 0) $catid = $cats->insert(array('catmasid'=>$masid, 'catname'=>$val), false) ;
            $masid = $catid;
          }
          $cats->rebuildPaths();
          $catid = $masid;
        }
        /*
        //stahnu obrazky
        $tarFileName = \Nette\Strings::webalize($vals['procode']);
        $this->getProPic($vals["propicname"].'.jpg', $tarFileName.'.jpg');
        $arr = explode('|', $vals["picnames"]);
        $c = 0;
        foreach ($arr as $picname) {
          $c++;
          if (!empty($picname)) $this->getProPic($picname, $tarFileName.'_'.$c.'.jpg', "add");
        }
        $vals['propicname'] = $tarFileName;
        */
        //odstranim vswechny polozky ktere nejsou v db
        foreach ($vals as $key => $row) {
          if (substr($key, 0, 3) != 'pro') {
            unset($vals[$key]);
          }
        }

        $vals['prodesc'] = $this->strip_word_html($vals['prodesc']);
        $proid = $products->insert($vals);

        //zaradim do kataogu
        if ($catid > 0 && $proid > 0) $catps->insert(array('capproid'=>$proid, 'capcatid'=>$catid));
      }
      fclose($handle);
    }
    die("hotovo");
  }

    public function renderImportProductImagesCsv() {
    if (($handle = fopen(WWW_DIR."/data/produkty3.csv", "r")) !== FALSE) {
      while (($data = fgetcsv($handle, Null, ";")) !== FALSE) {
        //stahnu obrazky
        $procode = $data[0];
        if ($procode == "KOD") continue;
        $tarFileName = Nette\Utils\Strings::webalize($procode);

        $this->getProPic($data[12], $tarFileName.'.jpg');
        $arr = explode('|', $data[13]);
        $c = 0;
        foreach ($arr as $picname) {
          $c++;
          if (!empty($picname)) $this->getProPic($picname, $tarFileName.'_'.$c.'.jpg', "add");
        }

        dibi::query("UPDATE products SET propicname='$tarFileName' WHERE procode=%s", $procode);
      }
      fclose($handle);
    }
    die("hotovo");
  }


  public function renderImportPricesCsv() {
    $cols = array(
      'procode',
      'propricea',
      'vat',
      'disc',
    );

    $cnt = 0;
    $cntNf = 0;
    $products = new \Model\ProductsModel();
    if (($handle = fopen(WWW_DIR."/data/ceny.csv", "r")) !== FALSE) {
      while (($data = fgetcsv($handle, 0, ";")) !== FALSE) {
        $cnt++;
        $vals = array();
        if ($cnt == 1) {
          continue;
        }
        $vals = array_combine($cols, $data);
        //prevedu pokud treba
        $vals['provatid'] = ($vals['vat'] == '21' ? 0 : 1);
        //zjistim ID zbozi
        $proid = dibi::fetchSingle("SELECT proid FROM products WHERE procode=%s", $vals['procode']);

        //odstranim vswechny polozky ktere nejsou v db
        unset($vals['vat']);
        unset($vals['disc']);
        unset($vals['procode']);
        if ($proid > 0) {
          $products->update($proid, $vals);
        } else {
          //echo "Nenalezeno:".$vals['procode']."<br>";
          $cntNf++;
        }
      }
      fclose($handle);
    }
    die("hotovo:".$cnt);
  }

  public function renderImportParamsCsv() {
    $cnt = 0;
    $cntNf = 0;
    dibi::query("TRUNCATE proparams");
    $params = new \Model\ProParamsModel();
    if (($handle = fopen(WWW_DIR."/data/parametry.csv", "r")) !== FALSE) {
      while (($data = fgetcsv($handle, 0, ";")) !== FALSE) {
        $cnt++;
        $vals = array();
        if ($cnt == 1) {
          continue;
        }
        //zjistim ID zbozi
        $proid = dibi::fetchSingle("SELECT proid FROM products WHERE procode=%s", $data[0]);

        if ($proid > 0) {

          //odstranim nesmysly
          $pos = strpos($data[2], '$');
          if ($pos === false) {
          } else {
            $data[2] = substr($data[2], 0, $pos);
          }

          $vals = array(
            'prpproid' => $proid,
            'prpname' => $data[1],
            'prpvalue' => trim($data[2].' '.$data[3]),
          );
          $params->insert($vals);
        } else {
          $cntNf++;
        }
      }
      fclose($handle);
    }
    die("hotovo:".$cnt);
  }

  protected function getProPic($sourceName, $picName, $type="main") {
    global $server;
    if ($type == 'main') {
      $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\big\\'.$picName;
      $file_source = "http://www.mobil-bar.cz/fotocache/bigorig/".$sourceName;
    } else {
      $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\big\\'.$picName;
      $file_source = "http://www.mobil-bar.cz/fotocache/bigadd/".$sourceName;
    }

    if (!file_exists($file_target)) {
      $file_source = str_replace(' ', '%20', html_entity_decode($file_source)); // fix url format

      try {

        // Begin transfer
        if (($rh = @fopen($file_source, 'rb')) === FALSE) { return false; } // fopen() handles
        if (($wh = @fopen($file_target, 'wb')) === FALSE) { return false; } // error messages.
        while (!feof($rh)) {
          // unable to write to file, possibly because the harddrive has filled up
          if (fwrite($wh, fread($rh, 1024)) === FALSE) { fclose($rh); fclose($wh); return false; }
        }
        } catch (Exception $e) {
        }
      // Finished without errors
      fclose($rh);
      fclose($wh);
      if ($type == 'main') {
        $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\list\\'.$picName;
        $file_source = "http://www.mobil-bar.cz/fotocache/small/".$sourceName;
      } else {
        $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\list\\'.$picName;
        $file_source = "http://www.mobil-bar.cz/fotocache/add/".$sourceName;
      }
      if (!file_exists($file_target)) {
        $file_source = str_replace(' ', '%20', html_entity_decode($file_source)); // fix url format

        // Begin transfer
        if (($rh = fopen($file_source, 'rb')) === FALSE) { return false; } // fopen() handles
        if (($wh = fopen($file_target, 'wb')) === FALSE) { return false; } // error messages.
        while (!feof($rh)) {
          // unable to write to file, possibly because the harddrive has filled up
          if (fwrite($wh, fread($rh, 1024)) === FALSE) { fclose($rh); fclose($wh); return false; }
        }

        // Finished without errors
        fclose($rh);
        fclose($wh);
        if ($type == 'main') {
          $file_target = 'D:\work\www\mobil-bar.cz\www\pic\product\detail\\'.$picName;
          $file_source = "http://www.mobil-bar.cz/fotocache/mid/".$sourceName;
        } else {
          return;
        }
        if (!file_exists($file_target)) {
          $file_source = str_replace(' ', '%20', html_entity_decode($file_source)); // fix url format

          // Begin transfer
          if (($rh = fopen($file_source, 'rb')) === FALSE) { return false; } // fopen() handles
          if (($wh = fopen($file_target, 'wb')) === FALSE) { return false; } // error messages.
          while (!feof($rh)) {
            // unable to write to file, possibly because the harddrive has filled up
            if (fwrite($wh, fread($rh, 1024)) === FALSE) { fclose($rh); fclose($wh); return false; }
          }

          // Finished without errors
          fclose($rh);
          fclose($wh);
        }
      }
    }
  }

  /********************* facilities *********************/

  protected function createComponentImportForm() {

    $form = $this->createAppForm();

    $form->addUpload('imp_file', "Importní CSV soubor", 100)
      ->addRule(Nette\Forms\Form::FILLED, "Vyberte %label");

    $form->addSubmit('save', 'Importovat');
    $form->onSuccess[] = array($this, 'importSubmitted');
    return $form;
  }

  public function importSubmitted (Nette\Application\UI\Form $form) {
    dibi::getConnection()->onEvent = NULL;
    $pros = new Model\ProductsModel();
    $mans = new Model\ManufacturersModel();
    $fileName = "";
    $log = array();
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $file = $vals["imp_file"][0];
      //podle typu importu se rozhodnu
      if (!$file->isOk()) {
        $form->addError("Neplatný importní soubor.");
        return;
      } else {
        $fileName = $file->getTemporaryFile();
      }
      $cntupdOK = 0;
      $cntupdER = 0;
      $cntinsOK = 0;
      $cntinsER = 0;
      $cnt = 0;

      if (file_exists($fileName)) {
        $file = file_get_contents($fileName);
        $products = array();
        $data = str_getcsv($file, "\n"); //parse the rows
        $row = str_getcsv($data[1], ";");
        $fields = array();
        foreach ($row as $key => $field) {
          $fields[$key] = $field;
        }
        if ($fields[0] !== "proid") {
          $form->addError("Chybí sloupec ID ([proid])");
          return;
        }
        foreach($data as $key => $row) {
          if ($key <= 1) continue;

          $row = str_getcsv($row, ";"); //parse the items in rows
          $item = array();
          foreach ($row as $key => $value) {
            $fieldName = $fields[$key];

            //pokud se jedná o název výrobce - dotáhnu ID výrobce
            if ($fieldName == 'manname') {
              $man = $mans->load($value, "name");
              if (!empty($man->manid)) {
                $item["promanid"] = $man->manid;
                continue;
              } else {
                $log[] = "Výrobce $value nenalezen, položka s kódem: ".$item["procode"]." se nenaimportovala.";
                $cntupdER++;
                continue 2;
              }
            }

            //uklidím pole které se netýkají produktu
            if (substr($fieldName, 0, 3) != 'pro') continue;
            $item[$fieldName] = $this->formatValueImport($fields[$key], $value);
          }


          if ($item["proid"] > 0) {
            if ($pros->update($item["proid"], $item)) {
              $cntupdOK++;
            } else {
              $cntupdER++;
              $log[] = "Chyba update proid=".$item["proid"];
            }
          } else {
            if ($pros->insert($item)) {
              $cntinsOK++;
            } else {
              $cntinsER++;
              $log[] = "Chyba insert procode=".$item["procode"];
            }
          }
        }
      } else {
        $log[] = "Nepodařilo se načíst soubor $fileName";
      }

      $log[] = "Celkem $cnt. Aktualizované záznamy: ".($cntupdOK+$cntinsOK)." bez chyb, ".($cntupdER+$cntinsER)." chyb";
      $log[] = "Import dokončen.";
      //$catalogs->rebuildPaths();
      $this->template->log = $log;
      //$this->flashMessage("Import dokončen.");
      //$this->redirect('this');
    }
  }

  protected function createComponentExportForm() {

    $form = $this->createAppForm();
    $catalogs = new \Model\CatalogsModel();
    $products = new \Model\ProductsModel();
    /*
    $arr = $catalogs->getEnumCatalogCombo();
    //$arr[0] = "Vše";
    $form->addSelect('catid', 'Katalog:', $arr)
      ->setPrompt("Vše");
    */
    $form->addSelect('manid', 'Výrobce:', $products->getEnumProManId())
      ->setPrompt("Vše");

    $form->addSelect('type', 'Typ exportu:', array(
      'pricelist'=>'Ceník',
      'full'=>'Plný export'
    ));

    $form->addSelect('prostatus', 'Status zboží:', $products->getEnumProStatus())
      ->setDefaultValue(0)
      ->setPrompt("Vše");

    $form->addSubmit('save', 'Exportovat');
    $form->onSuccess[] = array($this, 'exportSubmitted');
    return $form;
  }

  private function formatValueImport($field, $value) {
    switch ($field) {
      case "proprice1com":
      case "proprice1a":
      case "proprice1b":
      case "proprice1c":
      case "proprice1d":
      case "proprice2com":
      case "proprice2a":
      case "proprice2b":
      case "proprice2c":
      case "proprice2d":
        $value = $this->formatNumberMySQL($value);
        break;
    }
    if (is_string($value)) $value = $this->iconv2utf8($value);
    return $value;
  }

  private function formatValueExport($field, $value) {
    switch ($field) {
      case "proprice1com":
      case "proprice1a":
      case "proprice1b":
      case "proprice1c":
      case "proprice1d":
      case "proprice2com":
      case "proprice2a":
      case "proprice2b":
      case "proprice2c":
      case "proprice2d":
        $value = $this->formatNumber($value);
        break;
    }
    if (is_string($value)) $value = $this->iconv2win1250($value);
    return $value;
  }

  private function getManId($name) {
    $manid = dibi::fetchSingle("SELECT manid FROM manufacturers WHERE manname=%s", $name);
    if ($manid > 0) {
    } else {
      $manufacturers = new \Model\ManufacturersModel();
      $manid = $manufacturers->insert(array('manname'=>$name));
    }
    return($manid);

  }

  private function getTypId($name) {
    $enuid = dibi::fetchSingle("SELECT enuid FROM enumcats WHERE enutypid=2 AND enuname=%s", $name);
    if ($enuid > 0) {
    } else {
      $enumcats = new \Model\EnumcatsModel();
      $enuid = $enumcats->insert(array('enutypid'=>2, 'enuname'=>$name));
    }
    return($enuid);

  }

  private function prepStr($str) {
    $str = trim($str, '"');
    $str = ereg_replace('""', '"', $str);
    return $str;
  }

  private function strip_word_html($text, $allowed_tags = '<a><ul><li><br><br/><br /><p>') {
    mb_regex_encoding('UTF-8');
    //replace MS special characters first
    $search = array('/&lsquo;/u', '/&rsquo;/u', '/&ldquo;/u', '/&rdquo;/u', '/&mdash;/u');
    $replace = array('\'', '\'', '"', '"', '-');
    $text = preg_replace($search, $replace, $text);
    //make sure _all_ html entities are converted to the plain ascii equivalents - it appears
    //in some MS headers, some html entities are encoded and some aren't
    //$text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
    //try to strip out any C style comments first, since these, embedded in html comments, seem to
    //prevent strip_tags from removing html comments (MS Word introduced combination)
    if(mb_stripos($text, '/*') !== FALSE){
        $text = mb_eregi_replace('#/\*.*?\*/#s', '', $text, 'm');
    }
    //introduce a space into any arithmetic expressions that could be caught by strip_tags so that they won't be
    //'<1' becomes '< 1'(note: somewhat application specific)
    $text = preg_replace(array('/<([0-9]+)/'), array('< $1'), $text);
    $text = strip_tags($text, $allowed_tags);
    //eliminate extraneous whitespace from start and end of line, or anywhere there are two or more spaces, convert it to one
    $text = preg_replace(array('/^\s\s+/', '/\s\s+$/', '/\s\s+/u'), array('', '', ' '), $text);
    //strip out inline css and simplify style tags
    $search = array('#<(strong|b)[^>]*>(.*?)</(strong|b)>#isu', '#<(em|i)[^>]*>(.*?)</(em|i)>#isu', '#<u[^>]*>(.*?)</u>#isu');
    $replace = array('<b>$2</b>', '<i>$2</i>', '<u>$1</u>');
    $text = preg_replace($search, $replace, $text);
    //on some of the ?newer MS Word exports, where you get conditionals of the form 'if gte mso 9', etc., it appears
    //that whatever is in one of the html comments prevents strip_tags from eradicating the html comment that contains
    //some MS Style Definitions - this last bit gets rid of any leftover comments */
    $num_matches = preg_match_all("/\<!--/u", $text, $matches);
    if($num_matches){
        $text = preg_replace('/\<!--(.)*--\>/isu', '', $text);
    }
    $text = preg_replace('/(<[^>]+) style=".*?"/i', '$1', $text);
  return $text;
  }


  public function actionRenameCatPics() {
    dibi::getConnection()->onEvent = NULL;
    $rows = dibi::fetchAll("SELECT * from cz_catalog");
    foreach ($rows as $row) {
      //nactu obrazek a prejmenuji a smazu
      $path = WWW_DIR."/pic/catalog/";
      $filename = $row->catlogos;
      if (empty($filename)) continue;
      if (file_exists($path.$filename)) {
        rename($path . $filename, $path . $row->catid . ".jpg");
        //unlink($path . $filename);
      }
    }
    die("hotovo");
  }

  public function actionFillProCatRootId() {
    dibi::getConnection()->onEvent = NULL;
    $rows = dibi::fetchAll("SELECT proid, promasid, procatrootid from products where coalesce(procatrootid,0)=0");
    $pros = new Model\ProductsModel();

    foreach ($rows as $row) {
      $pros->setProCatRootId($row);
    }
    die("hotovo");
  }

  public function actionRecalcProIsMaster() {
    dibi::getConnection()->onEvent = NULL;
    $pros = new \Model\ProductsModel();
    $rows = dibi::fetchAll("SELECT proid, proprice1com, proprice2com, progift, progifts FROM products");
    foreach ($rows as $row) {
      $cnt = (int)dibi::fetchSingle("SELECT
    COUNT(proid) AS cnt
    FROM products WHERE promasid=%i", $row->proid);

      $proIsMaster = (int)($cnt > 0);
      $vals = array('proismaster'=>$proIsMaster);

      if ($proIsMaster == 1) {
        dibi::query("
          UPDATE products SET 
          proprice1com=%f", $row->proprice1com, ",
          proprice2com=%f", $row->proprice2com, "
          WHERE promasid=%i", $row->proid
        );

        /*
        $vals['proprice1com']=$row->proprice1com;
        $vals['proprice1a']=$row->proprice1a;
        $vals['proprice1b']=$row->proprice1b;
        $vals['proprice1c']=$row->proprice1c;
        $vals['proprice1d']=$row->proprice1d;
        $vals['proprice2com']=$row->proprice2com;
        $vals['proprice2a']=$row->proprice2a;
        $vals['proprice2b']=$row->proprice2b;
        $vals['proprice2c']=$row->proprice2c;
        $vals['proprice2d']=$row->proprice2d;
        */
      }

      //zjistim jestli má dárek
      if (!empty($row->progift)) {
        //načtu dárek abych zjistil kód
        $procode = (string)dibi::fetchSingle("SELECT procode FROM products WHERE proid=%i", $row->progift);
        if (!empty($procode)) $vals["progifts"] = $procode;
      }

      $pros->update($row->proid, $vals);
    }
    die("hotovo");
  }

  public function actionClearProPic() {
    dibi::getConnection()->onEvent = NULL;

    //načtu všechny produkty a budu kopírovat jeho obrázky
    $rows = dibi::fetchAll("SELECT proid, procode, proname,propicname FROM products");

    $dirs = array("list", "detail", "big", "usrsize", "size2");
    $pathSrc = WWW_DIR . "/pic/product-new/";
    $pathTarget = WWW_DIR . "/pic/product-clear/";
    foreach ($rows as $row) {
      $picname = (!empty($row->propicname) ? $row->propicname : $row->procode);
      foreach ($dirs as $dir) {
        $fileName = $picname.".jpg";
        if (file_exists($pathSrc . "/" . $dir . "/" . $fileName)) {
          copy($pathSrc . "/" . $dir . "/" . $fileName, $pathTarget . "/" . $dir . "/" . $fileName);
        }
        for ($i = 1; $i <= 20; $i++) {
          $fileName = $picname . "_" . $i . ".jpg";
          if (file_exists($pathSrc . "/" . $dir . "/" . $fileName)) {
            copy($pathSrc . "/" . $dir . "/" . $fileName, $pathTarget . "/" . $dir . "/" . $fileName);
          }
        }
      }
    }
    die("hotovo");
  }
}

