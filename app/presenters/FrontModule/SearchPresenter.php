<?php
namespace FrontModule;
use dibi;
use Nette;

final class SearchPresenter extends BasePresenter {

  /** @persistent */
  public $name = '';

  /** @persistent */
  public $fulltext = '';

  /** @persistent */
  public $s = '';

  /** @persistent */
  public $o = '';

  /** @persistent */
  public $m = array();

  public function renderDefault () {
    $this->name = $this->getParameter("name");
    $this->fulltext = $this->getParameter("fulltext");
    //$this->m = $this->getParameter("m");
    //$this->s = $this->getParameter("s");
    $this->o = $this->getParameter('o');

    $where  = $this->getWhere();
    $orderBy = "";
    switch ($this->o) {
       case '':
         $orderBy = "proorder";
         break;
       case 'name':
         $orderBy = " proname ASC";
         break;
       case 'name_':
         $orderBy = " proname DESC";
         break;
       case 'price':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) ASC";
         break;
       case 'price_':
         $orderBy = " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) DESC";
         break;
    }

    $product = new \Model\ProductsModel();
    $product->setPrcCat($this->userData->usrprccat); //musim nastavit cenovou kategorii prihlaseneho uzivatele
    $product->setCurrency($this->currencies, $this->curId);
    $dataSource = $product->getDataSource($product->getSqlList($where, $orderBy));

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["CATALOG_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->productsData = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    $form = $this['detailSearchForm'];
    if (!$form->isSubmitted()) {
      $form->setDefaults(array(
        'mans'=>$this->m,
        'name'=>$this->name,
        'fulltext'=>$this->fulltext,
        'onstock'=>$this->s,
      ));
    }
  }

  public function renderSearchAc($q) {
    $product = new \Model\ProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    $sql = $product->getSqlList(" prostatus=0 AND promasid=0 AND proname LIKE '%".$q."%'", "proname ASC LIMIT 7");
    $this->template->productsData = dibi::fetchAll($sql);
  }

  protected function createComponentDetailSearchForm() {
    $form = $this->createAppForm();
    $form->addGroup();
    $form->addtext("name", "Název:", 40);
    $form->addtext("fulltext", "Fulltext:", 40);
    $where = $this->getWhere();
    $query[] = "SELECT manid, manname 
FROM products 
INNER JOIN manufacturers ON (manid=promanid)
WHERE ";
    $query = array_merge($query, $where);
    array_push($query, "GROUP BY manid
ORDER BY manname");

    $manufacts = dibi::fetchAll($query);
    $form->addGroup("Výrobci");
    $container = $form->addContainer('mans');
    foreach ($manufacts as $row) {
      $container->addCheckbox($row->manid, $row->manname);
    }
    $form->addCheckbox('onstock', "Skladem")
      ->setHtmlId("onstock");
    $form->addSubmit('detailSearch', 'Hledej')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();

      $this->name = $vals["name"];
      $this->fulltext = $vals["fulltext"];
      //zaloguju hledany text
      if (!empty($vals["fulltext"])) {
        $ftx = new \Model\FulltextlogsModel();
        $ftx->insert(array('ftxtext'=>$vals["fulltext"]));
      }
      $this->s = $vals["onstock"];
      $this->m = array();
      foreach ($vals['mans'] as $key => $val) {
        if ($val) $this->m[$key] = $key;
      }
      $this->redirect('this');

      $this->redirect('Search:default');
    }
  }

  Private function getWhere($manOff = false) {
    $where = array("promasid=0 AND prostatus=0");
    if (!empty($this->name)) {
      array_push($where, " AND proname LIKE %~like~", $this->name);
    }
    if (!empty($this->fulltext)) {
      array_push($where, " AND (proname LIKE %~like~", $this->fulltext, " OR prodescs LIKE %~like~", $this->fulltext, ")");
    }
    if (count($this->m) > 0 && $manOff == false) {
      array_push($where, " AND promanid IN (%i)", $this->m);
    }

    if ($this->s) {
      array_push($where," AND  proaccess=0");
    }

    if (count($where) == 0) array_push($where, "proid=-1");
    return $where;
  }
}
