<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;
use Nette\Application\UI\Form;
use Model\PagesModel;

final class PagePresenter extends BasePresenter {

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $page = new \Model\PagesModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();

      if (empty($vals["pagurlkey"])) $vals["pagurlkey"] = Nette\Utils\Strings::webalize($vals["pagname"]);

      //projdu formularova pole a ktere nejsou treba odstranim
      foreach ($vals as $key => $value) {
      }

      try {
        if ($page->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          if ($vals["pagblock"] == 1) {
            $this->redirect('Page:defaultBlock');
          } else {
            $this->redirect('Page:default');
          }
        }
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  /********************* view default *********************/

  public function renderDefault() {
    $page = new PagesModel();
    $dataRows = dibi::query("SELECT * FROM pages WHERE  pagblock=0 ORDER BY pagname")
      ->fetchAssoc('pagid');

    $this->template->dataRows = $dataRows;
    $this->template->enum_pagtypid = $page->getEnumPagTypId();

    //načtu si uploadnuté systémové soubory
    $files = [];
    foreach ($page->getEnumSystemFiles() as $key => $name) {
      if (file_exists(WWW_DIR."/files/".$key)) {
        $files["/files/" . $key] = $name;
      }
    }
    $this->template->uploadedSystemFiles = $files;

  }

  public function renderDefaultBlock() {
    $page = new \Model\PagesModel();
    $dataRows = dibi::fetchAll("SELECT * FROM pages WHERE pagblock=1 ORDER BY pagname");
    $this->template->dataRows = $dataRows;
    $this->template->enum_pagstatus = $page->getEnumPagStatus();
  }

  public function renderEdit($id) {
    $page = new \Model\PagesModel();
    $dataRow = array();
    $pagBlock = Null;
    if ($id > 0) {
      $dataRow = $page->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      $pagBlock = $dataRow->pagblock;
    } else {
      $pagBlock = (int)$this->getParameter('pagblock');
      $dataRow = array('pagblock'=>$pagBlock);
    }
    if ($pagBlock==1) {
      $form = $this['editBlockForm'];
    } else {
      $form = $this['editForm'];
    }
    $form->setDefaults($dataRow);
    $this->template->pagblock = $pagBlock;
    $this->template->dataRow = $dataRow;
    $this->template->id = $id;
    $this->template->enum_pagtypid = $page->getEnumPagTypId();

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $id);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype IN ('jpg', 'png', 'gif')", $id);
    if (count($this->template->images) > 0) $this->template->imagesListId = $id;
  }

  public function renderJsImagesList($id) {
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE atapagid=%i AND atatype IN ('jpg', 'png', 'gif')", $id);
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');

    $page = new \Model\PagesModel();

    $form = $this->createAppForm();
    $form->addHidden('pagblock', 0);
    $form->addSelect('pagtypid', 'Typ stránky:', $page->getEnumPagTypId());

    $form->addText('pagname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    $form->addText('pagurlkey', 'URL:', 30);

    $form->addText('pagtitle', 'Title:', 100);
    $form->addTextArea('pagdescription', 'Description:', 75, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));
    $form->addText('pagkeywords', 'Keywords:', 100);

    $form->addTextArea('pagbody', 'Popis:', 60, 20);
    $form['pagbody']->getControlPrototype()->class('mceEditor');

    if ($id > 0) {
      $pag = $page->load($id);
      if ($pag->pagtypid==4) {
        $form->addTextArea('pagtext1', 'Produkt 1:', 160, 6)
          ->setOption("description", "Vyplňte ID, Název, cenu a popis, v tomoto pořadí, každou hodnotu na samostatný řádek");
        $form->addTextArea('pagtext2', 'Produkt 2:', 160, 6)
          ->setOption("description", "Vyplňte ID, Název, cenu a popis, v tomoto pořadí, každou hodnotu na samostatný řádek");
        $form->addTextArea('pagtext3', 'Produkt 3:', 160, 6)
          ->setOption("description", "Vyplňte ID, Název, cenu a popis, v tomoto pořadí, každou hodnotu na samostatný řádek");
        $form->addTextArea('pagtext4', 'Produkt 4:', 160, 6)
          ->setOption("description", "Vyplňte ID, Název, cenu a popis, v tomoto pořadí, každou hodnotu na samostatný řádek");
        $form->addTextArea('pagtext5', 'Produkt 5:', 160, 6)
          ->setOption("description", "Vyplňte ID, Název, cenu a popis, v tomoto pořadí, každou hodnotu na samostatný řádek");
        $form->addTextArea('pagtext6', 'Produkt 6:', 160, 6)
          ->setOption("description", "Vyplňte ID, Název, cenu a popis, v tomoto pořadí, každou hodnotu na samostatný řádek");
      }
    }

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  protected function createComponentEditBlockForm() {
    $id = (int)$this->getParameter('id');

    $page = new \Model\PagesModel();

    $form = $this->createAppForm();
    $form->addHidden('pagblock', 1);

    $form->addText('pagurlkey', 'Kód:', 30);
    $form->addText('pagname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    $form->addText('pagtitle', 'Title:', 100);
    $form->addTextArea('pagdescription', 'Description:', 75, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));

    $form->addTextArea('pagbody', 'Popis:', 60, 20);
    $form['pagbody']->getControlPrototype()->class('mceEditor');

    $form->addSelect('pagstatus', 'Status:', $page->getEnumPagStatus())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte status.');

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');
    return $form;
  }

  protected function createComponentUploadForm() {
    $form = $this->createAppForm();
    $form->addText("ataname", 'Název přílohy')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addUpload('file', 'Příloha:')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addSubmit('save', 'Připojit')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'UploadFormSubmitted');
    return $form;
  }

  public function UploadFormSubmitted (Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $pagid = $this->getParameter('id');
      if ($vals["file"]->isOk()) {
        //ulozim do db
        $atts = new \Model\AttachmentsModel();
        $ataVals = array(
          'atapagid'=>$pagid,
          'ataname'=>$vals["ataname"],
          'atafilename'=>Nette\Utils\Strings::webalize($vals["ataname"]).'_'.$pagid.'.'.substr($vals["file"]->getName(), -3),
          'atasize'=>(string)$vals["file"]->getSize(),
          'atatype'=>substr($vals["file"]->getName(), -3),
        );
        $ataid = $atts->insert($ataVals);
        $ataVals["atafilename"] = Nette\Utils\Strings::webalize($ataVals["ataname"]).'_'.$ataid.'.'.$ataVals["atatype"];
        $atts->update($ataid, $ataVals);
        /*
        if ($vals["file"]->isImage()) {
          //pokud je to obrazek tak zmensim
          $img = $vals["file"]->toImage();
          $img->resize(150, 150); // resize, který prostor vyplní a možná překročí
            //->crop('50%', '50%', 150, 150); // ořezání po stranách
          $blank = NImage::fromBlank(150, 150, NImage::rgb(255, 255, 255));
          $blank->place($img, '50%', '50%');
          /*
          $watermarkFile = $path.($dir!="" ? '/'.$dir : '').'/watermark.png';
          if (file_exists($watermarkFile)) {
            $watermark = NImage::fromFile($watermarkFile);
            $img->place($watermark, '50%', '50%');
          }
          */
          //$img->sharpen();
        //  $blank->save(WWW_DIR.'/files/'.$ataVals["atafilename"], 100);
          //$vals["file"]->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);
        //} else {
          $vals["file"]->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);
        //}
      }
      $this->redirect('edit', $pagid);
    }
  }

  protected function createComponentUploadSystemFileForm(): Form
  {
    $pageModel = new PagesModel();

    $form = $this->createAppForm();
    $form->addSelect("filename", 'Typ souboru který nahráváte', $pageModel->getEnumSystemFiles())
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addUpload('file', 'Vyberte soubor:')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addSubmit('save', 'Nahrát (původní soubor bude přepsaný tímto novým)')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'UploadSystemFileFormSubmitted');
    return $form;
  }

  public function UploadSystemFileFormSubmitted (Form $form): void
  {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $pagid = $this->getParameter('id');
      if ($vals["file"]->isOk()) {
        $vals["file"]->move(WWW_DIR.'/files/'.$vals["filename"]);
        $this->flashMessage("Soubor byl nahrán");
      }
      $this->redirect('default');
    }
  }

  public function actionDeleteAttachment($ataid, $pagid) {
    $file = dibi::fetch("SELECT * FROM attachments WHERE ataid=%i", $ataid);
    if ($file) {
      @unlink(WWW_DIR.'/files/'.$file->atafilename);
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $ataid);
    }
    $this->redirect('Page:edit', $pagid);
  }

}
