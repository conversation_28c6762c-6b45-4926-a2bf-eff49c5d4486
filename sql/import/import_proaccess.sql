TRUNCATE `proaccess`;
INSERT INTO proaccess (
SELECT
NULL AS acsid,
acsproid AS acsproid,
acsacsproid AS acsacsproid,
acsisban AS acsisban,
NOW() AS acsdatec,
NOW() AS acsdateu
FROM cz_access
);

-- vymažu přísluško co není nikde třeba
DELETE FROM proaccess WHERE NOT EXISTS (SELECT proid FROM products WHERE acsproid=proid);
DELETE FROM proaccess WHERE NOT EXISTS (SELECT proid FROM products WHERE acsacsproid=proid);