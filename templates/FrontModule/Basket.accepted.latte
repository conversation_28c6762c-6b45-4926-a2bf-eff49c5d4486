{$pageTitle  = 'Objednávka '.$order->ordcode.' byla odesl<PERSON>a'}
{$pageRobots = "nofollow,noindex"}

{block #content}
<h1>{$pageTitle}</h1>
{ifset $textBlocks["objednavka_odeslana"]}{$textBlocks["objednavka_odeslana"]|noescape}{/ifset}

{if $payment->delcode == 'cetelem'}
<form action="{$cetelem->conf["url"].$cetelem->conf["urlreqs"]}" method="post">
<input type="hidden" name="kodProdejce" value="{$cetelem->kodProdejce}" />
<input type="hidden" name="cena<PERSON>bozi" value="{(int)$cetelem->cena<PERSON>bozi}" />
<input type="hidden" name="calc" value="1" />
<input type="hidden" name="url_back_ok" value="{plink Cetelem:backOk}" />
<input type="hidden" name="url_back_ko" value="{plink Cetelem:backKo}" />
<input type="hidden" name="obj" value="{$order->ordcode}" />
<input type="hidden" name="numklient" value="{$order->ordcode}_{$order->ordusrid}" />
<p class="submit"><input class="btn" type="submit" name="submit" value="Zažádat o splátky" /></p>
</form>
{/if}

{if $payment->delcode == 'creditcard'}
  <br />
  <p>Zvolili jste on-line platbu <strong>kartou</strong>. Pro dokončení objednávky klikněte prosím na <a class="btn btn--info" id="final" href="{plink Payment:openGate $order->ordid.substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8)}">Zaplatit</a>. </p>
{/if}

{if !empty($payUPar)}
{*platebni brana*}
<br />
<h3>Zvolili jste on-line platbu <strong>PayU</strong>. Pro dokončení objednávky klikněte prosím na Zaplatit. Za 10s budete přesměrování na PayU automaticky.</h3>
<form id="koupit" action="https://secure.payu.com/paygw/UTF/NewPayment" method="POST" name="payform">
<input type="hidden" name="pos_id" value="{$payUPar["posId"]}">
<input type="hidden" name="pos_auth_key" value="{$payUPar["posAuthKey"]}">
<input type="hidden" name="session_id" value="{$payUPar["sesId"]}">
<input type="hidden" name="amount" value="{$payUPar["value"]}">
<input type="hidden" name="pay_type" value="{$payUPar["payCode"]}" >
<input id="payu_pay_type_id" type="hidden" value="" name="pay_type_{$payUPar["payCode"]}">
<input type="hidden" name="desc" value="{$payUPar["desc"]}">
<input type="hidden" name="order_id" value="{$order->ordcode}">
<input type="hidden" name="client_ip" value="{$payUPar["clientIp"]}">
<input type="hidden" name="email" value="{$order->ordmail}">
<input type="hidden" name="first_name" value="{$order->ordiname}">
<input type="hidden" name="last_name" value="{$order->ordilname}">
<input type="hidden" name="street" value="{$order->ordistreet} {$order->ordistreetno}">
<input type="hidden" name="city" value="{$order->ordicity}">
<input type="hidden" name="post_code" value="{$order->ordipostcode}">
<input type="hidden" name="language" value="cs">
<input type="hidden" name="sig" value="{$payUPar["sig"]}">
<input type="hidden" name="ts" value="{$payUPar["ts"]}">
<!--<input type="hidden" name="add_test" value="1">
<input type="hidden" name="add_testid" value="{$payUPar["sesId"]}">-->

<script type="text/javascript">
  setTimeout(function(){
    $('#final').trigger('click');
  }, 10000);
</script>

<p id="final"><input class="btn" type="submit" value="Zaplatit"></p>
</form>
{/if}

{if !empty($qrPlatba)}
<br />
<h3>Zvolili jste platbu <strong>převodem na náš účet</strong>, daňový doklad Vám bude zaslán se zbožím.</h3>
{if $payment->delcode == 'paybefore'}
<strong>Platební údaje:</strong><br />
Číslo účtu: {if $order->ordcurid==1}{$presenter->config["SERVER_ACCNO"]}{else}TatraBanka **********/1100{/if}<br />
Variabilní symbol: {$order->ordcode}<br />
Částka: {$order->ordpricevat|formatPrice}<br>
{/if}
<br>
<strong>QR platební kód pro vaši mobilní banku:</strong><br />
<img src="https://quickchart.io/qr?text={$qrPlatba|noescape}" title="QR Platba" /><br>
{/if}

{* Google Analytics eComerce *}
{php
$dphSum = $order->ordpriceinclvat - $order->ordpricenovat;
  $proIds = array();
  $productsSum = 0;
}
<script type="text/javascript">
  <!--
_gaq.push(['_addTrans',
  {$order->ordcode},
  "eShop" + {$presenter->config["SERVER_NAMESHORT"]},
  {$order->ordpricenovat},
  {$dphSum},
  {$order->orddelprice},
  {$order->ordicity},
  {$order->ordicity},
  "Ceska Republika"
]);
{ifset $order->items}
{foreach $order->items as $row}

  {php
  $proIds[] = $row->oriproid;
  $productsSum += ($row->oriprice * $row->oriqty);
  }

_gaq.push(['_addItem',
  {$order->ordcode},
  {$row->oriproid},
  {$row->oriname},
  {$row->catpath},
  {$row->oripricenovat},
  {$row->oriqty}"
]);
{/foreach}
{/ifset}
_gaq.push(['_trackTrans']);
//-->
</script>
{* Heureka konverze PPC *}
{if !empty($heurekaConfig["KeyMereniKonverzi"])}
<script type="text/javascript">
var _hrq = _hrq || [];
    _hrq.push(['setKey', {$heurekaConfig["KeyMereniKonverzi"]}]);
    _hrq.push(['setOrderId', {$order->ordid}]);
{foreach $order->items as $row}
    _hrq.push(['addProduct', {$row->oriname}, {$row->oriprice}, {$row->oriqty}]);
{/foreach}
    _hrq.push(['trackOrder']);

(function() {
    var ho = document.createElement('script'); ho.type = 'text/javascript'; ho.async = true;
    ho.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.heureka.cz/direct/js/cache/1-roi-async.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ho, s);
})();
</script>
{/if}
<!-- Google Code for Objednavka Conversion Page -->
<script type="text/javascript">
  /* <![CDATA[ */
  var google_conversion_id = 1030312203;
  var google_conversion_language = "cs";
  var google_conversion_format = "1";
  var google_conversion_color = "ffffff";
  var google_conversion_label = "ivjtCM_6rQEQi6Kl6wM";
  var google_conversion_value = {$order->ordpricevat};
  var google_conversion_currency = "CZK";
  var google_remarketing_only = false;
  /* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>
<noscript>
  <div style="display:inline;">
    <img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/1030312203/?value={$order->ordpricevat|noescape}&amp;currency_code=CZK&amp;label=ivjtCM_6rQEQi6Kl6wM&amp;guid=ON&amp;script=0"/>
  </div>
</noscript>

<iframe width="119" height="22" frameborder="0" scrolling="no" src="//c.imedia.cz/checkConversion?c=100021210&color=ffffff&v={$order->ordpricevat|noescape}"></iframe>
  {php
  $proIdsStr = '["'.implode('","', $proIds).'"]';
  }
<script>
  fbq('track', 'Purchase', {
    content_ids: {$proIdsStr},
    content_type: 'product_group',
    value: {$productsSum},
    currency: {$curKey}
  });
</script>
{/block}
