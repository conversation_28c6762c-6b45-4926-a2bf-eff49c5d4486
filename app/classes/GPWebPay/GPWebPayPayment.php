<?php

/**
 * https://github.com/Pixidos/gpwebpay-core
 */

use Pixidos\GPWebPay\Config\Factory\ConfigFactory;
use Pixidos\GPWebPay\Config\Factory\PaymentConfigFactory;
use Pixidos\GPWebPay\Config\PaymentConfigProvider;
use Pixidos\GPWebPay\Data\ResponseInterface;
use Pixidos\GPWebPay\Factory\RequestFactory;
use Pixidos\GPWebPay\Factory\ResponseFactory;
use Pixidos\GPWebPay\ResponseProvider;
use Pixidos\GPWebPay\Signer\SignerFactory;
use Pixidos\GPWebPay\Signer\SignerProvider;
use Pixidos\GPWebPay\Data\Operation;
use Pixidos\GPWebPay\Param\Amount;
use Pixidos\GPWebPay\Param\Currency;
use Pixidos\GPWebPay\Param\MerOrderNum;
use Pixidos\GPWebPay\Param\OrderNumber;
use Pixidos\GPWebPay\Param\ResponseUrl;
use Pixidos\GPWebPay\Enum\Currency as CurrencyEnum;
use Tracy\Debugger;

class GPWebPayPayment {

  private \Pixidos\GPWebPay\Config\Config $gateConfig;


  private SignerProvider $signerProvider;
  public $lastError = "";


  public function __construct(array $config) {

    $this->config = $config;

    $configFactory = new ConfigFactory(new PaymentConfigFactory());

    $this->gateConfig = $configFactory->create([
      ConfigFactory::PRIVATE_KEY => __DIR__ . '/keys/' . $config["privateKeyFile"],
      ConfigFactory::PRIVATE_KEY_PASSPHRASE => $config["privateKeyPassphrase"],
      ConfigFactory::PUBLIC_KEY => __DIR__ . '/keys/' . $config["publicKeyFile"],
      ConfigFactory::URL => $config["gateUrl"],
      ConfigFactory::MERCHANT_NUMBER => $config["merchantNumber"],
      ConfigFactory::DEPOSIT_FLAG => $config["depositFlag"],
      //ConfigFactory::RESPONSE_URL => ;
    ]);

    $this->signerProvider = new SignerProvider(new SignerFactory(), $this->gateConfig->getSignerConfigProvider());
  }

  public function openGate($order, $transactionId, $returnUrl) {

    try {

      $operation = new Operation(
        new OrderNumber($transactionId),
        new Amount($order->ordpricevat),
        new Currency(CurrencyEnum::CZK()),
        'default', // leave empty or null for default key
        new ResponseUrl($returnUrl) // you can setup by config responseUrl:
      );

      //doplním zákaznické označní platby - číslo objednávky
      $merOrderNum = New MerOrderNum($order->ordcode);
      $operation->addParam($merOrderNum);

      $requestFactory = new RequestFactory($this->gateConfig->getPaymentConfigProvider(), $this->signerProvider);
      $request = $requestFactory->create($operation);
      return $request->getRequestUrl();
    } catch (\Exception $e) {
      $this->lastError = "inicializace platby se nezdařila: " . (string)$e->getMessage();
    }
    return FALSE;
  }

  public function verifyPaymentResponse(ResponseInterface $response): bool {
    $provider = new ResponseProvider($this->gateConfig->getPaymentConfigProvider(), $this->signerProvider);

    if ($provider->verifyPaymentResponse($response)) {
      if ($response->hasError()) {

        $this->lastError = 'ProcessResponse error';
        return FALSE;
      }
    }
    return TRUE;
  }

  public function getResponse($parameters) {
    try {

      $responseFactory = new ResponseFactory($this->gateConfig->getPaymentConfigProvider());
      return $responseFactory->create($parameters);

    } catch (\Exception $e) {
      $this->lastError = "ProcessResponse Error: " . $e->getMessage();
    }

    return FALSE;
  }

}
