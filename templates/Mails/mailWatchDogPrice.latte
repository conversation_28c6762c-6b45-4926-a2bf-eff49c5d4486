<p><strong>Na {$presenter->config["SERVER_NAME"]} jsme <PERSON>, k<PERSON><PERSON></strong>.</p>

<p>Nastavil(a) jste si hlídání ceny na těcht<PERSON>, kter<PERSON> jsou nyní levnějš<PERSON>. Uvedené ceny uvidíte po přihlášení na svůj účet.</p>

{foreach $products as $row}
  {if $iterator->isFirst()}
    <p>
  {/if}
  <a href="{plink //:Front:Product:detail, $row->proid, ($row|getProKey)}">{$row->proname}</a>, cena {$row["propricea"]|FormatPrice}<br>
  {if $iterator->isLast()}
    </p>
  {/if}
{/foreach}

{include 'mailFooter.latte'}
