<?php
namespace FrontModule;
use dibi;
use Model\OrdersModel;
use Model\UsersModel;
use Nette;

final class BatchPresenter extends BasePresenter {

  public function actionSetOrderStatus() {
    $k = (string)$this->getParameter("k");
    if ($k !== 'jdx9w7xpee') {
      $this->terminate();
    }

    $code = (string)$this->getParameter("code");
    $status = (string)$this->getParameter("status");
    $parCode = (string)$this->getParameter("parcel");
    $statusNote = (string)$this->getParameter("note");
    $ords = new OrdersModel();
    $ord = $ords->load($code, "code");

    if ($ord === FALSE || empty($status)) {
      echo "ERROR";
    } else {

      $vals = array(
        "ordid" => $ord->ordid,
        "ordstatus" => (int)$status,
      );
      if (!empty($parCode)) {
        $vals["ordparcode"] = $parCode;
      }

      $this->changeOrderStatus($vals, FALSE, 17, $statusNote);

      echo "OK";
    }
    $this->terminate();
  }

  /**
   * vrací status objednávky
   *
   * @throws Nette\Application\AbortException
   * @throws \Dibi\Exception
   */
  public function actionGetOrderStatus() {
    $k = (string)$this->getParameter("k");
    if ($k !== 'jdx9w7xpee') {
      $this->terminate();
    }

    $code = (string)$this->getParameter("code");
    $ords = new OrdersModel();
    $ord = $ords->load($code, 'code');

    if ($ord === false) {
      echo "ERROR";
    } else {
      echo $ord->ordstatus;
    }

    $this->terminate();
  }

  public function actionNightBatch() {
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') $this->terminate();

    //vyberu vsechny produkty co maji nastavenou novinku a datum vytvoreni je starsi nez X dni.
    $days = (int)$this->config["PRODUCT_NEWAGE"];
    if ($days > 0) {
      $rows = dibi::fetchAll("SELECT proid FROM products WHERE protypid2=1 AND DATE(prodatec + INTERVAL ".$days." DAY) < CURDATE()");
      $ids = array();
      foreach ($rows as $key => $row) {
        $ids[] = $row->proid;
      }
      if (count($ids) > 0) {
        $idsStr = implode(',', $ids);
        dibi::query("UPDATE products SET WHERE proid IN ($idsStr)");
      }
    }
    $str = "";
    //stahnu XML pro ulozenku
    $str = @file_get_contents('https://www.ulozenka.cz/download/pobocky.xml');
    if (!empty($str)) {
      @file_put_contents(TEMP_DIR.'/cache/ulozenka_pobocky.xml', $str);
    }

    //kontrola akčních cen
    $rows = dibi::fetchAll("SELECT * FROM product_prices WHERE prastatus=0 AND CURDATE() BETWEEN pradatefrom And pradateto");
    $pras = new ProductPricesModel();
    foreach ($rows as $row) {
      $pras->activate($row);
    }
    $rows = dibi::fetchAll("SELECT * FROM product_prices WHERE prastatus=1 AND CURDATE() NOT BETWEEN pradatefrom And pradateto");
    foreach ($rows as $row) {
      $pras->deactivate($row);
    }

    file_get_contents('http://www.google.com/webmasters/tools/ping?sitemap=https://www.'.$this->config["SERVER_NAMESHORT"].'/export/sitemap');
    file_get_contents('http://www.bing.com/webmaster/ping.aspx?siteMap=https://www.'.$this->config["SERVER_NAMESHORT"].'/export/sitemap');

    $this->terminate();
  }



  public function actionUpdateBranches() {
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') {
      $this->terminate();
    }

    //odberne mista Balikovna
    $balapi = new \BalikovnaApi();
    $balapi->updateBranches();

    echo "hotovo";
    $this->terminate();
  }
  public function actionMailing() {
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') $this->terminate();
    dibi::getConnection()->onEvent = NULL;

    $pros = new \Model\ProductsModel();
    $mails = new \Model\MailsModel();
    $mailings = new \Model\MailingsModel();
    $usersTable = $mailings->usersTable;

    //zjistim jestli neni nachystane nejake mailovani
    $rows = dibi::fetchAll("SELECT * FROM mailings WHERE mamstatus=1 AND (mamdate<=CURDATE() OR mamdate IS NULL)");
    foreach ($rows as $mailing) {
      //naplnim produkty
      $prosArr = explode("\n", trim($mailing->mamproducts));

      $arrDesc1 = explode("\n", trim($mailing->mamdesc1));
      $arrDesc2 = explode("\n", trim($mailing->mamdesc2));
      $arrDesc3 = explode("\n", trim($mailing->mamdesc3));

      $cnt = 0;
      $products = array();
      foreach ($prosArr as $proid) {
        $pro = $pros->load($proid);
        if ($pro) {
          $pro->promamdesc1 = (isset($arrDesc1[$cnt]) ? $arrDesc1[$cnt] : "");
          $pro->promamdesc2 = (isset($arrDesc2[$cnt]) ? $arrDesc2[$cnt] : "");
          $pro->promamdesc3 = (isset($arrDesc3[$cnt]) ? $arrDesc3[$cnt] : "");
          $products[$cnt] = $pro;
          $cnt ++;
        }
      }
      //nastavim sablonu
      $mailTemplate = $this->createTemplate();
      $mailTemplate->products = $products;


      $mailTemplate->config = $this->config;
      $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailing.latte');
      //adresati
      //Poskládám cenové hladiny
      $priceLevels = array();
      if ($mailing->mampricea == 1) $priceLevels[] = 'a';
      if ($mailing->mampriceb == 1) $priceLevels[] = 'b';
      if ($mailing->mampricec == 1) $priceLevels[] = 'c';
      if ($mailing->mampriced == 1) $priceLevels[] = 'd';

      $users = dibi::fetchAll("SELECT usrid, usrmail, usrprccat, usrdatec FROM ".$usersTable." WHERE usrmaillist IN (%i)", ($mailing->mammaillist === 1 ? 1 : array(0,1)) , " AND usrprccat IN (%s)", $priceLevels, " AND usrstatus=0 ORDER BY usrid");
      $mailTemplate->mailing = $mailing;
      $mailsCnt = 0;
      foreach ($users as $user) {
        //kontrola formatu mailu
        if (!Nette\Utils\Validators::isEmail($user->usrmail)) {
          dibi::query("UPDATE ".$usersTable." SET usrmaillist=0 WHERE usrid=%i", $user->usrid, " LIMIT 1");
          continue;
        }

        $mailTemplate->usrid = $user->usrid;
        $mailTemplate->loKey = md5($user->usrid.$user->usrdatec);
        $mailTemplate->usrmail = $user->usrmail;
        $mailTemplate->mamid = $mailing->mamid;
        $mailTemplate->isMail = TRUE;
        $date = (empty($mailing->mamdate) ? NULL : $mailing->mamdate);
        $templateHtml = (string)$mailTemplate;
        $data = array(
          'malmamid' => $mailing->mamid,
          'malusrid' => $user->usrid,
          'maldate' => $date,
          'malfrom' => $this->config["SERVER_MAILING_MAIL"],
          'malmail' => $user->usrmail,
          'malsubject' => $mailing->mamsubject,
          'malbody' => $templateHtml,
        );
        $mails->insert($data);
        $mailsCnt ++;
      }

      //nastavim ze se maily odesilaji
      $mailings->update($mailing->mamid, array(
        'mamstatus'=>2,
        'mamcntall'=>$mailsCnt,
        'mamdatestart'=> new \DateTime()
      ));
    }
    //spoustim mailovaci davku
    $rows = dibi::fetchAll("SELECT * FROM mails WHERE (maldate<=CURDATE() OR maldate IS NULL) AND malstatus=0 ORDER BY malid LIMIT ".(int)$this->config["SERVER_MAILING_CNT"]);
    $cnt = 0;
    $cntSleep = 0;
    $mamIsBlocked = array();

    if (!empty($this->config["SMTP_SERVER"])) {
      $mailer = new Nette\Mail\SmtpMailer([
        'host' => $this->config["SMTP_SERVER"],
        'port' => $this->config["SMTP_PORT"],
        'username' => $this->config["SMTP_LOGIN"],
        'password' => $this->config["SMTP_PASSW"],
        'secure' => 'ssl', //port 465
      ]);
    } else {
      $mailer = new Nette\Mail\SendmailMailer;
    }

    foreach ($rows as $row) {

      //zjistim jestli prislusny mailing neni pozastaveny
      if (!isset($mamIsBlocked[$row->malmamid])) {
        $mamIsBlocked[$row->malmamid] = (bool)dibi::fetchSingle("SELECT mamstatus=4 FROM mailings WHERE mamid=%i", $row->malmamid);
      }
      if ($mamIsBlocked[$row->malmamid]) continue;

      $mail = new Nette\Mail\Message();
      //$mail->mailer->commandArgs = '-f'.$row->malfrom;
      if (!empty($row->malmamid)) $mail->setHeader('X-mamid', $row->malmamid);
      if (!empty($row->malusrid)) $mail->setHeader('X-usrid', $row->malusrid);
      if (!empty($row->malserid)) $mail->setHeader('X-serid', $row->malserid);

      $mail->setHeader('Precedence', 'bulk');
      $mail->setFrom($this->config["SERVER_NAMESHORT"].' <'.$row->malfrom.'>');
      $mail->setReturnPath($row->malfrom);
      $mail->addReplyTo($row->malfrom);
      $mail->addTo($row->malmail);
      $mail->setSubject($row->malsubject);
      $mail->setHtmlBody($row->malbody);
      try {
        $mailer->send($mail);

        $mails->delete($row->malid);
      } catch (Exception $e) {
        \Tracy\Debugger::log($row->malid.":".$e->getMessage());
      }
      /*
      if ($cnt % 10 == 0) {
        sleep(30);
        $cntSleep++;
      }
      */
      $cnt ++;
      unset($mail);
    }
    foreach ($mamIsBlocked as $key => $value) {
      if ($value) \Tracy\Debugger::log("Maily pro mailing ID $key jsou pozastaveny.");
    }
    //\Tracy\Debugger::log("Odmailováno $cnt mailů.");

    //zjistim jestli neni nejaka davka uz cela odmailovana
    $rows = dibi::fetchAll("SELECT mamid FROM mailings WHERE mamstatus=2");
    foreach ($rows as $row) {
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(malmamid) FROM mails WHERE malmamid=%i", $row->mamid, " AND malstatus=0");
      if ($cnt == 0) {
        $mailings->update($row->mamid, array(
          'mamstatus'=>3,
          'mamleft'=>0,
          'mamdateend'=> new \DateTime()
        ));
      } else {
        $mailings->update($row->mamid, array('mamleft'=>$cnt));
      }
    }
    $this->terminate();
  }

  public function actionWip($hash) {
    $this->takeCareOfWipBinRequest('wip', $hash);
    $this->terminate();
  }

  public function actionBin($hash) {
    $this->takeCareOfWipBinRequest('bin', $hash);
    $this->terminate();
  }

  public function takeCareOfWipBinRequest($type, $hash) {
    $options = $this->neonParameters["mssql"];
    ini_set('mssql.charset', $options["charset"]);
    $msCn = new \Dibi\Connection($options);

    if ($type === 'wip') {
      $viewName = 'HPCRegistraceVIP';
    } else {
      $viewName = 'HPCRegistraceBIN';
    }

    $sql = 'SELECT
    CAST(Hash AS INT) AS Hash,
    CAST(Prijmeni AS TEXT) AS Prijmeni,
    CAST(Jmeno AS TEXT) AS Jmeno,
    CAST(Adresa1 AS TEXT) AS Adresa1,
    CAST(Adresa2 AS TEXT) AS Adresa2,
    CAST(Mesto AS TEXT) AS Mesto,
    CAST(PSC AS TEXT) AS PSC,
    CAST(EMail AS TEXT) AS EMail,
    CAST(Novinky AS BIT) AS Novinky
    FROM ' . $viewName . ' WHERE Hash='.$hash;

    $user = $msCn->fetch($sql);

    if ($user === FALSE) {
      echo "ERR|Hash not exists";
      $this->terminate();
    }

    $email = $user->EMail;
    $usrs = new UsersModel();
    $usr = $usrs->load($email, 'mail');

    $mailTemplate = $this->createTemplate();
    $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailUserBin.latte');

    if ($usr !== FALSE) {
      echo "OK|Account exists";

      $mailTemplate->type = "exists";
      $mailTemplate->user = $usr;

      if ($type === 'wip' && $usr->usrprccat !== 'd') {
        $usrs->update($usr->usrid, array('usrprccat' => 'd'));
      }

      $this->mailSend($usr->usrmail, "Registrace", $mailTemplate);

    } else {
      $mailTemplate->type = "new";
      $vals = array(
        'usrmail' => $this->toUtf8($user->EMail),
        'usrprccat' => ($type==='wip' ? 'd' : 'a'),
        'usriname' => $this->toUtf8($user->Jmeno),
        'usrilname' => $this->toUtf8($user->Prijmeni),
        'usristreet' => $this->toUtf8($user->Adresa1 . " " . $user->Adresa2),
        'usricity' => $this->toUtf8($user->Mesto),
        'usrmaillist' => $user->Novinky,
        'usripostcode' => $user->PSC,
        'usrmailvcode' => NULL,
        'usrmailverified' => 1
      );

      $passw = Nette\Utils\Random::generate(6);
      $vals["usrpassw"] = $this->passwordHash($passw);

      //print_r($vals);
      $usrid = $usrs->insert($vals);
      echo "OK|Account created";
      //zapíšu souhlas GDPR
      //zaloguji, regiistraci
      $usrs->logEvent($usrid, UsersModel::EVENT_REGISTER);
      //zaloguji souhlas s ucováním údajů
      $usrs->logEvent($usrid, UsersModel::EVENT_GDPR);
      if ($user->Novinky) {
        //zaloguji přihlášení do maillingu
        $usrs->logEvent($usrid, UsersModel::EVENT_MAILLIST_ADD);
      }

      $userRow = $usrs->load($usrid);

      //ulozim si do sablony heslo nez se zaheshuje
      $mailTemplate->usrpassw = $passw;

      $mailTemplate->user = $userRow;

      $this->mailSend($userRow->usrmail, "Registrace", $mailTemplate);
      $mailTemplate->usrpassw = "";
      //$this->mailSend($this->config["SERVER_MAIL"], "Nová registrace BIN", "Pravě se registroval nový uživatel: ".$userRow->usriname." ".$userRow->usrilname.' <a href="mailto:'.$userRow->usrmail.'">'.$userRow->usrmail.'</a>');
    }
  }
}
