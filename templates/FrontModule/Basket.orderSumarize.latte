{default $pageTitle='Souhrn objednávky'}
{default $pageRobots = "nofollow,noindex"}

{block #content}

{/block}

{block #orderProgress}
{form orderSumarizeForm}
<div class="order order--4">

  <div class="order-progress">

    <div class="container-fluid">

      <div class="order-progress__item"><a href="{plink default}">{_'Nákupní k<PERSON>'}</a></div>
      <div class="order-progress__item"><a href="{plink orderDelMode}">{_'Doprava a platba'}</a></div>
      <div class="order-progress__item"><a href="{plink orderContact}">{_'Dodací údaje'}</a></div>
      <div class="order-progress__item is-active">{_'Souhrn objednávky'}</div>

    </div>

  </div>

 <div class="container-fluid">
 <div class="order__article">

  <h2>{_'Souhrn informací Va<PERSON>'}</h2>
   <table class="order__table" cellpadding="3" cellspacing="0" border="1">
     <tbody>
     {foreach $basket->items as $id=>$value}
       <tr>
         <td class="order__product">
           <a href="{plink Product:detail, $productRows[$id]->proid, ($productRows[$id]|getProKey)}">
             <img src="{$baseUri}/{($productRows[$id]|getProductPicNameMaster:'list')|noescape}" alt="{$productRows[$id]->proname}">
           </a>
           <a href="{plink Product:detail, $productRows[$id]->proid, ($productRows[$id]|getProKey)}">
             {$productRows[$id]->proname} {if isset($config["VATTYPE_" . $productRows[$id]->provatid])} ({$config["VATTYPE_" . $productRows[$id]->provatid]}% DPH){/if}
           </a>
           {*nezahrnuje se do slevy*}
           {if $productRows[$id]->pronotdisc == 1}<span class="order__star">*</span>{/if}
         </td>
         <td class="order__count">{$value} ks
           {*if (int)$productRows[$id]->proqty > 0 && (int)$productRows[$id]->proqty < (int)$basket->items[$id]}<br />Pouze {$productRows[$id]->proqty}ks{/if*}
         </td>
         <td class="order__price">
           {($productRows[$id]->proprice*$value)|formatPrice}
         </td>
       </tr>
     {/foreach}
     </tbody>
     <tfoot>
     <tr class="order__sumprice">
      <td  class="order__product" colspan="2">{$delname}</td>
      <td class="order__price">{$delprice|formatPrice}</td>
    </tr>
     {if !empty($basket->coupon)}
       <tr class="order__discount">
         <td class="order__product"><strong>{_'Slevový kupón'} {$basket->coupon->coucode}</strong></td>
         <td colspan="3">
           {if ((int)$basket->coupon->couvalue == 0)}
             Získáváte:<br>
             <ul>
               {if $basket->coupon->coupayfree == 1}<li>Platba zdarma</li>{/if}
               {if $basket->coupon->coudelfree == 1}<li>Doprava zdarma</li>{/if}
               {foreach $basket->coupon->products as $product}
                 <li>Sleva {$product["prodiscount"]|formatPrice} na {$product["proname"]} (původní cena: {$product["proprice"]|formatPrice})
                   {if $product["used"] == FALSE}<a href="{plink add $product["proid"]}">vložit do košíku a využít slevu</a>{/if}
                 </li>
               {/foreach}
             </ul>
           {/if}
         </td>
         <td>

         </td>
       </tr>
     {/if}

     {if $basket->discountVal > 0}
      <tr class="order__discount">
        <td><strong>{_'Sleva'} {if $basket->discountVal > 0 && $basket->discountPer > 0}{$basket->discountPer}%{/if}</strong></td>
        <td colspan="3" class="order__price"><strong>{if $basket->discountVal > 0}{$basket->discountVal|formatPrice}{else}bez slevy{/if}</strong></td>
      </tr>
      {/if}
     </tfoot>
   </table>

  <h2>{if empty($formData['ordstname'])}{_'Fakturační a současně doručovací adresa'}{else}Fakturační adresa{/if}</h2>

  <table class="table table--vertical table--small table--parameters table--aligned" cellpadding="3" cellspacing="0" border="1">
    <tbody>
      <tr>
        <th>Jméno, příjmení:</th>
        <td>{$formData['ordiname']} {$formData['ordilname']}</td>
      </tr><tr>
        <th>Název firmy:</th>
        <td>{$formData['ordifirname']}</td>
      </tr><tr>
        <th>Ulice:</th>
        <td>{$formData['ordistreet']} {$formData['ordistreetno']}</td>
      </tr><tr>
        <th>PSČ, město:</th>
        <td>{$formData['ordipostcode']} {$formData['ordicity']}</td>
      </tr><tr>
        <th>Email:</th>
        <td>{$formData['ordmail']}</td>
      </tr><tr>
        <th>Telefon:</th>
        <td>{$formData['ordtel']}</td>
      </tr><tr>
        <th>IČ:</th>
        <td>{$formData['ordic']}</td>
      </tr><tr>
        <th>DIČ:</th>
        <td>{$formData['orddic']}</td>
      </tr>
    </tbody>
  </table>

  {if !empty($formData['ordstname'])}

    <h2>Dodací adresa</h2>

    <table class="table table--vertical table--small table--parameters table--aligned" cellpadding="3" cellspacing="0" border="1">
      <tbody>
        <tr>
          <th>Jméno, příjmení:</th>
          <td>{$formData['ordstname']} {$formData['ordstlname']}</td>
        </tr><tr>
          <th>Název firmy:</th>
          <td>{$formData['ordstfirname']}</td>
        </tr><tr>
          <th>Ulice:</th>
          <td>{$formData['ordststreet']} {$formData['ordststreetno']}</td>
        </tr><tr>
          <th>PSČ, město:</th>
          <td>{$formData['ordstpostcode']} {$formData['ordstcity']}</td>
        </tr>
      </tbody>
    </table>

  {/if}

  {if !empty($formData['ordnote'])}

    <h2>{_'Vzkaz k objednávce'}</h2>

    <table class="table table--vertical table--small table--parameters" cellpadding="3" cellspacing="0" border="1">
      <tbody>
        <tr>
          <td>{$formData['ordnote']|nl2br|noescape}</td>
        </tr>
      </tbody>
    </table>

  {/if}

  </div>
  </div>

  <div class="order__price-final">
  <div class="container-fluid">
    {_'CENA CELKEM'} (včetně DPH)
    <strong>{($basket->priceSumVat + $delprice - $basket->discountVal)|formatPrice}</strong>
  </div>
  </div>

  <div class="container-fluid">
    <div class="row order-controls">

      <div class="col-xs-12 col-sm-6">
        <p>&nbsp;</p>
        <a href="{plink 'orderContact'}" class="btn btn--back btn--big"><i class="icon icon--arrow-left"></i> {_'O krok zpět'}</a>
      </div>

      <div class="col-xs-12 col-sm-6">
        <p>{input agreement} potvrzuji, že souhlasím s <a href="{$baseUri}/obchodni-podminky-t4" target="_blank">obchodními podmínkami</a> obchodu {$presenter->config["SERVER_NAMESHORT"]}.</p>
        <button type="submit" id="frm-orderSumarizeForm-submit" name="_submit" class="btn--big btn--buy">Objednat (objednávka zavazující k platbě)</button>
      </div>

    </div>

 </div>

</div>
{/form}
{/block}
