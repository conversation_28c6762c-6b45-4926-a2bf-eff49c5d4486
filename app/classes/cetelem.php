<?
//CETELEM Class
//(c) <PERSON><PERSON>, <EMAIL>

class Cetelem {
  public $conf;
  public $status;
  public $zprava;

  public $kodProdejce;
  public $kodBaremu;
  public $kodPojisteni;
  public $kodMaterialu;
  public $cenaZbozi;
  public $primaPlatba;
  public $vyseUveru;
  public $pocetSplatek;
  public $odklad;
  public $zdarma;
  public $vyseSplatky;
  public $cenaUveru;
  public $RPSN;
  public $ursaz;
  public $celkovaCastka;

  public function __construct($kodProdejce) {
    $this->kodProdejce=$kodProdejce;
    $this->kodBaremu="";
    $this->kodPojisteni="";
    $this->kodMaterialu="619";
    $this->cenaZbozi="0";
    $this->primaPlatba="0";
    $this->vyseUveru="0";
    $this->pocetSplatek="0";
    $this->odklad="0";
    $this->vyseSplatky="0";
    $this->cenaUveru="0";
    $this->RPSN="0";
    $this->ursaz="0";
    $this->celkovaCastka="0";

    $this->conf["url"]="https://www.cetelem.cz/";
    $this->conf["urlenum"]="webciselnik.php";
    $this->conf["urlcalc"]="webkalkulacka.php";
    $this->conf["urlreqs"]="cetelem2_webshop.php/zadost-o-pujcku/on-line-zadost-o-pujcku";
  }

  public function getCombo($type, $selval=0, $htmlpar='') {
    $murlenum=$this->conf["url"].$this->conf["urlenum"]."?kodProdejce=".$this->kodProdejce."&typ=$type";
    //$murlenum="barem.xml";

    $xmlvals=$this->parseintostruct($murlenum);
    $cmb = array();
    foreach ($xmlvals as $val) {
      If ($val["tag"]=="moznost") {
        $mval=$val["attributes"]["hodnota"];
        $itemtext=$val["value"];
        $cmb[$mval] = $itemtext;
      }
    }
    return $cmb;
  }

  public function calculate() {
    $calcurl=$this->conf["url"].$this->conf["urlcalc"]."?kodProdejce=".$this->kodProdejce."&kodBaremu=".$this->kodBaremu."&kodPojisteni=".$this->kodPojisteni."&kodMaterialu=".$this->kodMaterialu."&cenaZbozi=".$this->cenaZbozi."&vyseUveru=".$this->vyseUveru."&primaPlatba=".$this->primaPlatba."&pocetSplatek=".$this->pocetSplatek;
    //$calcurl="webkalkulacka.xml";
    $xmlvals=$this->parseintostruct($calcurl);
    //echo $calcurl;
    //echo "<pre>";
    //print_r($xmlvals);
    //echo "</pre>";
    $this->status=$xmlvals[1]["value"];
    If ($this->status=="error") {
      $this->zprava="Nepodařilo se provést výpočet. ".(isset($xmlvals[2]["value"]) ? $xmlvals[2]["value"] : "");
    } else {
      foreach ($xmlvals as $val) {
        If ($val["level"]=="3") {
          $nodename=$val["tag"];
          $this->$nodename=$val["value"];
        }
      }
    }
  }

  public function parseintostruct($xmlfile){
    @$xmlcontents=file_get_contents($xmlfile);
    $p=xml_parser_create('');
    xml_parser_set_option($p, XML_OPTION_CASE_FOLDING, 0);
    xml_parser_set_option($p, XML_OPTION_SKIP_WHITE, 1);
    xml_parse_into_struct($p, $xmlcontents, $vals, $index);
    xml_parser_free($p);
    return $vals;
  }
}
