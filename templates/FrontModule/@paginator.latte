{if $paginator->pageCount > 1}
<div class="pagination__wrapper">
  <div class="pagination">

  <strong>Stránka:</strong>

  {foreach $steps as $step}
    {if $iterator->first && $paginator->page > 1}
      <a href="{link this, 'page' => $paginator->page - 1}"><i class="icon icon--dash-back"></i> předchozí</a>
    {/if}

    {if $step == $paginator->page}
      <span class="paginator__current">{$step}</span>
    {else}
      <a href="{link this, 'page' => $step}">{$step}</a>
    {/if}
    {if $iterator->nextValue > $step + 1}<span>&hellip;</span>{/if}

    {if $iterator->last && $paginator->page < count($steps)}
      <a href="{link this, 'page' => $paginator->page + 1}">další <i class="icon icon--dash"></i></a>
    {/if}
  {/foreach}

  </div>
</div>
{/if}
