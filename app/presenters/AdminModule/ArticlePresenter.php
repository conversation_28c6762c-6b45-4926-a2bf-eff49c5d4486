<?php
namespace AdminModule;

use Net<PERSON>,
  dibi,
  Model;

final class ArticlePresenter extends BasePresenter {

  public function renderDefault() {
    $articles = new \Model\ArticlesModel();
    $dataRows = dibi::query("SELECT * FROM articles ORDER BY artdate DESC")
      ->fetchAssoc('artid');

    $this->template->dataRows = $dataRows;
    $this->template->enum_arttypid = $articles->getEnumArtTypId();
    $this->template->enum_arttop = $articles->getEnumArtTop();
  }

  public function renderEdit($id) {
    $articles = new \Model\ArticlesModel();
    $dataRow = array();
    if ($id > 0) {
      $dataRow = $articles->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
    }
    $form = $this['editForm'];
    if (!empty($dataRow->artdate)) $dataRow->artdate = $this->formatDate($dataRow->artdate);
    $form->setDefaults($dataRow);
    $this->template->dataRow = $dataRow;
    $this->template->id = $id;
    $this->template->enum_arttypid = $articles->getEnumArtTypId();
    $this->template->enum_arttop = $articles->getEnumArtTop();

    //nactu prilohy
    $this->template->attachments = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype NOT IN ('jpg', 'png', 'gif')", $id);
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $id);
    if (count($this->template->images) > 0) $this->template->imagesListId = $id;

    //vlozim hlavni obrazek
    $fileName = 'art_'.$id.'.jpg';
    $this->template->mainImageName = "";
    if (file_exists(WWW_DIR."/pic/art/list/$fileName")) $this->template->mainImageName = $fileName;
  }

  public function renderJsImagesList($id) {
    $this->template->images = dibi::fetchAll("SELECT * FROM attachments WHERE ataartid=%i AND atatype IN ('jpg', 'png', 'gif')", $id);
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');

    $articles = new \Model\ArticlesModel();

    $form = $this->createAppForm();
    $form->addSelect('arttypid', 'Typ článku:', $articles->getEnumArtTypId());
    //$form->addSelect('arttop', 'Umístění:', $articles->getEnumArtTop());

    $form->addText('artname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    $form->addText('artdate', 'Datum článku:', 30)
      ->addRule(Nette\Forms\Form::FILLED, '%label: je nutné vyplnit');

    $form->addText('arturlkey', 'URL:', 30);

    $form->addText('arttitle', 'Title:', 100);
    $form->addTextArea('artdescription', 'Anotace:', 75, 3)
      ->setAttribute('maxlength', '255')
      ->setOption('description', Nette\Utils\Html::el('p')->class('charsRemaining'));
    $form->addText('artkeywords', 'Keywords:', 100);

    $form->addTextArea('artbody', 'Popis:', 60, 20);
    $form['artbody']->getControlPrototype()->class('mceEditor');

    $form->addUpload("imageMain", "Hlavní obrázek:")
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete');

    $form->addSelect('artstatus', 'Status:', $articles->getEnumArtStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {

      $article = new \Model\ArticlesModel();
      $id = (int)$this->getParameter('id');
      $formVals = $form->getValues();

      $image = null;
      if ($formVals["imageMain"]->isOk()) $image = $formVals["imageMain"]->toImage();
      unset($formVals["imageMain"]);

      if (empty($formVals["arturlkey"])) $formVals["arturlkey"] = Nette\Utils\Strings::webalize($formVals["artname"]);
      if (empty($formVals["artdate"])) {
        $formVals["artdate"] = new \DateTime();
      } else {
        $formVals["artdate"] = $this->formatDateMySQL($formVals["artdate"]);
      }

      try {
        if ($article->save($id, $formVals)) {
          $this->flashMessage('Uloženo v pořádku');

          if (isset($image)) {
            $sizes = (array($this->config["ARTPICSIZE_BIG"].'xbig', $this->config["ARTPICSIZE_DETAIL"].'xdetail', $this->config["ARTPICSIZE_LIST"].'xlist'));
            //ulozim obrazek
            $this->saveImage($image, WWW_DIR."/pic/art", 'art_'.$id.".jpg", $sizes);
          }

          $this->redirect('default');
        }
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }


  protected function createComponentUploadForm() {
    $form = $this->createAppForm();
    $form->addText("ataname", 'Název přílohy')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addUpload('file', 'Příloha:')
      ->addRule(Nette\Forms\Form::FILLED);
    $form->addSubmit('save', 'Připojit')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'UploadFormSubmitted');
    return $form;
  }

  public function UploadFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $artid = $this->getParameter('id');
      if ($vals["file"]->isOk()) {
        //ulozim do db
        $atts = new \Model\AttachmentsModel();
        $ataVals = array(
          'ataartid'=>$artid,
          'ataname'=>$vals["ataname"],
          'atafilename'=>Nette\Utils\Strings::webalize($vals["ataname"]).'_'.$artid.'.'.substr($vals["file"]->getName(), -3),
          'atasize'=>(string)$vals["file"]->getSize(),
          'atatype'=>substr($vals["file"]->getName(), -3),
        );
        $ataid = $atts->insert($ataVals);
        $ataVals["atafilename"] = Nette\Utils\Strings::webalize($ataVals["ataname"]).'_'.$ataid.'.'.$ataVals["atatype"];
        $atts->update($ataid, $ataVals);
        $vals["file"]->move(WWW_DIR.'/files/'.$ataVals["atafilename"]);
      }
      $this->redirect('edit', $artid);
    }
  }

  public function actionDeleteAttachment($ataid, $artid) {
    $file = dibi::fetch("SELECT * FROM attachments WHERE ataid=%i", $ataid);
    if ($file) {
      @unlink(WWW_DIR.'/files/'.$file->atafilename);
      dibi::query("DELETE FROM attachments WHERE ataid=%i", $ataid);
    }
    $this->redirect('edit', $artid);
  }

}
