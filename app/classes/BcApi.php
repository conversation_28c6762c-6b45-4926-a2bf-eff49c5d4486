<?php

use <PERSON>\Debugger;

class BcApi {

  const LOGIN_URL = 'https://login.microsoftonline.com/1a41b96d-457d-41ac-94ef-22d1901a7556/oauth2/v2.0/token';

  const ENDPOINT_TOKEN = "token";
  const ENDPOINT_PRODUCTS = "webItemsCU";
  const ENDPOINT_INVENTORY = "webInventory";

  /* @string autorizační token */
  private $token;
  private $config;
  private $errors;

  public function __construct(array $config) {
    $this->config = $config;
    $this->token = $this->getToken();
  }

  private function getToken(): string {

    $data = [
      'grant_type' => 'client_credentials',
      'client_id' => $this->config['clientId'],
      'client_secret' => $this->config['clientSecret'],
      'scope' => 'https://api.businesscentral.dynamics.com/.default'
    ];

    $ret = $this->curlExec(self::ENDPOINT_TOKEN, $data);
    return (string)$ret["access_token"];
  }

  public function getProducts() {
    $ret = $this->curlExec(self::ENDPOINT_PRODUCTS, ['$filter'=>"eshopGroup ne ''"], "GET");
    file_put_contents(TEMP_DIR . "/bc-products.txt", var_export($ret, true));
    return $ret;
  }

 public function getInventory() {
    $ret = $this->curlExec(self::ENDPOINT_INVENTORY, ['$filter'=>"locationCode eq '" . $this->config['locationCode'] . "' and inventory gt 0"], "GET");
    \Tracy\Debugger::barDump($ret);
    return $ret;
  }

  private function curlExec($endpoint, $data, $request='POST') {
    $ch = curl_init();
    if ($endpoint === self::ENDPOINT_TOKEN) {
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
      curl_setopt($ch, CURLOPT_POST, 1);
      curl_setopt($ch, CURLOPT_URL, self::LOGIN_URL);
      curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded'));
      curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

    } else {

      if ($request === 'POST') {
        curl_setopt($ch, CURLOPT_POST, 1);

        $payload = json_encode($data);

        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_URL, $this->config['apiUrl'] . $endpoint);

      } else if ($request === 'GET') {

        curl_setopt($ch, CURLOPT_URL, $this->config['apiUrl'] . $endpoint . (count($data) > 0 ? "?" . http_build_query($data) : ""));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST , "GET");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer " . $this->token));
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);

      }

    }

    $ret = curl_exec($ch);
    $err = curl_error($ch);
    curl_close($ch);

    if (!empty($err)) {
      $this->setErrorMsg("cUrl error: $err");
      Debugger::log($err);
      return false;
    }

    //bdump($ret);
    //die($ret);
    return(json_decode($ret, TRUE));
  }

  private function setErrorMsg($text) {
    $this->errors[] = $text;
  }

  public function getErrors() {
    return $this->errors;
  }
}
