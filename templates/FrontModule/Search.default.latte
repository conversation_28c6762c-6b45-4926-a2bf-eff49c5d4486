{$pageTitle       = 'Vyhledávání'}
{$pageDescription = 'Vyhledávání zboží'}
{$canonicalUrl    = $presenter->link('//Search:default', array('name'=>'', 'fulltext'=>'', 's'=>'', 'o'=>'', 'm'=>array()))}
{$pageRobots = "noindex,follow"}

{php
  $GLOBALS["ecommProId"] = '';
  $GLOBALS["ecommPageType"] = 'searchresults';
  $GLOBALS["ecommTotalValue"] = 0;
}

{block #content}
<div class="row">

  <div class="col-xs-12">

    <!-- search filters start -->
    <div class="search__filters">

      {form detailSearchForm}
        {* vypise chyby formulare *}
        {include ../@formErrors.latte form=>$form}

        <fieldset>

          <legend>Vyhledávání</legend>

          <p>
            {php echo $form["name"]->getLabel("Zadejte název nebo část názvu zboží")}
            {php echo $form["name"]->getControl()}
          </p>

          <p>
            {php echo $form["fulltext"]->getLabel("Zadejte jakékoliv hledané slovo")}
            {php echo $form["fulltext"]->getControl()}
          </p>

          <p>
            {input detailSearch}
          </p>

        </fieldset>

      {/form}

    </div>
    <!-- search filters end -->

  </div>

</div>

{* výpis produktů *}
{include @productsList.latte, products => $productsData, title => $pageTitle}

{/block}
