<?php
namespace FrontModule;
use dibi;
use Nette;

final class CommentPresenter extends BasePresenter {
  /** @persistent */
  public $name = '';

  /** @persistent */
  public $fulltext = '';

  /** @persistent */
  public $cat = array();

  public function renderDefault() {
    $cmts = new \Model\CommentsModel();
    $and = false;
    $query[] = "
    SELECT * 
    FROM comments 
    LEFT JOIN products ON (cmtproid=proid)
    WHERE cmtreid=0 ";
    if (!empty($this->name)) {
      array_push($query, " AND proname LIKE %~like~", $this->name);
    }

    if (!empty($this->fulltext)) {
      array_push($query, " AND cmttext LIKE %~like~", $this->fulltext);
      $and = true;
    }

    if (count($this->cat) > 0) {
      array_push($query, " AND (cmtcatid IN (%i)", $this->cat, " OR cmtcatid2 IN (%i)", $this->cat, ")");
      $and = true;
    }

    //if ($and == false) array_push($query, " AND cmtid=-1");
    array_push($query, " ORDER BY cmtid DESC");

    $ds = $cmts->getDataSource($query);
    $rs = dibi::fetchAll($query);
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = 10;
    $paginator->itemCount = $ds->count();
    $rows = $ds->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();

    foreach ($rows as $key => $row) {
      $rows[$key]->res = dibi::fetchAll("SELECT * FROM comments WHERE cmtreid=%i", $row->cmtid, " ORDER BY cmtid DESC");
    }
    $this->template->rows = $rows;

  }

  protected function createComponentCommentForm() {
    $cmts = new \Model\CommentsModel();
    $enu_cmtcatid = $cmts->getEnumCmtCatId();
    $reid = (int)$this->getParameter("reid");
    $re = (string)$this->getParameter("subj");

    $form = $this->createAppForm();
    $form->addGroup("Vložte svůj dotaz");
    $form->addHidden("cmtreid", 0);
    $form->addText("cmtnick", "Jméno/přezdívka:", 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Vaše jméno/přezdívku.');
    $form->addText("cmtmail", "Email:", 40)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');
    $form->addText("cmtsubj", "Titulek:", 40)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte titulek.');
    $form->addTextArea("cmttext", "Text:", 80, 10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text.');

    if ($reid > 0) {
      $form["cmtreid"]->setDefaultValue($reid);
      $form["cmtsubj"]->setDefaultValue($re);
    }

    $form->addSubmit('submit', 'Vložit obecný dotaz')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'commentFormSubmitted');
    return $form;
  }

  public function commentFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $couCode = $this->appNamespace->countryCode;
      if ($couCode == 'cz' || $couCode == 'sk' || $couCode == '') {
        $vals = $form->getValues();
        $cmts = new \Model\CommentsModel();
        $cmts->insert($vals);
        $this->flashMessage("Děkujeme za Váš dotaz");
      } else {
        $form->addError("Nekorektně odeslaný formulář.");
        return false;
      }
      $this->redirect('default'.(!empty($vals["cmtreid"]) ? '#g'.$vals["cmtreid"] : ""));
    }
  }

  protected function createComponentCommentSearchForm() {
    $cmts = new \Model\CommentsModel();
    $enu_cmtcatid = $cmts->getEnumCmtCatId();
    unset($enu_cmtcatid[0]);
    $form = $this->createAppForm();
    $form->addGroup();
    $form->addtext("proname", "Název zboží:", 40);
    if (!empty($this->name)) $form["proname"]->setDefaultValue($this->name);

    $form->addtext("fulltext", "Slovní spojení:", 40);
    if (!empty($this->fulltext)) $form["fulltext"]->setDefaultValue($this->fulltext);

    $form->addGroup("Témata");
    $container = $form->addContainer('comcat');
    foreach ($enu_cmtcatid as $id => $name) {
      $container->addCheckbox($id, $name);
      if (isset($this->cat[$id])) $container[$id]->setDefaultValue(TRUE);
    }
    $form->addSubmit('commentSearch', 'Vyhledat')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $this->name = $vals["proname"];
      $this->fulltext = $vals["fulltext"];
      $this->cat = array();
      foreach ($vals['comcat'] as $key => $val) {
        if ($val) $this->cat[$key] = $key;
      }
      $this->redirect('default');
    }
  }
}
