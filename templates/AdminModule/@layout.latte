{default $title=''}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  {* The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags *}
  <title>{$title} - ADMINISTRACE {$presenter->config["SERVER_NAME"]}</title>
  <meta name="robots" content="noindex,nofollow">
  <meta name="googlebot" content="noindex,nofollow">
  <!-- Bootstrap -->
  <!-- Latest compiled and minified CSS -->
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
  <link rel="stylesheet" href="{$baseUri}/admin/css/custom.css">
  <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
  <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
  <!--[if lt IE 9]>
  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
  <![endif]-->
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>
  <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
  <!-- Latest compiled and minified JavaScript -->
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
  <script src="{$baseUri}/admin/js/live-form-validation.js"></script>
  <script src="{$baseUri}/admin/js/custom.js"></script>
  <script src="https://cloud.tinymce.com/stable/tinymce.min.js?apiKey=xqhfjpyj031266xbhl4e90bj4ijd0p7frfb1j038kx1gkgue"></script>
  <script type="text/javascript">
  tinymce.init({
  selector: '.mceEditor',
  height: 500,
  language: 'cs',
  language_url: {$baseUri}+"/admin/js/tinimce/langs/cs.js",
  menubar: false,
  paste_as_text: true,
  nonbreaking_force_tab: true,
  element_format: "html",
  entity_encoding: "raw",
  statusbar: false,
  convert_urls: false,
  plugins: [
  'advlist autolink lists link image charmap print preview anchor',
  'searchreplace visualblocks code fullscreen',
  'insertdatetime media table contextmenu paste code'
  ],
  toolbar: 'undo redo | insert | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | charmap subscript superscript | code',
  content_css: '//www.tinymce.com/css/codepen.min.css'
  });

  function DeleteConfirm(msgadd) {
    return window.confirm("Opravdu chcete smazat " + msgadd + " ?");
  }
  </script>
</head>

<body>
    {*horni menu*}
    {include '@menuTop.latte'}

    {*obsah*}
    <div class="container-fluid">
        <div class="page-header">
          <h3>{$title}</h3>
          {foreach $flashes as $flash}
            <div class="alert {if $flash->type == 'err'}alert-warning{else}alert-success{/if}">{$flash->message}</div>
          {/foreach}
        </div>
        {block content}{/block}
    </div>

    {*paticka*}
    <footer class="footer">
      <div class="container-fluid">
        <p class="text-muted">
          Databáze: {$dbName} |
          <a href="{$baseUri}" target="front">Veřejná část</a> |
          {if $admin->admid > 0}
            Přihlášen/a <a href="{plink Admin:edit $admin->admid}">{$admin->admname} ({$admin->admmail})</a>
          {else}
            Nepřihlášen/a
          {/if}
        </p>
      </div>
    </footer>
</body>
</html>

