// ovládací prvky

.control {

  display: inline-block;

  width: 16px;
  height: 16px;

  margin: 3px 0;

  color: @color_white;
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  text-decoration: none;
  text-align: center;

  background-color: @color_info;
  border-radius: 8px;

  cursor: pointer;

  // zabránění oz<PERSON>
  user-select: none;

  // větší verze pro malé rozlišení
  @media (max-width: @mqxs) {
    width: 24px;
    height: 24px;
    font-size: 24px;
    line-height: 24px;
    border-radius: 12px;
  }

  // tisk<PERSON> verze
  @media print {
    display: none;
  }

  // hover efekty
  &:hover, &:active, &:focus {
    background-color: darken(@color_info, 10%);
  }

  // úprava pokud je uvnitř ikona
  .icon {

    font-size: 12px;
    margin-top: 2px;

    // větší verze pro malé rozlišení
    @media (max-width: @mqxs) {
      font-size: 18px;
    }

  }

}
a.control {
  color: @color_white !important; // přerazíme pokud by do<PERSON><PERSON> k <PERSON> bar<PERSON> od<PERSON>
}

  // velké tlačítko
  .control--big {

    width: 32px;
    height: 32px;
    font-size: 32px;
    line-height: 32px;
    border-radius: 16px;

    // úprava pokud je uvnitř ikona
    .icon {
      font-size: 27px;
      margin-top: 1px;
    }

  }

  // tlačítko potvrzení
  .control--success {

    background-color: @color_success;

    &:hover, &:active, &:focus {
      background-color: darken(@color_success, 10%);
    }

  }

  // tlačítko odstranit/zavřít
  .control--remove {

    background-color: @color_danger;

    &:hover, &:active, &:focus {
      background-color: darken(@color_danger, 10%);
    }

  }

  // input s počtem kusů
  .control--count {

    white-space: nowrap; // nechceme zalamovat ovládací prvky

    // omezíme velikost inputu
    input {
      max-width: 75px;
    }

  }
