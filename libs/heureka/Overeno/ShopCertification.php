<?php

namespace Heureka;

use <PERSON>ureka\ShopCertification\ApiEndpoint;
use <PERSON>ureka\ShopCertification\DuplicateProductItemIdException;
use <PERSON>ureka\ShopCertification\InvalidArgumentException;
use <PERSON>ureka\ShopCertification\IRequester;
use <PERSON>ureka\ShopCertification\Response;

/**
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
class ShopCertification
{

    const HEUREKA_CZ = 0;
    const HEUREKA_SK = 1;

    /**
     * @var string
     */
    private $apiKey;

    /**
     * @var IRequester
     */
    private $requester;

    /**
     * @var array
     */
    private $options = [];

    /**
     * @var string
     */
    private $email;

    /**
     * @var string
     */
    private $orderId;

    /**
     * @var string[]
     */
    private $productItemIds = [];

    /**
     * Used to prevent double sending of the order
     *
     * @var bool
     */
    private $orderSent = false;

    /**
     * @param string          $apiKey
     * @param array           $options
     * @param IRequester|null $requester
     */
    public function __construct($apiKey, array $options = [], IRequester $requester = null)
    {
        $this->apiKey = $apiKey;

        $defaultOptions = [
            'service' => self::HEUREKA_CZ,
        ];

        $this->options = array_merge($defaultOptions, $options);
        $apiEndpoint = new ApiEndpoint($this->options['service']);

        if ($requester === null) {
            if (function_exists('curl_version')) {
                $requester = new ShopCertification\CurlRequester();
            } else {
                $requester = new ShopCertification\PhpRequester();
            }
        }

        $requester->setApiEndpoint($apiEndpoint);

        $this->requester = $requester;
    }

    /**
     * @param string $email Customer's e-mail address
     *
     * @return self
     */
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @param int $orderId ID of the customer's order
     *
     * @return self
     * @throws InvalidArgumentException
     */
    public function setOrderId($orderId)
    {
        if (!is_int($orderId)) {
            throw new InvalidArgumentException(
                sprintf('OrderId must be an integer, "%s" given.', print_r($orderId, true))
            );
        }

        $this->orderId = $orderId;

        return $this;
    }

    /**
     * @param string $productItemId Must be same as ITEM_ID provided in the Heureka XML feed.
     *
     * @return ShopCertification
     * @throws DuplicateProductItemIdException
     */
    public function addProductItemId($productItemId)
    {
        $productItemId = (string)$productItemId;

        if (array_search($productItemId, $this->productItemIds) !== false) {
            throw new DuplicateProductItemIdException(
                'The productItemId "%s" was already added. Please check the implementation.'
            );
        }

        $this->productItemIds[] = $productItemId;

        return $this;
    }

    /**
     * Sends the data you set to the Heureka ShopCertification service.
     *
     * @return Response
     *
     * @throws ShopCertification\Exception
     */
    public function logOrder()
    {
        if ($this->orderSent) {
            throw new ShopCertification\Exception('You already sent one order. Please check your implementation.');
        }

        if (!$this->email) {
            throw new ShopCertification\MissingInformationException("Customer email address isn't set and is mandatory.");
        }

        $data['apiKey'] = $this->apiKey;
        $data['email'] = $this->email;

        if ($this->orderId) {
            $data['orderId'] = $this->orderId;
        }

        $data['productItemIds'] = $this->productItemIds;

        $result = $this->requester->request(IRequester::ACTION_LOG_ORDER, $data);
        if ($result->code !== 200) {
            throw new ShopCertification\Exception("Unexpected response:\n" . print_r($result, true));
        }

        $this->orderSent = true;
    }

}
