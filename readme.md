#EKRAMEK

Základní informace k projektu.

##Postup instalace "na čisto"

1. klonovat aktuální verzi příslušného projektu z bitbucket.org
2. vytvořit databázi a naplnit z dumpu databáze z adresáře /sql/
3. vytvořit adresáře /temp/ a /log/ a nastavit práva zápisu z PHP pokud je třeba
4. zkopírovat soubor /www/.htaccess.example jako .htaccess a upravit pokud je třeba
5. zkopírovat soubor /app/config/config.local.neon.example jako config.local.neon a upravit (především napojení do DB)
6. zkopírovat obrázky v adresáři /img/ a /pic/

##CSS

Kompilace do CSS řeší Grunt.js, je použit preprocesor LESS, zde je rovněž nasazen autoprefixer, minifikace a legaCSSy (podpora starších IE). Více informací v souboru `css/styles.less`.

###Základní struktura stylů

* **css/styles.less** - základní struktura stylů, propojení na další části
  * **css/base** - základní vlastnosti, normalizační soubor
  * **css/core** - nastavení, mixiny, zde je nejdůležitější soubor `settings.less` který obsahuje barvy a veškeré nastavení
  * **css/components** - jednotlivé komponenty stylů, rozděleny do podložek podle hlavních částí
  * **css/bootstrap** - framework Bootstrap, použit pouze grid, proměnné a mixiny


##JavaScript

Je používána jQuery ve verzi 1.12.4, spojování stylů a minifikaci řeší Grunt.js.

###Další použité knihovny

* **webfont.js** - loader pro Google Fonts, asynchronní odložené načítání - https://github.com/typekit/webfontloader
* **jquery.magnific-popup.js** - lightbox skript pro jQuery - https://github.com/dimsemenov/Magnific-Popup
* **jquery.unveil.js** - lazy loading obrázků pro jQuery - https://github.com/luis-almeida/unveil

##Grunt

Grunt řeší spojování, generování a minifikaci LESS a JS souborů, optimalizaci obrázků, autoprefixování, atd.

###Grunt - instalace

Vyčerpávající návod je například zde: http://www.vzhurudolu.cz/prirucka/node-instalace - níže stručná instalace:

* stáhnout a nainstalovat Node.js - https://nodejs.org/en/ (nutný restart počítače po první instalaci)
* spustit konzoli a najít cestu k projektu (cd + přetáhnout adresář projektu)
* zadat `npm install -g grunt-cli`
* zadat `npm install grunt --save-dev`
* zadat `npm install`
* vytvoří se adresář "node_modules" který neverzovat a nezasahovat do něj
* po instalaci stačí zadat `grunt` který bude hlídat změnu souborů a generovat potřebné


###Grunt - použití

* Před nasazením na produkci použijte `grunt deploy` task, který spojí a minifikuje CSS a JS soubory a provede optimalizaci obrázků.
* Základní task `grunt` provádí pouze sledování změn, spojování CSS a JS souborů z důvodů rychlosti.
* Lze využít také tasky `grunt makejs` (spojí a minifikuje JS) a `grunt makecss` (vygeneruje CSS z LESS souborů, spustí autoprefixer a minifikuje CSS).

##ENGINE

###Textové bloky
* textové bloky se definují v administraci Obsah->Seznam textovách bloků / Nový blok
* pro následné odkazování na textový blok v šabloně se používá pole Kód
* ve FRONTu se všechny textové bloky načítají a cachují a předávají do šablon v proměnné `$textBlocks`
* na obsah příslušného textového bloku je pak možné se v šabloně odkázat takto:
`{ifset $textBlocks["kod_textoveho_bloku"]}{!$textBlocks["kod_textoveho_bloku"]}{/ifset}`

