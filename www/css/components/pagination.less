// stránkování

.pagination {

  clear: both;
  max-width: 1200px;
  font-size: 15px;
  text-align: center;

  // základní definice boxů
  a, span {
    display: inline-block;
    padding: 7px 10px;
    margin: 2px 0;
  }

  a {

    background-color: @color_main_light;
    color: @color_white;
    text-decoration: none;

    &:hover, &:active, &:focus {
      color: @color_white;
      background-color: @color_main;
    }

  }

  // úvodní text "stránka"
  strong {
    margin-right: 15px;
    font-weight: 400;
  }

  .icon {
    margin-top: 2px;
    font-size: 10px;
  }

}

  // obal strán<PERSON>
  .pagination__wrapper {

    margin-top: 30px;
    padding: 3% 0% 3% 0;
    background-color: @color_gray_light;
    border-top: 1px solid @color_gray;

    // pokud je zobrazen sidebar, tak odskočíme pro vycentrování s obsahem
    @media (min-width: @mqsm) {
      padding-left: 33.33333333%;
    }
    @media (min-width: @mqmd) {
      padding-left: 25%;
    }

  }
