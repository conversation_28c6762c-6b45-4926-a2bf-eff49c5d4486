// výpis produktů

.product {

  padding: 35px 10px 30px 10px; // kompenzujeme padding na tlačítcích
  text-align: center;
  background-color: #fff;

  // štítky u produktu
  // přetěžujeme pozici - chceme formátovat pouze na výpisu
  .labels {

    // vystrčení z obrázku
    position: absolute;
    top: 5px;
    left: -5px;
    width: 1px; // pro zarovnání <PERSON>t<PERSON>tk<PERSON> pod sebe a zachování inline bloku štítků
    text-align: left;
    z-index: 1;

    // men<PERSON><PERSON> mezery mezi štítky
    .label {
      margin-bottom: 2px;
    }

  }

}

  // obal produktu
  .product__wrapper {
    border-bottom: 1px solid @color_gray_light;
  }

  // hlavička produktu
  .product__header {

    margin: 0 0 20px 0;
    color: @color_main_light;
    font-size: 15px;
    line-height: 1.2;
    text-transform: uppercase;
    font-weight: 400;

    @media (min-width: @mqxxs) {
      overflow: hidden; // zabránění přetečení nadpisu
      height: 39px; // rezerva pro dva řádky, pouze na vyšších rozlišeních
      font-size: 17px;
    }

    a {

      color: @color_main_light;
      text-decoration: none;

      &:hover, &:active, &:focus {
        color: @color_main_dark;
      }

    }

  }

  // obrázek produktu
  .product__image {

    position: relative; // pro absolutní pozicování štítků
    margin: 15px 0;

    img {
      width: 100%;
    }

    a {

      display: block;

      &:hover, &:active, &:focus {
        opacity: 0.8;
      }

    }

  }

  // zkrácený text produktu
  .product__info {
    margin: 0.25em 0 0.75em 0;
    font-size: 13px;
  }

  // cena
  .product__price {

    margin: 10px 0;

    font-size: 13px;
    color: @color_price;

    // částka ceny
    strong {
      font-size: 20px;
    }

  }

  // ovládací prvky (tlačítka, inputy)
  .product__controls {

    // mezera mezi tlačítky na nižších rozlišeních
    a {
      margin-bottom: 5px;
    }

  }
