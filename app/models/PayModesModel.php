<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class PayModesModel extends BaseModel {

  protected $tableName = "paymodes";
  protected $fieldPrefix = "pay";

  /********************* ciselniky *********************/

  /**
  * ciselnik delstatus
  * @return array
  */
  public function getEnumPayStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  /**
  * ciselnik zpusoby dodani
  * @return array
  */
  public function getEnumPayModes() {
    $items = dibi::query("SELECT * FROM paymodes ORDER BY payorder")
      ->fetchPairs('payid', 'payname');
    return($items);
  }
}
