<?php

namespace FrontModule;
use dibi;
use Model\OrdersModel;
use Nette;

final class PaymentCsobPresenter extends BasePresenter {

  protected function startup() {
    parent::startup();
  }

  public function renderReturn() {
    $config = $this->neonParameters["csob"];
    $csob = new \CsobPayment($config);
    try {
      $response = $csob->receiveResponse();
      $paymentInfo = $csob->getPaymentInfo($response["payId"]);
      $payStatus = $paymentInfo["status"];
      $eetData = $paymentInfo["eetData"];
    } catch (\Exception $e) {
      $payStatusText = $e->getMessage();
    }

    if($payStatus == 4 || $payStatus == 7) {
      //nastavim obj jako uhrazenou
      $logOrdId = dibi::fetchSingle("SELECT payordid FROM csob_log WHERE payid=%s", $_POST["payId"], " ORDER BY paydatec DESC");
      if ($logOrdId > 0) {
        $ords = new OrdersModel();
        $ord = $ords->load($logOrdId);
        if ($ord) {
          if ($ord->ordpaystatus == 1) {
            $this->template->msg = "Vaši objednávku evidujeme jako již uhrazenou.";
          } else {
            $vals = array(
              'ordstatus'=> 6,
              'ordpaymentid' => $_POST["payId"],
              'ordpaystatus' => 1,
              'orddatepayed' => new \DateTime()
            );
            $ords = new OrdersModel();
            $ords->update($ord->ordid, $vals);
            $ords->logStatus($ord->ordid, $vals["ordstatus"], 1, "CSOB platba platební kartou");
            if (!empty($eetData)) $ords->logEet($ord->ordid, $eetData, "CSOB platba platební kartou");
            $this->template->msg = "Platba byla přijata a objednávka označena jako uhrazena.";

            //mailuji informaci o zaplacení
            $ord = $ords->load($ord->ordid);
            $mailTemplate = $this->createTemplate();
            $mailTemplate->orderRow = $ord;
            $mailTemplate->eetRow = dibi::fetch('SELECT * FROM eet_log WHERE logordid=%i', $ord->ordid);
            $mailTemplate->enum_ordStatus = $ords->getEnumOrdStatus();
            $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderChanged.latte');

            //mailuju zakaznikovi
            try {
              $this->mailSend($ord->ordmail, "Změna stavu objednávky č. ".$ord->ordcode, $mailTemplate);
            } catch (Nette\InvalidStateException $e) {
              $this->flashMessage("Nepodařilo se odeslat informační email o změně stavu objednávky (".$e->getMessage().")".' [KOD:'.$ord->ordcode.']', "danger");
            }

          }
        }
      }
    } else {
      if (!empty($payStatus)) $payStatusText = $csob->getPaymentStatusText($payStatus);
      $this->template->msg = "Aktuální stav platby je: " . $payStatusText;
    }
  }

  public function renderStatus($id) {
    $config = $this->neonParameters["csob"];
    $csob = new \CsobPayment($config);
    $payStatus = $csob->getPaymentStatus($id);
    $payStatusText = $csob->getPaymentStatusText($payStatus);
    $this->template->paymentStatusText = $payStatusText;
  }
}
