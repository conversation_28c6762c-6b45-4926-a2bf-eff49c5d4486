<?php
	$volt = array (
  0 => 
  array (
    'match' => '0CCD 200C',
    'replace' => 'E0AD',
  ),
  1 => 
  array (
    'match' => '200D 0CCD',
    'replace' => 'E0AC',
  ),
  2 => 
  array (
    'match' => '0CC6 0CC2',
    'replace' => '0CCA',
  ),
  3 => 
  array (
    'match' => '0C95 0CCD 0CB7',
    'replace' => 'E07D',
  ),
  4 => 
  array (
    'match' => '0C9C 0CCD 0C9E',
    'replace' => 'E07E',
  ),
  5 => 
  array (
    'match' => '0CB0 0CCD',
    'replace' => 'E00B',
  ),
  6 => 
  array (
    'match' => '((0C95|0C96|0C97|0C98|0C99|0C9A|0C9B|0C9C|0C9D|0C9E|0C9F|0CA0|0CA1|0CA2|0CA3|0CA4|0CA5|0CA6|0CA7|0CA8|0CAA|0CAB|0CAC|0CAD|0CAE|0CAF|0CB0|0CB1|0CB2|0CB3|0CB5|0CB6|0CB7|0CB8|0CB9|E07D|E07E|E0A3)) 0CCD',
    'replace' => '\\1 E0AC',
  ),
  7 => 
  array (
    'match' => '((0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) 0CCD',
    'replace' => '\\1 E0AC',
  ),
  8 => 
  array (
    'match' => '(0CBC) 0CCD',
    'replace' => '\\1 E0AC',
  ),
  9 => 
  array (
    'match' => '(0020) 0CCD',
    'replace' => '\\1 E0AC',
  ),
  10 => 
  array (
    'match' => '(25CC) 0CCD',
    'replace' => '\\1 E0AC',
  ),
  11 => 
  array (
    'match' => '0C95 0CBC',
    'replace' => 'E0E6',
  ),
  12 => 
  array (
    'match' => '0C96 0CBC',
    'replace' => 'E0E7',
  ),
  13 => 
  array (
    'match' => '0C97 0CBC',
    'replace' => 'E172',
  ),
  14 => 
  array (
    'match' => '0C98 0CBC',
    'replace' => 'E173',
  ),
  15 => 
  array (
    'match' => '0C99 0CBC',
    'replace' => 'E174',
  ),
  16 => 
  array (
    'match' => '0C9A 0CBC',
    'replace' => 'E175',
  ),
  17 => 
  array (
    'match' => '0C9B 0CBC',
    'replace' => 'E176',
  ),
  18 => 
  array (
    'match' => '0C9C 0CBC',
    'replace' => 'E0E8',
  ),
  19 => 
  array (
    'match' => '0C9D 0CBC',
    'replace' => 'E0E9',
  ),
  20 => 
  array (
    'match' => '0C9E 0CBC',
    'replace' => 'E177',
  ),
  21 => 
  array (
    'match' => '0C9F 0CBC',
    'replace' => 'E178',
  ),
  22 => 
  array (
    'match' => '0CA0 0CBC',
    'replace' => 'E179',
  ),
  23 => 
  array (
    'match' => '0CA1 0CBC',
    'replace' => 'E17A',
  ),
  24 => 
  array (
    'match' => '0CA2 0CBC',
    'replace' => 'E17B',
  ),
  25 => 
  array (
    'match' => '0CA3 0CBC',
    'replace' => 'E17C',
  ),
  26 => 
  array (
    'match' => '0CA4 0CBC',
    'replace' => 'E17D',
  ),
  27 => 
  array (
    'match' => '0CA5 0CBC',
    'replace' => 'E17E',
  ),
  28 => 
  array (
    'match' => '0CA6 0CBC',
    'replace' => 'E17F',
  ),
  29 => 
  array (
    'match' => '0CA7 0CBC',
    'replace' => 'E180',
  ),
  30 => 
  array (
    'match' => '0CA8 0CBC',
    'replace' => 'E181',
  ),
  31 => 
  array (
    'match' => '0CAA 0CBC',
    'replace' => 'E182',
  ),
  32 => 
  array (
    'match' => '0CAB 0CBC',
    'replace' => 'E0EA',
  ),
  33 => 
  array (
    'match' => '0CAC 0CBC',
    'replace' => 'E183',
  ),
  34 => 
  array (
    'match' => '0CAD 0CBC',
    'replace' => 'E184',
  ),
  35 => 
  array (
    'match' => '0CAE 0CBC',
    'replace' => 'E185',
  ),
  36 => 
  array (
    'match' => '0CAF 0CBC',
    'replace' => 'E186',
  ),
  37 => 
  array (
    'match' => '0CB0 0CBC',
    'replace' => 'E0EB',
  ),
  38 => 
  array (
    'match' => '0CB1 0CBC',
    'replace' => 'E187',
  ),
  39 => 
  array (
    'match' => '0CB2 0CBC',
    'replace' => 'E188',
  ),
  40 => 
  array (
    'match' => '0CB3 0CBC',
    'replace' => 'E189',
  ),
  41 => 
  array (
    'match' => '0CB5 0CBC',
    'replace' => 'E18A',
  ),
  42 => 
  array (
    'match' => '0CB6 0CBC',
    'replace' => 'E18B',
  ),
  43 => 
  array (
    'match' => '0CB7 0CBC',
    'replace' => 'E18C',
  ),
  44 => 
  array (
    'match' => '0CB8 0CBC',
    'replace' => 'E18D',
  ),
  45 => 
  array (
    'match' => '0CB9 0CBC',
    'replace' => 'E18E',
  ),
  46 => 
  array (
    'match' => 'E07D 0CBC',
    'replace' => 'E117',
  ),
  47 => 
  array (
    'match' => 'E07E 0CBC',
    'replace' => 'E118',
  ),
  48 => 
  array (
    'match' => 'E0A3 0CBC',
    'replace' => 'E136',
  ),
  49 => 
  array (
    'match' => 'E0AC 0C95',
    'replace' => 'E02E',
  ),
  50 => 
  array (
    'match' => 'E0AC 0C96',
    'replace' => 'E02F',
  ),
  51 => 
  array (
    'match' => 'E0AC 0C97',
    'replace' => 'E030',
  ),
  52 => 
  array (
    'match' => 'E0AC 0C98',
    'replace' => 'E031',
  ),
  53 => 
  array (
    'match' => 'E0AC 0C99',
    'replace' => 'E032',
  ),
  54 => 
  array (
    'match' => 'E0AC 0C9A',
    'replace' => 'E033',
  ),
  55 => 
  array (
    'match' => 'E0AC 0C9B',
    'replace' => 'E034',
  ),
  56 => 
  array (
    'match' => 'E0AC 0C9C',
    'replace' => 'E035',
  ),
  57 => 
  array (
    'match' => 'E0AC 0C9D',
    'replace' => 'E036',
  ),
  58 => 
  array (
    'match' => 'E0AC 0C9E',
    'replace' => 'E037',
  ),
  59 => 
  array (
    'match' => 'E0AC 0C9F',
    'replace' => 'E038',
  ),
  60 => 
  array (
    'match' => 'E0AC 0CA0',
    'replace' => 'E039',
  ),
  61 => 
  array (
    'match' => 'E0AC 0CA1',
    'replace' => 'E03A',
  ),
  62 => 
  array (
    'match' => 'E0AC 0CA2',
    'replace' => 'E03B',
  ),
  63 => 
  array (
    'match' => 'E0AC 0CA3',
    'replace' => 'E03C',
  ),
  64 => 
  array (
    'match' => 'E0AC 0CA4',
    'replace' => 'E03D',
  ),
  65 => 
  array (
    'match' => 'E0AC 0CA5',
    'replace' => 'E03E',
  ),
  66 => 
  array (
    'match' => 'E0AC 0CA6',
    'replace' => 'E03F',
  ),
  67 => 
  array (
    'match' => 'E0AC 0CA7',
    'replace' => 'E040',
  ),
  68 => 
  array (
    'match' => 'E0AC 0CA8',
    'replace' => 'E041',
  ),
  69 => 
  array (
    'match' => 'E0AC 0CAA',
    'replace' => 'E042',
  ),
  70 => 
  array (
    'match' => 'E0AC 0CAB',
    'replace' => 'E043',
  ),
  71 => 
  array (
    'match' => 'E0AC 0CAC',
    'replace' => 'E044',
  ),
  72 => 
  array (
    'match' => 'E0AC 0CAD',
    'replace' => 'E045',
  ),
  73 => 
  array (
    'match' => 'E0AC 0CAE',
    'replace' => 'E046',
  ),
  74 => 
  array (
    'match' => 'E0AC 0CAF',
    'replace' => 'E047',
  ),
  75 => 
  array (
    'match' => 'E0AC 0CB0',
    'replace' => 'E048',
  ),
  76 => 
  array (
    'match' => 'E0AC 0CB1',
    'replace' => 'E049',
  ),
  77 => 
  array (
    'match' => 'E0AC 0CB2',
    'replace' => 'E04A',
  ),
  78 => 
  array (
    'match' => 'E0AC 0CB3',
    'replace' => 'E04B',
  ),
  79 => 
  array (
    'match' => 'E0AC 0CB5',
    'replace' => 'E04C',
  ),
  80 => 
  array (
    'match' => 'E0AC 0CB6',
    'replace' => 'E04D',
  ),
  81 => 
  array (
    'match' => 'E0AC 0CB7',
    'replace' => 'E04E',
  ),
  82 => 
  array (
    'match' => 'E0AC 0CB8',
    'replace' => 'E04F',
  ),
  83 => 
  array (
    'match' => 'E0AC 0CB9',
    'replace' => 'E050',
  ),
  84 => 
  array (
    'match' => 'E0AC E07D',
    'replace' => 'E081',
  ),
  85 => 
  array (
    'match' => 'E07D E03C',
    'replace' => 'E0A3',
  ),
  86 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E02E',
    'replace' => '\\1 E052',
  ),
  87 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E02F',
    'replace' => '\\1 E053',
  ),
  88 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E030',
    'replace' => '\\1 E054',
  ),
  89 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E031',
    'replace' => '\\1 E055',
  ),
  90 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E032',
    'replace' => '\\1 E056',
  ),
  91 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E033',
    'replace' => '\\1 E057',
  ),
  92 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E034',
    'replace' => '\\1 E058',
  ),
  93 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E035',
    'replace' => '\\1 E059',
  ),
  94 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E036',
    'replace' => '\\1 E05A',
  ),
  95 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E037',
    'replace' => '\\1 E05B',
  ),
  96 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E038',
    'replace' => '\\1 E05C',
  ),
  97 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E039',
    'replace' => '\\1 E05D',
  ),
  98 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E03A',
    'replace' => '\\1 E05E',
  ),
  99 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E03B',
    'replace' => '\\1 E05F',
  ),
  100 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E03C',
    'replace' => '\\1 E060',
  ),
  101 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E03D',
    'replace' => '\\1 E061',
  ),
  102 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E03E',
    'replace' => '\\1 E062',
  ),
  103 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E03F',
    'replace' => '\\1 E063',
  ),
  104 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E040',
    'replace' => '\\1 E064',
  ),
  105 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E041',
    'replace' => '\\1 E065',
  ),
  106 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E042',
    'replace' => '\\1 E066',
  ),
  107 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E043',
    'replace' => '\\1 E067',
  ),
  108 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E044',
    'replace' => '\\1 E068',
  ),
  109 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E045',
    'replace' => '\\1 E069',
  ),
  110 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E046',
    'replace' => '\\1 E06A',
  ),
  111 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E047',
    'replace' => '\\1 E06B',
  ),
  112 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E048',
    'replace' => '\\1 E06C',
  ),
  113 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E049',
    'replace' => '\\1 E06D',
  ),
  114 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E04A',
    'replace' => '\\1 E06E',
  ),
  115 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E04B',
    'replace' => '\\1 E06F',
  ),
  116 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E04C',
    'replace' => '\\1 E070',
  ),
  117 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E04D',
    'replace' => '\\1 E071',
  ),
  118 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E04E',
    'replace' => '\\1 E072',
  ),
  119 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E04F',
    'replace' => '\\1 E073',
  ),
  120 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E050',
    'replace' => '\\1 E074',
  ),
  121 => 
  array (
    'match' => '((E02E|E02F|E030|E031|E032|E033|E034|E035|E036|E037|E038|E039|E03A|E03B|E03C|E03D|E03E|E03F|E040|E041|E042|E043|E044|E045|E046|E047|E048|E049|E04A|E04B|E04C|E04D|E04E|E04F|E050|E081)) E081',
    'replace' => '\\1 E081',
  ),
  122 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E02E',
    'replace' => '\\1 E052',
  ),
  123 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E02F',
    'replace' => '\\1 E053',
  ),
  124 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E030',
    'replace' => '\\1 E054',
  ),
  125 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E031',
    'replace' => '\\1 E055',
  ),
  126 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E032',
    'replace' => '\\1 E056',
  ),
  127 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E033',
    'replace' => '\\1 E057',
  ),
  128 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E034',
    'replace' => '\\1 E058',
  ),
  129 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E035',
    'replace' => '\\1 E059',
  ),
  130 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E036',
    'replace' => '\\1 E05A',
  ),
  131 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E037',
    'replace' => '\\1 E05B',
  ),
  132 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E038',
    'replace' => '\\1 E05C',
  ),
  133 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E039',
    'replace' => '\\1 E05D',
  ),
  134 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E03A',
    'replace' => '\\1 E05E',
  ),
  135 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E03B',
    'replace' => '\\1 E05F',
  ),
  136 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E03C',
    'replace' => '\\1 E060',
  ),
  137 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E03D',
    'replace' => '\\1 E061',
  ),
  138 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E03E',
    'replace' => '\\1 E062',
  ),
  139 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E03F',
    'replace' => '\\1 E063',
  ),
  140 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E040',
    'replace' => '\\1 E064',
  ),
  141 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E041',
    'replace' => '\\1 E065',
  ),
  142 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E042',
    'replace' => '\\1 E066',
  ),
  143 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E043',
    'replace' => '\\1 E067',
  ),
  144 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E044',
    'replace' => '\\1 E068',
  ),
  145 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E045',
    'replace' => '\\1 E069',
  ),
  146 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E046',
    'replace' => '\\1 E06A',
  ),
  147 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E047',
    'replace' => '\\1 E06B',
  ),
  148 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E048',
    'replace' => '\\1 E06C',
  ),
  149 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E049',
    'replace' => '\\1 E06D',
  ),
  150 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04A',
    'replace' => '\\1 E06E',
  ),
  151 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04B',
    'replace' => '\\1 E06F',
  ),
  152 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04C',
    'replace' => '\\1 E070',
  ),
  153 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04D',
    'replace' => '\\1 E071',
  ),
  154 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04E',
    'replace' => '\\1 E072',
  ),
  155 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04F',
    'replace' => '\\1 E073',
  ),
  156 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E050',
    'replace' => '\\1 E074',
  ),
  157 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E081',
    'replace' => '\\1 E081',
  ),
  158 => 
  array (
    'match' => '(E07D) E02E',
    'replace' => '\\1 E052',
  ),
  159 => 
  array (
    'match' => '(E07D) E02F',
    'replace' => '\\1 E053',
  ),
  160 => 
  array (
    'match' => '(E07D) E030',
    'replace' => '\\1 E054',
  ),
  161 => 
  array (
    'match' => '(E07D) E031',
    'replace' => '\\1 E055',
  ),
  162 => 
  array (
    'match' => '(E07D) E032',
    'replace' => '\\1 E056',
  ),
  163 => 
  array (
    'match' => '(E07D) E033',
    'replace' => '\\1 E057',
  ),
  164 => 
  array (
    'match' => '(E07D) E034',
    'replace' => '\\1 E058',
  ),
  165 => 
  array (
    'match' => '(E07D) E035',
    'replace' => '\\1 E059',
  ),
  166 => 
  array (
    'match' => '(E07D) E036',
    'replace' => '\\1 E05A',
  ),
  167 => 
  array (
    'match' => '(E07D) E037',
    'replace' => '\\1 E05B',
  ),
  168 => 
  array (
    'match' => '(E07D) E038',
    'replace' => '\\1 E05C',
  ),
  169 => 
  array (
    'match' => '(E07D) E039',
    'replace' => '\\1 E05D',
  ),
  170 => 
  array (
    'match' => '(E07D) E03A',
    'replace' => '\\1 E05E',
  ),
  171 => 
  array (
    'match' => '(E07D) E03B',
    'replace' => '\\1 E05F',
  ),
  172 => 
  array (
    'match' => '(E07D) E03C',
    'replace' => '\\1 E060',
  ),
  173 => 
  array (
    'match' => '(E07D) E03D',
    'replace' => '\\1 E061',
  ),
  174 => 
  array (
    'match' => '(E07D) E03E',
    'replace' => '\\1 E062',
  ),
  175 => 
  array (
    'match' => '(E07D) E03F',
    'replace' => '\\1 E063',
  ),
  176 => 
  array (
    'match' => '(E07D) E040',
    'replace' => '\\1 E064',
  ),
  177 => 
  array (
    'match' => '(E07D) E041',
    'replace' => '\\1 E065',
  ),
  178 => 
  array (
    'match' => '(E07D) E042',
    'replace' => '\\1 E066',
  ),
  179 => 
  array (
    'match' => '(E07D) E043',
    'replace' => '\\1 E067',
  ),
  180 => 
  array (
    'match' => '(E07D) E044',
    'replace' => '\\1 E068',
  ),
  181 => 
  array (
    'match' => '(E07D) E045',
    'replace' => '\\1 E069',
  ),
  182 => 
  array (
    'match' => '(E07D) E046',
    'replace' => '\\1 E06A',
  ),
  183 => 
  array (
    'match' => '(E07D) E047',
    'replace' => '\\1 E06B',
  ),
  184 => 
  array (
    'match' => '(E07D) E048',
    'replace' => '\\1 E06C',
  ),
  185 => 
  array (
    'match' => '(E07D) E049',
    'replace' => '\\1 E06D',
  ),
  186 => 
  array (
    'match' => '(E07D) E04A',
    'replace' => '\\1 E06E',
  ),
  187 => 
  array (
    'match' => '(E07D) E04B',
    'replace' => '\\1 E06F',
  ),
  188 => 
  array (
    'match' => '(E07D) E04C',
    'replace' => '\\1 E070',
  ),
  189 => 
  array (
    'match' => '(E07D) E04D',
    'replace' => '\\1 E071',
  ),
  190 => 
  array (
    'match' => '(E07D) E04E',
    'replace' => '\\1 E072',
  ),
  191 => 
  array (
    'match' => '(E07D) E04F',
    'replace' => '\\1 E073',
  ),
  192 => 
  array (
    'match' => '(E07D) E050',
    'replace' => '\\1 E074',
  ),
  193 => 
  array (
    'match' => '(E07D) E081',
    'replace' => '\\1 E081',
  ),
  194 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E02E',
    'replace' => '\\1 E052',
  ),
  195 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E02F',
    'replace' => '\\1 E053',
  ),
  196 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E030',
    'replace' => '\\1 E054',
  ),
  197 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E031',
    'replace' => '\\1 E055',
  ),
  198 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E032',
    'replace' => '\\1 E056',
  ),
  199 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E033',
    'replace' => '\\1 E057',
  ),
  200 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E034',
    'replace' => '\\1 E058',
  ),
  201 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E035',
    'replace' => '\\1 E059',
  ),
  202 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E036',
    'replace' => '\\1 E05A',
  ),
  203 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E037',
    'replace' => '\\1 E05B',
  ),
  204 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E038',
    'replace' => '\\1 E05C',
  ),
  205 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E039',
    'replace' => '\\1 E05D',
  ),
  206 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03A',
    'replace' => '\\1 E05E',
  ),
  207 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03B',
    'replace' => '\\1 E05F',
  ),
  208 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03C',
    'replace' => '\\1 E060',
  ),
  209 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03D',
    'replace' => '\\1 E061',
  ),
  210 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03E',
    'replace' => '\\1 E062',
  ),
  211 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03F',
    'replace' => '\\1 E063',
  ),
  212 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E040',
    'replace' => '\\1 E064',
  ),
  213 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E041',
    'replace' => '\\1 E065',
  ),
  214 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E042',
    'replace' => '\\1 E066',
  ),
  215 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E043',
    'replace' => '\\1 E067',
  ),
  216 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E044',
    'replace' => '\\1 E068',
  ),
  217 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E045',
    'replace' => '\\1 E069',
  ),
  218 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E046',
    'replace' => '\\1 E06A',
  ),
  219 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E047',
    'replace' => '\\1 E06B',
  ),
  220 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E048',
    'replace' => '\\1 E06C',
  ),
  221 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E049',
    'replace' => '\\1 E06D',
  ),
  222 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04A',
    'replace' => '\\1 E06E',
  ),
  223 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04B',
    'replace' => '\\1 E06F',
  ),
  224 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04C',
    'replace' => '\\1 E070',
  ),
  225 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04D',
    'replace' => '\\1 E071',
  ),
  226 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04E',
    'replace' => '\\1 E072',
  ),
  227 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04F',
    'replace' => '\\1 E073',
  ),
  228 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E050',
    'replace' => '\\1 E074',
  ),
  229 => 
  array (
    'match' => '(E07D (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E081',
    'replace' => '\\1 E081',
  ),
  230 => 
  array (
    'match' => '(E07E) E02E',
    'replace' => '\\1 E052',
  ),
  231 => 
  array (
    'match' => '(E07E) E02F',
    'replace' => '\\1 E053',
  ),
  232 => 
  array (
    'match' => '(E07E) E030',
    'replace' => '\\1 E054',
  ),
  233 => 
  array (
    'match' => '(E07E) E031',
    'replace' => '\\1 E055',
  ),
  234 => 
  array (
    'match' => '(E07E) E032',
    'replace' => '\\1 E056',
  ),
  235 => 
  array (
    'match' => '(E07E) E033',
    'replace' => '\\1 E057',
  ),
  236 => 
  array (
    'match' => '(E07E) E034',
    'replace' => '\\1 E058',
  ),
  237 => 
  array (
    'match' => '(E07E) E035',
    'replace' => '\\1 E059',
  ),
  238 => 
  array (
    'match' => '(E07E) E036',
    'replace' => '\\1 E05A',
  ),
  239 => 
  array (
    'match' => '(E07E) E037',
    'replace' => '\\1 E05B',
  ),
  240 => 
  array (
    'match' => '(E07E) E038',
    'replace' => '\\1 E05C',
  ),
  241 => 
  array (
    'match' => '(E07E) E039',
    'replace' => '\\1 E05D',
  ),
  242 => 
  array (
    'match' => '(E07E) E03A',
    'replace' => '\\1 E05E',
  ),
  243 => 
  array (
    'match' => '(E07E) E03B',
    'replace' => '\\1 E05F',
  ),
  244 => 
  array (
    'match' => '(E07E) E03C',
    'replace' => '\\1 E060',
  ),
  245 => 
  array (
    'match' => '(E07E) E03D',
    'replace' => '\\1 E061',
  ),
  246 => 
  array (
    'match' => '(E07E) E03E',
    'replace' => '\\1 E062',
  ),
  247 => 
  array (
    'match' => '(E07E) E03F',
    'replace' => '\\1 E063',
  ),
  248 => 
  array (
    'match' => '(E07E) E040',
    'replace' => '\\1 E064',
  ),
  249 => 
  array (
    'match' => '(E07E) E041',
    'replace' => '\\1 E065',
  ),
  250 => 
  array (
    'match' => '(E07E) E042',
    'replace' => '\\1 E066',
  ),
  251 => 
  array (
    'match' => '(E07E) E043',
    'replace' => '\\1 E067',
  ),
  252 => 
  array (
    'match' => '(E07E) E044',
    'replace' => '\\1 E068',
  ),
  253 => 
  array (
    'match' => '(E07E) E045',
    'replace' => '\\1 E069',
  ),
  254 => 
  array (
    'match' => '(E07E) E046',
    'replace' => '\\1 E06A',
  ),
  255 => 
  array (
    'match' => '(E07E) E047',
    'replace' => '\\1 E06B',
  ),
  256 => 
  array (
    'match' => '(E07E) E048',
    'replace' => '\\1 E06C',
  ),
  257 => 
  array (
    'match' => '(E07E) E049',
    'replace' => '\\1 E06D',
  ),
  258 => 
  array (
    'match' => '(E07E) E04A',
    'replace' => '\\1 E06E',
  ),
  259 => 
  array (
    'match' => '(E07E) E04B',
    'replace' => '\\1 E06F',
  ),
  260 => 
  array (
    'match' => '(E07E) E04C',
    'replace' => '\\1 E070',
  ),
  261 => 
  array (
    'match' => '(E07E) E04D',
    'replace' => '\\1 E071',
  ),
  262 => 
  array (
    'match' => '(E07E) E04E',
    'replace' => '\\1 E072',
  ),
  263 => 
  array (
    'match' => '(E07E) E04F',
    'replace' => '\\1 E073',
  ),
  264 => 
  array (
    'match' => '(E07E) E050',
    'replace' => '\\1 E074',
  ),
  265 => 
  array (
    'match' => '(E07E) E081',
    'replace' => '\\1 E081',
  ),
  266 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E02E',
    'replace' => '\\1 E052',
  ),
  267 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E02F',
    'replace' => '\\1 E053',
  ),
  268 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E030',
    'replace' => '\\1 E054',
  ),
  269 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E031',
    'replace' => '\\1 E055',
  ),
  270 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E032',
    'replace' => '\\1 E056',
  ),
  271 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E033',
    'replace' => '\\1 E057',
  ),
  272 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E034',
    'replace' => '\\1 E058',
  ),
  273 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E035',
    'replace' => '\\1 E059',
  ),
  274 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E036',
    'replace' => '\\1 E05A',
  ),
  275 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E037',
    'replace' => '\\1 E05B',
  ),
  276 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E038',
    'replace' => '\\1 E05C',
  ),
  277 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E039',
    'replace' => '\\1 E05D',
  ),
  278 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03A',
    'replace' => '\\1 E05E',
  ),
  279 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03B',
    'replace' => '\\1 E05F',
  ),
  280 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03C',
    'replace' => '\\1 E060',
  ),
  281 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03D',
    'replace' => '\\1 E061',
  ),
  282 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03E',
    'replace' => '\\1 E062',
  ),
  283 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E03F',
    'replace' => '\\1 E063',
  ),
  284 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E040',
    'replace' => '\\1 E064',
  ),
  285 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E041',
    'replace' => '\\1 E065',
  ),
  286 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E042',
    'replace' => '\\1 E066',
  ),
  287 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E043',
    'replace' => '\\1 E067',
  ),
  288 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E044',
    'replace' => '\\1 E068',
  ),
  289 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E045',
    'replace' => '\\1 E069',
  ),
  290 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E046',
    'replace' => '\\1 E06A',
  ),
  291 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E047',
    'replace' => '\\1 E06B',
  ),
  292 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E048',
    'replace' => '\\1 E06C',
  ),
  293 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E049',
    'replace' => '\\1 E06D',
  ),
  294 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04A',
    'replace' => '\\1 E06E',
  ),
  295 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04B',
    'replace' => '\\1 E06F',
  ),
  296 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04C',
    'replace' => '\\1 E070',
  ),
  297 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04D',
    'replace' => '\\1 E071',
  ),
  298 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04E',
    'replace' => '\\1 E072',
  ),
  299 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E04F',
    'replace' => '\\1 E073',
  ),
  300 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E050',
    'replace' => '\\1 E074',
  ),
  301 => 
  array (
    'match' => '(E07E (0CBE|0CBF|0CC6|0CC1|0CC2|0CCC|0CCA)) E081',
    'replace' => '\\1 E081',
  ),
  302 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E03D',
    'replace' => '\\1 E076',
  ),
  303 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E041',
    'replace' => '\\1 E077',
  ),
  304 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E046',
    'replace' => '\\1 E078',
  ),
  305 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E047',
    'replace' => '\\1 E079',
  ),
  306 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E048',
    'replace' => '\\1 E07A',
  ),
  307 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04A',
    'replace' => '\\1 E07B',
  ),
  308 => 
  array (
    'match' => '((E052|E053|E054|E055|E056|E057|E058|E059|E05A|E05B|E05C|E05D|E05E|E05F|E060|E061|E062|E063|E064|E065|E066|E067|E068|E069|E06A|E06B|E06C|E06D|E06E|E06F|E070|E071|E072|E073|E074|E081)) E04E',
    'replace' => '\\1 E07C',
  ),
  309 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E030',
    'replace' => '\\1 E104',
  ),
  310 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E032',
    'replace' => '\\1 E105',
  ),
  311 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E034',
    'replace' => '\\1 E106',
  ),
  312 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E035',
    'replace' => '\\1 E107',
  ),
  313 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E036',
    'replace' => '\\1 E108',
  ),
  314 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E037',
    'replace' => '\\1 E109',
  ),
  315 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E039',
    'replace' => '\\1 E10A',
  ),
  316 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E03A',
    'replace' => '\\1 E10B',
  ),
  317 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E03B',
    'replace' => '\\1 E10C',
  ),
  318 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E03C',
    'replace' => '\\1 E10D',
  ),
  319 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E03E',
    'replace' => '\\1 E10E',
  ),
  320 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E03F',
    'replace' => '\\1 E10F',
  ),
  321 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E040',
    'replace' => '\\1 E110',
  ),
  322 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E049',
    'replace' => '\\1 E111',
  ),
  323 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E04A',
    'replace' => '\\1 E112',
  ),
  324 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E04C',
    'replace' => '\\1 E113',
  ),
  325 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E04E',
    'replace' => '\\1 E114',
  ),
  326 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E050',
    'replace' => '\\1 E115',
  ),
  327 => 
  array (
    'match' => '((E0E6|E0E7|E172|E173|E174|E175|E176|E0E8|E0E9|E177|E178|E179|E17A|E17B|E17C|E17D|E17E|E17F|E180|E181|E182|E0EA|E183|E184|E185|E186|E0EB|E187|E188|E189|E18A|E18B|E18C|E18D|E18E|E0F2|E0F3|E11B|E11C|E11D|E11E|E0F4|E0F5|E11F|E120|E121|E122|E123|E124|E125|E126|E127|E128|E0F9|E129|E12A|E12B|E12C|E0FD|E12D|E12E|E12F|E130|E131|E132|E133)) E051',
    'replace' => '\\1 E116',
  ),
  328 => 
  array (
    'match' => '((0CAA|0CAB|0CB5|E182|E0EA|E18A)) 0CC1',
    'replace' => '\\1 E007',
  ),
  329 => 
  array (
    'match' => '((0CAA|0CAB|0CB5|E182|E0EA|E18A)) 0CC2',
    'replace' => '\\1 E009',
  ),
  330 => 
  array (
    'match' => '((0CB3|E189)) 0CC1',
    'replace' => '\\1 E008',
  ),
  331 => 
  array (
    'match' => '((0CB3|E189)) 0CC2',
    'replace' => '\\1 E00A',
  ),
  332 => 
  array (
    'match' => '0C98 0CC1',
    'replace' => 'E0A5',
  ),
  333 => 
  array (
    'match' => '0C98 0CC6',
    'replace' => 'E0A6',
  ),
  334 => 
  array (
    'match' => '0C98 0CCA',
    'replace' => 'E0A7',
  ),
  335 => 
  array (
    'match' => '0C98 0CCB',
    'replace' => 'E0A8',
  ),
  336 => 
  array (
    'match' => '0C99 0CC6',
    'replace' => 'E0A9',
  ),
  337 => 
  array (
    'match' => '0C99 0CCA',
    'replace' => 'E0AA',
  ),
  338 => 
  array (
    'match' => '0C99 0CCB',
    'replace' => 'E0AB',
  ),
  339 => 
  array (
    'match' => '0C9E 0CC1',
    'replace' => 'E0D8',
  ),
  340 => 
  array (
    'match' => '0C9E 0CC2',
    'replace' => 'E0D9',
  ),
  341 => 
  array (
    'match' => '0C9E 0CC6',
    'replace' => 'E0DA',
  ),
  342 => 
  array (
    'match' => '0C9E 0CCA',
    'replace' => 'E0DB',
  ),
  343 => 
  array (
    'match' => '0CB1 0CC6',
    'replace' => 'E0AC',
  ),
  344 => 
  array (
    'match' => '0CB1 0CCA',
    'replace' => 'E0AD',
  ),
  345 => 
  array (
    'match' => '0CB1 0CCB',
    'replace' => 'E0AE',
  ),
  346 => 
  array (
    'match' => '0CAA 0CCA',
    'replace' => 'E0AF',
  ),
  347 => 
  array (
    'match' => '0CAB 0CCA',
    'replace' => 'E0B0',
  ),
  348 => 
  array (
    'match' => '0CB5 0CCA',
    'replace' => 'E0B1',
  ),
  349 => 
  array (
    'match' => '0C9D 0CC6',
    'replace' => 'E0DD',
  ),
  350 => 
  array (
    'match' => '0C9D 0CCA',
    'replace' => 'E0DE',
  ),
  351 => 
  array (
    'match' => '0C9D 0CCB',
    'replace' => 'E0DF',
  ),
  352 => 
  array (
    'match' => '0CAE 0CC6',
    'replace' => 'E0E0',
  ),
  353 => 
  array (
    'match' => '0CAE 0CCA',
    'replace' => 'E0E1',
  ),
  354 => 
  array (
    'match' => '0CAE 0CCB',
    'replace' => 'E0E2',
  ),
  355 => 
  array (
    'match' => '0CAF 0CC6',
    'replace' => 'E0E3',
  ),
  356 => 
  array (
    'match' => '0CAF 0CCA',
    'replace' => 'E0E4',
  ),
  357 => 
  array (
    'match' => '0CAF 0CCB',
    'replace' => 'E0E5',
  ),
  358 => 
  array (
    'match' => '0CB3 0CCA',
    'replace' => 'E0DC',
  ),
  359 => 
  array (
    'match' => 'E173 0CC1',
    'replace' => 'E138',
  ),
  360 => 
  array (
    'match' => 'E173 0CC6',
    'replace' => 'E139',
  ),
  361 => 
  array (
    'match' => 'E173 0CCA',
    'replace' => 'E13A',
  ),
  362 => 
  array (
    'match' => 'E174 0CC6',
    'replace' => 'E13C',
  ),
  363 => 
  array (
    'match' => 'E174 0CCA',
    'replace' => 'E13D',
  ),
  364 => 
  array (
    'match' => 'E187 0CC6',
    'replace' => 'E13F',
  ),
  365 => 
  array (
    'match' => 'E187 0CCA',
    'replace' => 'E140',
  ),
  366 => 
  array (
    'match' => 'E182 0CCA',
    'replace' => 'E142',
  ),
  367 => 
  array (
    'match' => 'E0EA 0CCA',
    'replace' => 'E0FC',
  ),
  368 => 
  array (
    'match' => 'E18A 0CCA',
    'replace' => 'E143',
  ),
  369 => 
  array (
    'match' => 'E177 0CC1',
    'replace' => 'E164',
  ),
  370 => 
  array (
    'match' => 'E177 0CC2',
    'replace' => 'E165',
  ),
  371 => 
  array (
    'match' => 'E177 0CC6',
    'replace' => 'E166',
  ),
  372 => 
  array (
    'match' => 'E177 0CCA',
    'replace' => 'E167',
  ),
  373 => 
  array (
    'match' => 'E189 0CCA',
    'replace' => 'E168',
  ),
  374 => 
  array (
    'match' => 'E0E9 0CC6',
    'replace' => 'E0F6',
  ),
  375 => 
  array (
    'match' => 'E0E9 0CCA',
    'replace' => 'E0F8',
  ),
  376 => 
  array (
    'match' => 'E185 0CC6',
    'replace' => 'E16C',
  ),
  377 => 
  array (
    'match' => 'E185 0CCA',
    'replace' => 'E16D',
  ),
  378 => 
  array (
    'match' => 'E186 0CC6',
    'replace' => 'E16F',
  ),
  379 => 
  array (
    'match' => 'E186 0CCA',
    'replace' => 'E170',
  ),
  380 => 
  array (
    'match' => '0C95 0CBF',
    'replace' => 'E082',
  ),
  381 => 
  array (
    'match' => '0C96 0CBF',
    'replace' => 'E083',
  ),
  382 => 
  array (
    'match' => '0C97 0CBF',
    'replace' => 'E084',
  ),
  383 => 
  array (
    'match' => '0C98 0CBF',
    'replace' => 'E085',
  ),
  384 => 
  array (
    'match' => '0C9A 0CBF',
    'replace' => 'E086',
  ),
  385 => 
  array (
    'match' => '0C9B 0CBF',
    'replace' => 'E087',
  ),
  386 => 
  array (
    'match' => '0C9C 0CBF',
    'replace' => 'E088',
  ),
  387 => 
  array (
    'match' => '0C9D 0CBF',
    'replace' => 'E089',
  ),
  388 => 
  array (
    'match' => '0CA0 0CBF',
    'replace' => 'E08A',
  ),
  389 => 
  array (
    'match' => '0CA1 0CBF',
    'replace' => 'E08B',
  ),
  390 => 
  array (
    'match' => '0CA2 0CBF',
    'replace' => 'E08C',
  ),
  391 => 
  array (
    'match' => '0CA3 0CBF',
    'replace' => 'E08D',
  ),
  392 => 
  array (
    'match' => '0CA4 0CBF',
    'replace' => 'E08E',
  ),
  393 => 
  array (
    'match' => '0CA5 0CBF',
    'replace' => 'E08F',
  ),
  394 => 
  array (
    'match' => '0CA6 0CBF',
    'replace' => 'E090',
  ),
  395 => 
  array (
    'match' => '0CA7 0CBF',
    'replace' => 'E091',
  ),
  396 => 
  array (
    'match' => '0CA8 0CBF',
    'replace' => 'E092',
  ),
  397 => 
  array (
    'match' => '0CAA 0CBF',
    'replace' => 'E093',
  ),
  398 => 
  array (
    'match' => '0CAB 0CBF',
    'replace' => 'E094',
  ),
  399 => 
  array (
    'match' => '0CAC 0CBF',
    'replace' => 'E095',
  ),
  400 => 
  array (
    'match' => '0CAD 0CBF',
    'replace' => 'E096',
  ),
  401 => 
  array (
    'match' => '0CAE 0CBF',
    'replace' => 'E097',
  ),
  402 => 
  array (
    'match' => '0CAF 0CBF',
    'replace' => 'E098',
  ),
  403 => 
  array (
    'match' => '0CB0 0CBF',
    'replace' => 'E099',
  ),
  404 => 
  array (
    'match' => '0CB2 0CBF',
    'replace' => 'E09A',
  ),
  405 => 
  array (
    'match' => '0CB3 0CBF',
    'replace' => 'E09B',
  ),
  406 => 
  array (
    'match' => '0CB5 0CBF',
    'replace' => 'E09C',
  ),
  407 => 
  array (
    'match' => '0CB6 0CBF',
    'replace' => 'E09D',
  ),
  408 => 
  array (
    'match' => '0CB7 0CBF',
    'replace' => 'E09E',
  ),
  409 => 
  array (
    'match' => '0CB8 0CBF',
    'replace' => 'E09F',
  ),
  410 => 
  array (
    'match' => '0CB9 0CBF',
    'replace' => 'E0A0',
  ),
  411 => 
  array (
    'match' => 'E07D 0CBF',
    'replace' => 'E0A1',
  ),
  412 => 
  array (
    'match' => 'E07E 0CBF',
    'replace' => 'E0A2',
  ),
  413 => 
  array (
    'match' => 'E0E6 0CBF',
    'replace' => 'E0F2',
  ),
  414 => 
  array (
    'match' => 'E0E7 0CBF',
    'replace' => 'E0F3',
  ),
  415 => 
  array (
    'match' => 'E172 0CBF',
    'replace' => 'E11B',
  ),
  416 => 
  array (
    'match' => 'E173 0CBF',
    'replace' => 'E11C',
  ),
  417 => 
  array (
    'match' => 'E175 0CBF',
    'replace' => 'E11D',
  ),
  418 => 
  array (
    'match' => 'E176 0CBF',
    'replace' => 'E11E',
  ),
  419 => 
  array (
    'match' => 'E0E8 0CBF',
    'replace' => 'E0F4',
  ),
  420 => 
  array (
    'match' => 'E0E9 0CBF',
    'replace' => 'E0F5',
  ),
  421 => 
  array (
    'match' => 'E179 0CBF',
    'replace' => 'E11F',
  ),
  422 => 
  array (
    'match' => 'E17A 0CBF',
    'replace' => 'E120',
  ),
  423 => 
  array (
    'match' => 'E17B 0CBF',
    'replace' => 'E121',
  ),
  424 => 
  array (
    'match' => 'E17C 0CBF',
    'replace' => 'E122',
  ),
  425 => 
  array (
    'match' => 'E17D 0CBF',
    'replace' => 'E123',
  ),
  426 => 
  array (
    'match' => 'E17E 0CBF',
    'replace' => 'E124',
  ),
  427 => 
  array (
    'match' => 'E17F 0CBF',
    'replace' => 'E125',
  ),
  428 => 
  array (
    'match' => 'E180 0CBF',
    'replace' => 'E126',
  ),
  429 => 
  array (
    'match' => 'E181 0CBF',
    'replace' => 'E127',
  ),
  430 => 
  array (
    'match' => 'E182 0CBF',
    'replace' => 'E128',
  ),
  431 => 
  array (
    'match' => 'E0EA 0CBF',
    'replace' => 'E0F9',
  ),
  432 => 
  array (
    'match' => 'E183 0CBF',
    'replace' => 'E129',
  ),
  433 => 
  array (
    'match' => 'E184 0CBF',
    'replace' => 'E12A',
  ),
  434 => 
  array (
    'match' => 'E185 0CBF',
    'replace' => 'E12B',
  ),
  435 => 
  array (
    'match' => 'E186 0CBF',
    'replace' => 'E12C',
  ),
  436 => 
  array (
    'match' => 'E0EB 0CBF',
    'replace' => 'E0FD',
  ),
  437 => 
  array (
    'match' => 'E188 0CBF',
    'replace' => 'E12D',
  ),
  438 => 
  array (
    'match' => 'E189 0CBF',
    'replace' => 'E12E',
  ),
  439 => 
  array (
    'match' => 'E18A 0CBF',
    'replace' => 'E12F',
  ),
  440 => 
  array (
    'match' => 'E18B 0CBF',
    'replace' => 'E130',
  ),
  441 => 
  array (
    'match' => 'E18C 0CBF',
    'replace' => 'E131',
  ),
  442 => 
  array (
    'match' => 'E18D 0CBF',
    'replace' => 'E132',
  ),
  443 => 
  array (
    'match' => 'E18E 0CBF',
    'replace' => 'E133',
  ),
  444 => 
  array (
    'match' => 'E117 0CBF',
    'replace' => 'E134',
  ),
  445 => 
  array (
    'match' => 'E118 0CBF',
    'replace' => 'E135',
  ),
  446 => 
  array (
    'match' => 'E136 0CBF',
    'replace' => 'E137',
  ),
  447 => 
  array (
    'match' => 'E0AD',
    'replace' => '0CCD',
  ),
  448 => 
  array (
    'match' => 'E0AC',
    'replace' => '0CCD',
  ),
  449 => 
  array (
    'match' => '0C95 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E00C \\1',
  ),
  450 => 
  array (
    'match' => '0C96 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E00D \\1',
  ),
  451 => 
  array (
    'match' => '0C97 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E00E \\1',
  ),
  452 => 
  array (
    'match' => '0C98 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E00F \\1',
  ),
  453 => 
  array (
    'match' => '0C99 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E010 \\1',
  ),
  454 => 
  array (
    'match' => '0C9A ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E011 \\1',
  ),
  455 => 
  array (
    'match' => '0C9B ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E012 \\1',
  ),
  456 => 
  array (
    'match' => '0C9C ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E013 \\1',
  ),
  457 => 
  array (
    'match' => '0C9D ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E014 \\1',
  ),
  458 => 
  array (
    'match' => '0C9F ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E015 \\1',
  ),
  459 => 
  array (
    'match' => '0CA0 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E016 \\1',
  ),
  460 => 
  array (
    'match' => '0CA1 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E017 \\1',
  ),
  461 => 
  array (
    'match' => '0CA2 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E018 \\1',
  ),
  462 => 
  array (
    'match' => '0CA3 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E019 \\1',
  ),
  463 => 
  array (
    'match' => '0CA4 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E01A \\1',
  ),
  464 => 
  array (
    'match' => '0CA5 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E01B \\1',
  ),
  465 => 
  array (
    'match' => '0CA6 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E01C \\1',
  ),
  466 => 
  array (
    'match' => '0CA7 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E01D \\1',
  ),
  467 => 
  array (
    'match' => '0CA8 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E01E \\1',
  ),
  468 => 
  array (
    'match' => '0CAA ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E01F \\1',
  ),
  469 => 
  array (
    'match' => '0CAB ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E020 \\1',
  ),
  470 => 
  array (
    'match' => '0CAC ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E021 \\1',
  ),
  471 => 
  array (
    'match' => '0CAD ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E022 \\1',
  ),
  472 => 
  array (
    'match' => '0CAE ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E023 \\1',
  ),
  473 => 
  array (
    'match' => '0CAF ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E024 \\1',
  ),
  474 => 
  array (
    'match' => '0CB0 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E025 \\1',
  ),
  475 => 
  array (
    'match' => '0CB1 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E026 \\1',
  ),
  476 => 
  array (
    'match' => '0CB2 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E027 \\1',
  ),
  477 => 
  array (
    'match' => '0CB3 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E028 \\1',
  ),
  478 => 
  array (
    'match' => '0CB5 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E029 \\1',
  ),
  479 => 
  array (
    'match' => '0CB6 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E02A \\1',
  ),
  480 => 
  array (
    'match' => '0CB7 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E02B \\1',
  ),
  481 => 
  array (
    'match' => '0CB8 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E02C \\1',
  ),
  482 => 
  array (
    'match' => '0CB9 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E02D \\1',
  ),
  483 => 
  array (
    'match' => 'E07D ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E07F \\1',
  ),
  484 => 
  array (
    'match' => 'E07E ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E080 \\1',
  ),
  485 => 
  array (
    'match' => 'E0E6 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E0EC \\1',
  ),
  486 => 
  array (
    'match' => 'E0E7 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E0ED \\1',
  ),
  487 => 
  array (
    'match' => 'E172 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E18F \\1',
  ),
  488 => 
  array (
    'match' => 'E173 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E190 \\1',
  ),
  489 => 
  array (
    'match' => 'E174 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E191 \\1',
  ),
  490 => 
  array (
    'match' => 'E175 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E192 \\1',
  ),
  491 => 
  array (
    'match' => 'E176 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E193 \\1',
  ),
  492 => 
  array (
    'match' => 'E0E8 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E0EE \\1',
  ),
  493 => 
  array (
    'match' => 'E0E9 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E0EF \\1',
  ),
  494 => 
  array (
    'match' => 'E178 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E194 \\1',
  ),
  495 => 
  array (
    'match' => 'E179 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E195 \\1',
  ),
  496 => 
  array (
    'match' => 'E17A ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E196 \\1',
  ),
  497 => 
  array (
    'match' => 'E17B ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E197 \\1',
  ),
  498 => 
  array (
    'match' => 'E17C ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E198 \\1',
  ),
  499 => 
  array (
    'match' => 'E17D ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E199 \\1',
  ),
  500 => 
  array (
    'match' => 'E17E ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E19A \\1',
  ),
  501 => 
  array (
    'match' => 'E17F ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E19B \\1',
  ),
  502 => 
  array (
    'match' => 'E180 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E19C \\1',
  ),
  503 => 
  array (
    'match' => 'E181 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E19D \\1',
  ),
  504 => 
  array (
    'match' => 'E182 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E19E \\1',
  ),
  505 => 
  array (
    'match' => 'E0EA ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E0F0 \\1',
  ),
  506 => 
  array (
    'match' => 'E183 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E19F \\1',
  ),
  507 => 
  array (
    'match' => 'E184 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A0 \\1',
  ),
  508 => 
  array (
    'match' => 'E185 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A1 \\1',
  ),
  509 => 
  array (
    'match' => 'E186 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A2 \\1',
  ),
  510 => 
  array (
    'match' => 'E0EB ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E0F1 \\1',
  ),
  511 => 
  array (
    'match' => 'E187 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A3 \\1',
  ),
  512 => 
  array (
    'match' => 'E188 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A4 \\1',
  ),
  513 => 
  array (
    'match' => 'E189 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A5 \\1',
  ),
  514 => 
  array (
    'match' => 'E18A ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A6 \\1',
  ),
  515 => 
  array (
    'match' => 'E18B ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A7 \\1',
  ),
  516 => 
  array (
    'match' => 'E18C ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A8 \\1',
  ),
  517 => 
  array (
    'match' => 'E18D ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1A9 \\1',
  ),
  518 => 
  array (
    'match' => 'E18E ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E1AA \\1',
  ),
  519 => 
  array (
    'match' => 'E117 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E119 \\1',
  ),
  520 => 
  array (
    'match' => 'E118 ((0CBE|0CC6|0CC7|E003|0CCA|0CCB|0CCC))',
    'replace' => 'E11A \\1',
  ),
  521 => 
  array (
    'match' => '0C95 (0CCD)',
    'replace' => 'E00C \\1',
  ),
  522 => 
  array (
    'match' => '0C96 (0CCD)',
    'replace' => 'E00D \\1',
  ),
  523 => 
  array (
    'match' => '0C97 (0CCD)',
    'replace' => 'E00E \\1',
  ),
  524 => 
  array (
    'match' => '0C98 (0CCD)',
    'replace' => 'E00F \\1',
  ),
  525 => 
  array (
    'match' => '0C99 (0CCD)',
    'replace' => 'E010 \\1',
  ),
  526 => 
  array (
    'match' => '0C9A (0CCD)',
    'replace' => 'E011 \\1',
  ),
  527 => 
  array (
    'match' => '0C9B (0CCD)',
    'replace' => 'E012 \\1',
  ),
  528 => 
  array (
    'match' => '0C9C (0CCD)',
    'replace' => 'E013 \\1',
  ),
  529 => 
  array (
    'match' => '0C9D (0CCD)',
    'replace' => 'E014 \\1',
  ),
  530 => 
  array (
    'match' => '0C9F (0CCD)',
    'replace' => 'E015 \\1',
  ),
  531 => 
  array (
    'match' => '0CA0 (0CCD)',
    'replace' => 'E016 \\1',
  ),
  532 => 
  array (
    'match' => '0CA1 (0CCD)',
    'replace' => 'E017 \\1',
  ),
  533 => 
  array (
    'match' => '0CA2 (0CCD)',
    'replace' => 'E018 \\1',
  ),
  534 => 
  array (
    'match' => '0CA3 (0CCD)',
    'replace' => 'E019 \\1',
  ),
  535 => 
  array (
    'match' => '0CA4 (0CCD)',
    'replace' => 'E01A \\1',
  ),
  536 => 
  array (
    'match' => '0CA5 (0CCD)',
    'replace' => 'E01B \\1',
  ),
  537 => 
  array (
    'match' => '0CA6 (0CCD)',
    'replace' => 'E01C \\1',
  ),
  538 => 
  array (
    'match' => '0CA7 (0CCD)',
    'replace' => 'E01D \\1',
  ),
  539 => 
  array (
    'match' => '0CA8 (0CCD)',
    'replace' => 'E01E \\1',
  ),
  540 => 
  array (
    'match' => '0CAA (0CCD)',
    'replace' => 'E01F \\1',
  ),
  541 => 
  array (
    'match' => '0CAB (0CCD)',
    'replace' => 'E020 \\1',
  ),
  542 => 
  array (
    'match' => '0CAC (0CCD)',
    'replace' => 'E021 \\1',
  ),
  543 => 
  array (
    'match' => '0CAD (0CCD)',
    'replace' => 'E022 \\1',
  ),
  544 => 
  array (
    'match' => '0CAE (0CCD)',
    'replace' => 'E023 \\1',
  ),
  545 => 
  array (
    'match' => '0CAF (0CCD)',
    'replace' => 'E024 \\1',
  ),
  546 => 
  array (
    'match' => '0CB0 (0CCD)',
    'replace' => 'E025 \\1',
  ),
  547 => 
  array (
    'match' => '0CB1 (0CCD)',
    'replace' => 'E026 \\1',
  ),
  548 => 
  array (
    'match' => '0CB2 (0CCD)',
    'replace' => 'E027 \\1',
  ),
  549 => 
  array (
    'match' => '0CB3 (0CCD)',
    'replace' => 'E028 \\1',
  ),
  550 => 
  array (
    'match' => '0CB5 (0CCD)',
    'replace' => 'E029 \\1',
  ),
  551 => 
  array (
    'match' => '0CB6 (0CCD)',
    'replace' => 'E02A \\1',
  ),
  552 => 
  array (
    'match' => '0CB7 (0CCD)',
    'replace' => 'E02B \\1',
  ),
  553 => 
  array (
    'match' => '0CB8 (0CCD)',
    'replace' => 'E02C \\1',
  ),
  554 => 
  array (
    'match' => '0CB9 (0CCD)',
    'replace' => 'E02D \\1',
  ),
  555 => 
  array (
    'match' => 'E07D (0CCD)',
    'replace' => 'E07F \\1',
  ),
  556 => 
  array (
    'match' => 'E07E (0CCD)',
    'replace' => 'E080 \\1',
  ),
  557 => 
  array (
    'match' => 'E0E6 (0CCD)',
    'replace' => 'E0EC \\1',
  ),
  558 => 
  array (
    'match' => 'E0E7 (0CCD)',
    'replace' => 'E0ED \\1',
  ),
  559 => 
  array (
    'match' => 'E172 (0CCD)',
    'replace' => 'E18F \\1',
  ),
  560 => 
  array (
    'match' => 'E173 (0CCD)',
    'replace' => 'E190 \\1',
  ),
  561 => 
  array (
    'match' => 'E174 (0CCD)',
    'replace' => 'E191 \\1',
  ),
  562 => 
  array (
    'match' => 'E175 (0CCD)',
    'replace' => 'E192 \\1',
  ),
  563 => 
  array (
    'match' => 'E176 (0CCD)',
    'replace' => 'E193 \\1',
  ),
  564 => 
  array (
    'match' => 'E0E8 (0CCD)',
    'replace' => 'E0EE \\1',
  ),
  565 => 
  array (
    'match' => 'E0E9 (0CCD)',
    'replace' => 'E0EF \\1',
  ),
  566 => 
  array (
    'match' => 'E178 (0CCD)',
    'replace' => 'E194 \\1',
  ),
  567 => 
  array (
    'match' => 'E179 (0CCD)',
    'replace' => 'E195 \\1',
  ),
  568 => 
  array (
    'match' => 'E17A (0CCD)',
    'replace' => 'E196 \\1',
  ),
  569 => 
  array (
    'match' => 'E17B (0CCD)',
    'replace' => 'E197 \\1',
  ),
  570 => 
  array (
    'match' => 'E17C (0CCD)',
    'replace' => 'E198 \\1',
  ),
  571 => 
  array (
    'match' => 'E17D (0CCD)',
    'replace' => 'E199 \\1',
  ),
  572 => 
  array (
    'match' => 'E17E (0CCD)',
    'replace' => 'E19A \\1',
  ),
  573 => 
  array (
    'match' => 'E17F (0CCD)',
    'replace' => 'E19B \\1',
  ),
  574 => 
  array (
    'match' => 'E180 (0CCD)',
    'replace' => 'E19C \\1',
  ),
  575 => 
  array (
    'match' => 'E181 (0CCD)',
    'replace' => 'E19D \\1',
  ),
  576 => 
  array (
    'match' => 'E182 (0CCD)',
    'replace' => 'E19E \\1',
  ),
  577 => 
  array (
    'match' => 'E0EA (0CCD)',
    'replace' => 'E0F0 \\1',
  ),
  578 => 
  array (
    'match' => 'E183 (0CCD)',
    'replace' => 'E19F \\1',
  ),
  579 => 
  array (
    'match' => 'E184 (0CCD)',
    'replace' => 'E1A0 \\1',
  ),
  580 => 
  array (
    'match' => 'E185 (0CCD)',
    'replace' => 'E1A1 \\1',
  ),
  581 => 
  array (
    'match' => 'E186 (0CCD)',
    'replace' => 'E1A2 \\1',
  ),
  582 => 
  array (
    'match' => 'E0EB (0CCD)',
    'replace' => 'E0F1 \\1',
  ),
  583 => 
  array (
    'match' => 'E187 (0CCD)',
    'replace' => 'E1A3 \\1',
  ),
  584 => 
  array (
    'match' => 'E188 (0CCD)',
    'replace' => 'E1A4 \\1',
  ),
  585 => 
  array (
    'match' => 'E189 (0CCD)',
    'replace' => 'E1A5 \\1',
  ),
  586 => 
  array (
    'match' => 'E18A (0CCD)',
    'replace' => 'E1A6 \\1',
  ),
  587 => 
  array (
    'match' => 'E18B (0CCD)',
    'replace' => 'E1A7 \\1',
  ),
  588 => 
  array (
    'match' => 'E18C (0CCD)',
    'replace' => 'E1A8 \\1',
  ),
  589 => 
  array (
    'match' => 'E18D (0CCD)',
    'replace' => 'E1A9 \\1',
  ),
  590 => 
  array (
    'match' => 'E18E (0CCD)',
    'replace' => 'E1AA \\1',
  ),
  591 => 
  array (
    'match' => 'E117 (0CCD)',
    'replace' => 'E119 \\1',
  ),
  592 => 
  array (
    'match' => 'E118 (0CCD)',
    'replace' => 'E11A \\1',
  ),
  593 => 
  array (
    'match' => '0C98 (0CBE)',
    'replace' => 'E00F \\1',
  ),
  594 => 
  array (
    'match' => '0CB1 (0CBE)',
    'replace' => 'E026 \\1',
  ),
  595 => 
  array (
    'match' => 'E173 (0CBE)',
    'replace' => 'E190 \\1',
  ),
  596 => 
  array (
    'match' => 'E187 (0CBE)',
    'replace' => 'E1A3 \\1',
  ),
  597 => 
  array (
    'match' => '((E00F|0C9E|E026|E190|E177|E1A3)) 0CBE',
    'replace' => '\\1 E004',
  ),
);
?>